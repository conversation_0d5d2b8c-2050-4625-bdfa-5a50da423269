# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ignore all .env files
.env
.env.*

# But don't ignore .env.sample
!.env.sample

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# static assets
!/public
public/nutrient-viewer/
public/lib/nutrient-viewer/
public/pdftron-webviewer/
public/lib/pdftron-webviewer/