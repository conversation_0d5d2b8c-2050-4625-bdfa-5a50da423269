'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { useState } from 'react';

import pathsConfig from '@pdfily/config/paths.config';

import { cn } from '@pdfily/ui/utils';
import { Input } from '@pdfily/ui/input';
import { Button } from '@pdfily/ui/button';

import { forgotPasswordAction } from '@/app/actions/forgot-password';

import { Message } from '@/components/form-message';
import { SubmitButton } from '@/components/submit-button';

import ForgotPasswordForm from '@/components/auth/forgot-password-form';

export default function ForgotPasswordPage({ searchParams }: { searchParams: Message }) {
  const t = useTranslations('Auth.forgotPassword');
  const [email, setEmail] = useState('');

  if ('success' in searchParams) {
    return (
      <>
        <h1
          className={cn(
            'mb-5 text-center font-onest text-[32px] font-medium leading-9 tracking-[-1px] text-[#1C1C1C]',
            'final:mb-4 final:text-[26px] final:leading-[30px]',
          )}
        >
          {t('success.title')}
        </h1>
        <p className="text-center font-onest text-[16px] font-normal leading-[22px] text-[#585858]">
          {t.rich('success.description', {
            span: (chunks) => <span className="font-medium">{chunks}</span>,
            email,
          })}
        </p>

        <form className="flex flex-col">
          <Input name="email" type="email" className={'hidden'} value={email} readOnly />

          <SubmitButton
            className="mb-2 mt-[26px] flex h-[50px] w-full items-center justify-center rounded-[10px] border border-[#F0401D] bg-transparent font-onest text-[16px] font-medium leading-5 text-[#F0401D] transition-colors hover:bg-[#F0401D]/10"
            pendingText="Sending ..."
            formAction={forgotPasswordAction}
          >
            {t('success.submitButton')}
          </SubmitButton>

          <Link href={pathsConfig.auth.signIn} className="pb-[200px]">
            <Button className="flex h-[50px] w-full items-center justify-center gap-2 rounded-[10px] border-none bg-transparent font-onest text-[16px] font-medium leading-5 text-[#1C1C1C] transition-colors hover:bg-gray-100">
              <Image
                src={'/images/auth/LeftArrow.svg'}
                alt={'arrow'}
                width={20}
                height={20}
                className={cn('object-contain')}
              />
              {t('signinLink')}
            </Button>
          </Link>
        </form>
      </>
    );
  }

  return (
    <>
      <h1
        className={cn(
          'mb-5 text-center font-onest text-[32px] font-medium leading-9 tracking-[-1px] text-[#1C1C1C]',
          'final:mb-4 final:text-[26px] final:leading-[30px]',
        )}
      >
        {t('title')}
      </h1>
      <p
        className={cn(
          'mb-8 text-center font-onest text-[16px] font-normal leading-[22px] text-[#585858]',
          'final:mb-6',
        )}
      >
        {t.rich('description', {
          br: () => <br />,
        })}
      </p>

      <ForgotPasswordForm searchParams={searchParams} onEmailChange={(e: string) => setEmail(e)} />
    </>
  );
}
