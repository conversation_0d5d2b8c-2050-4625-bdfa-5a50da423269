-- Migration to create has_subscription_status function with two flags

-- Create a function to check if a user has an active subscription
create or replace function "public"."has_subscription_status"(user_id uuid)
returns table (
  has_subscription boolean,
  is_trialing boolean
)
language sql
as $$
  select
    case
      when "status" = 'active' and "current_period_end" > now() then true
      else false
    end as has_subscription,

    case
      when "status" = 'trialing' and "current_period_end" > now() then true
      else false
    end as is_trialing
  from "public"."subscriptions"
  where "user_id" = has_subscription_status.user_id
  order by "current_period_end" desc
  limit 1;
$$;