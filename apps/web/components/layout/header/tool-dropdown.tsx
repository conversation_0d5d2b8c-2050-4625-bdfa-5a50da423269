'use client'

import Image from 'next/image'
import { ChevronDown } from 'lucide-react'
import { useLocale, useTranslations } from 'next-intl'
import { Button } from '@pdfily/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@pdfily/ui/dropdown-menu'
import { cn } from '@pdfily/ui/utils'

/**
 * ToolDropdown - Renders a dropdown menu with various PDF tools.
 */
function ToolDropdown() {
  const t = useTranslations('Header.tools');
  const locale = useLocale();

  const dropdownItems = [
    { key: 'editPdf', href: '/edit-pdf', hide: false },
    { key: 'convertPdf', href: '/convert-pdf', hide: false },
    { key: 'mergePdfs', href: '/merge-pdf', hide: false },
    { key: 'splitPdfs', href: '/split-pdf', hide: false },
    { key: 'compressPdf', href: '/compress-pdf', hide: false },
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'flex h-5 w-[86px] items-center gap-1.5 border-none',
            'font-onest text-[16px] font-medium leading-5 tracking-normal',
          )}
        >
          <Image
            src="/images/header/Tools.svg"
            alt="Tools logo"
            width={20}
            height={20}
            className="object-contain"
          />
          <span className="flex items-center gap-1">
            {t('dropdownMenu')} <ChevronDown className="h-4 w-4" />
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-56">
        {dropdownItems
        .filter((item) => !item.hide) // 👈 filter out hide items
        .map(({ key, href }) => (
          <DropdownMenuItem asChild key={key}>
            <a
              href={`/${locale}${href}`}
              onClick={(e) => e.preventDefault()} // disables navigation
              className="cursor-pointer hover:bg-accent hover:text-accent-foreground"
            >
              {t(`dropdownMenuItems.${key}`)}
            </a>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default ToolDropdown
