import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { Button } from '@pdfily/ui/button';
import { cn } from '@pdfily/ui/utils';

export default function CtaSection() {
  const t = useTranslations('HomePage.ctaSection');
  return (
    <section
      className={cn(
        'flex items-center justify-center rounded-2xl px-[110px] py-[34px]',
        'desktop:px-10',
        'laptop:py-10',
        'tablet:px-0 tablet:py-10',
        'final:px-0 final:py-10',
        'hidden tablet:block final:block',
      )}
    >
      <div
        className={cn(
          'flex w-full items-center justify-between',
          'tablet:flex-col tablet:justify-center tablet:gap-8',
          'final:flex-col final:justify-center final:gap-9',
        )}
      >
        <div
          className={cn(
            'flex flex-col items-start gap-8',
            'laptop:max-w-[360px]',
            'tablet:items-center tablet:gap-6',
            'final:items-center final:gap-[29px]',
          )}
        >
          <div
            className={cn(
              'flex flex-col items-start gap-6',
              'tablet:items-center tablet:gap-3',
              'final:items-center final:gap-3',
            )}
          >
            <h2
              className={cn(
                'font-onest text-[46px] font-medium leading-[50px] tracking-[-0.05px] text-[#1C1C1C]',
                'desktop:text-[38px] desktop:leading-10',
                'tablet:text-center tablet:text-[30px] tablet:leading-[34px]',
                'final:text-center final:text-[30px] final:leading-9',
              )}
            >
              {t.rich('title', {
                br: () => <br />,
                span: (chunks) => <span className="text-[#F0401D]">{chunks}</span>,
              })}
            </h2>
            <p
              className={cn(
                'font-onest text-lg font-normal leading-[26px] tracking-normal text-[#585858]',
                'tablet:text-center tablet:text-[16px] tablet:leading-6',
                'final:text-center final:text-[16px] final:leading-6',
              )}
            >
              {t.rich('description', {
                br: () => <br />,
              })}
            </p>
          </div>
          <Button
            className={cn(
              'h-[62px] w-[304px] rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-semibold leading-5 tracking-normal text-white hover:bg-red-600',
              'tablet:h-[50px] tablet:w-[153px]',
              'final:h-[50px] final:w-[175px]',
            )}
          >
            {t('button')}
          </Button>
        </div>

        <Image
          src={'/images/landing-page/cta-section/Banner.svg'}
          alt="PDFily Icon"
          width={300}
          height={300}
          className={cn(
            'object-contain',
            'desktop:w-[450px]',
            'laptop:w-[320px]',
            'tablet:h-[300px] tablet:w-[300px]',
            'final:h-[300px] final:w-[300px]',
          )}
        />
      </div>
    </section>
  );
}
