'use client'

import React from 'react'
import Image from 'next/image'

import { cn } from '@pdfily/ui/utils'

interface HamburgerButtonProps {
  onClick: () => void
}

/**
 * HamburgerButton - A reusable button component to toggle mobile menus.
 *
 * @param {HamburgerButtonProps} props - The component props.
 * @param {() => void} props.onClick - The click handler to open the mobile menu.
 *
 * @example
 * <HamburgerButton onClick={() => setMobileMenuOpen(true)} />
 */
const HamburgerButton = ({ onClick }: HamburgerButtonProps) => {
  return (
    <Image
      src="/images/header/Hamburger.svg"
      alt="hamburger"
      width={26}
      height={26}
      className={cn('object-contain', 'hidden small:block final:block')}
      onClick={onClick}
    />
  )
}

export default HamburgerButton
