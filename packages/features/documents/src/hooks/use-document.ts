import { useEffect, useState } from 'react';
import s3Config from '@pdfily/config/s3-config';
import { Database } from '@pdfily/supabase/database';
import { useSupabase } from '@pdfily/supabase/hooks/use-supabase';
import { getPresignedDownloadUrl } from '@pdfily/supabase/s3/get-presigned-download-url';

export type Document = Pick<
  Database['public']['Tables']['documents']['Row'],
  'id' | 'title' | 'name' | 'size' | 'page_count' | 'original_key' | 'edited_key' | 'is_edited'
> & {
  original_file_url: string | null;
  edited_file_url: string | null;
};

/**
 * Represents a structured error response object.
 *
 * @interface ErrorObject
 */
interface ErrorObject {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

interface UseDocumentResult {
  document: Document | null;
  isLoading: boolean;
  error: string | Record<string, any> | null | ErrorObject;
}

/**
 * React hook to fetch a single document directly from Supabase.
 *
 * @param documentId - ID of the document to retrieve.
 * @returns Object containing document data, loading state, and any error.
 */
export function useDocument(documentId: string | null): UseDocumentResult {
  const supabase = useSupabase();
  const [document, setDocument] = useState<Document | null>(null);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | Record<string, any> | null>(null);

  useEffect(() => {
    if (!documentId) {
      return;
    }

    const fetchDocument = async () => {
      // Attempt to fetch document details from the Supabase database.
      const { data, error } = await supabase.from('documents').select('*').eq('id', documentId).single();

      if (error) {
        setError({
          error: 'Internal Server Error',
          message: `An unexpected error occurred while fetching document details.`,
          statusCode: 500,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      if (!data) {
        setError({
          error: 'Document is not found',
          message: `The requested document does not exist or may have been deleted.`,
          statusCode: 404,
          timestamp: new Date().toISOString(),
        });
        return;
      }

      const bucket = s3Config.bucket ?? 'uploads';

      // Step 2: Retrieve file from S3 Storage
      const [originalFileUrl, editedFileUrl] = await Promise.all([
        data?.original_key ? getPresignedDownloadUrl(bucket, data.original_key) : Promise.resolve(null),
        data?.edited_key ? getPresignedDownloadUrl(bucket, data.edited_key) : Promise.resolve(null),
      ]);

      setDocument({
        id: data.id,
        title: data.title,
        name: data?.name,
        size: data?.size,
        page_count: data?.page_count,
        original_key: data.original_key,
        original_file_url: originalFileUrl,
        is_edited: data.is_edited,
        edited_key: data.edited_key,
        edited_file_url: editedFileUrl,
      });

      setLoading(false);
    };

    fetchDocument();
  }, [supabase, documentId, setDocument, setLoading, setError]);

  return { document, isLoading, error };
}
