import { UserResponse } from '@supabase/supabase-js';
import { NextResponse, URLPattern, type NextRequest } from 'next/server';
import createMiddleware from 'next-intl/middleware';
import { createMiddlewareClient } from '@pdfily/supabase/middleware-client';
import pathsConfig from '@pdfily/config/paths.config';
import { v4 as uuidv4 } from 'uuid';
import { getCorsHeaders } from './lib/cors';
import { routing } from './lib/i18n/routing';
import { defaultLocale, locales as allLocales } from './lib/i18n/locales';

/**
 * Interface for URL pattern and handler mapping.
 */
export interface UrlPatternHandler {
  pattern: URLPattern;
  handler: (req: NextRequest, res: NextResponse) => Promise<NextResponse | void>;
}

// Initialize the internationalization middleware
const handleI18nRouting = createMiddleware(routing);

// Initialize the config object
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - images - .svg, .png, .jpg, .jpeg, .gif, .webp
     * - css files
     * - locales (i18n translations)
     * - assets (static assets)
     * - api/* (API endpoints)
     */
    '/((?!_next/static|_next/image|branding|images|locales|assets|api|lib|.*.(?:svg|png|jpg|jpeg|gif|webp|css)$).*)',
    '/((?!api|trpc|_next|_vercel|.*..*).*)',
  ],
};

/**
 * Get the authenticated user using Supabase middleware client
 *
 * @param request - Next.js middleware request object
 * @param response - Next.js middleware response object
 * @returns A promise resolving to the authenticated user data
 */
const getUser = async (request: NextRequest, response: NextResponse): Promise<UserResponse> => {
  // This will refresh session if expired - required for Server Components
  // https://supabase.com/docs/guides/auth/server-side/nextjs
  const supabase = createMiddlewareClient(request, response);
  return supabase.auth.getUser();
};

/**
 * Updates the user's session based on the incoming request.
 *
 * @param {NextRequest} request - Next.js request object.
 * @param {NextResponse} response - Next.js response object.
 * @param {string} locale - The detected locale.
 * @returns {Promise<NextResponse>} - A promise resolving with the updated response.
 */
const updateSession = async (request: NextRequest, response: NextResponse, locale: string): Promise<NextResponse> => {
  try {
    // Set a unique request ID for each request
    setRequestId(request);

    // Handle patterns for specific routes
    const safePathname = (request.nextUrl.pathname ?? '/') as string;
    const handlePattern = await matchUrlPattern(safePathname, locale);

    // If a pattern handler exists, call it
    if (handlePattern) {
      const patternHandlerResponse = await handlePattern(request, response);

      // if a pattern handler returns a response, return it
      if (patternHandlerResponse) {
        return patternHandlerResponse;
      }
    }

    return response;
  } catch (error) {
    // If you are here, a Supabase client could not be created!
    // This is likely because you have not set up environment variables.
    return NextResponse.next({
      request: {
        headers: request.headers,
      },
    });
  }
};

/**
 * Helper to extract locale from the pathname or fallback to defaultLocale.
 * Handles cases where locale is not present in the URL.
 */
function extractLocale(pathname: string): string {
  const firstSegment = pathname.split('/')[1] ?? '';
  if ((allLocales as string[]).includes(firstSegment)) return firstSegment;
  return defaultLocale;
}

/**
 * Middleware function that updates the user's session based on the incoming request.
 * Handles cases where locale is not present in the URL.
 *
 * @param {NextRequest} request - The Next.js request object.
 * @returns {Promise<NextResponse>} - A promise that resolves to the updated session response.
 */
export async function middleware(request: NextRequest): Promise<NextResponse> {
  // ✅ Handle CORS preflight OPTIONS requests in Vercel
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, {
      status: 204,
      headers: getCorsHeaders(request),
    });
  }

  // 🧭 Apply the internationalization middleware first
  const response = handleI18nRouting(request);

  // Extract locale from path or fallback to default
  const pathname = request.nextUrl.pathname;
  let detectedLocale: string = extractLocale(pathname);
  if (!detectedLocale) detectedLocale = defaultLocale;

  // 🛡️ Handle session and custom redirections
  return await updateSession(request, response, detectedLocale);
}

/**
 * Retrieves an array of URL patterns and their corresponding handlers.
 * Handles both with and without locale in the path.
 *
 * @param locale - The locale to be used for the URL matching.
 * @returns {UrlPatternHandler[]} An array of objects containing URL patterns and their handlers.
 */
function getPatterns(locale: string): UrlPatternHandler[] {
  // Helper function to handle user authentication and redirection
  const handleAuthRedirect = async (req: NextRequest, res: NextResponse, redirectPath: string) => {
    const {
      data: { user },
    } = await getUser(req, res);

    // If the user is not logged in or is anonymous, redirect to the given path
    if (!user || user.is_anonymous) {
      // If locale is present, use it; otherwise, don't prefix
      const prefix = (allLocales as string[]).includes(locale) ? `/${locale}` : '';
      const redirectUrl = new URL(`${prefix}${redirectPath}`, req.nextUrl.origin).href;
      return NextResponse.redirect(redirectUrl);
    }
  };

  // Patterns for both with and without locale in the path
  const withLocale = (allLocales as string[]).includes(locale) ? `/${locale}` : '';
  return [
    {
      pattern: new URLPattern({ pathname: `${withLocale}/auth/*?` }),
      handler: async (req: NextRequest, res: NextResponse) => {
        const {
          data: { user },
        } = await getUser(req, res);

        // Allow sign-in if the user is not logged in or is an anonymous user
        if (!user || user.is_anonymous) return;

        // Redirect to the home page if the user is logged in and not anonymous
        const prefix = (allLocales as string[]).includes(locale) ? `/${locale}` : '';
        const redirectUrl = new URL(`${prefix}${pathsConfig.app.home}`, req.nextUrl.origin).href;
        return NextResponse.redirect(redirectUrl);
      },
    },
    {
      pattern: new URLPattern({ pathname: `${withLocale}/my-documents` }),
      handler: async (req: NextRequest, res: NextResponse) => {
        return await handleAuthRedirect(req, res, pathsConfig.auth.signIn);
      },
    },
    {
      pattern: new URLPattern({ pathname: `${withLocale}/settings/*?` }),
      handler: async (req: NextRequest, res: NextResponse) => {
        return await handleAuthRedirect(req, res, pathsConfig.auth.signIn);
      },
    },
  ];
}

/**
 * Matches the given URL against registered patterns and returns the corresponding handler.
 *
 * @param pathname - The URL to be matched against the registered patterns.
 * @param locale - The locale to be used for the URL matching.
 * @returns - The matching handler or null if no match is found.
 */
async function matchUrlPattern(pathname: string, locale: string) {
  const patterns = getPatterns(locale);
  const input = pathname.split('?')[0]; // Remove query parameters

  for (const { pattern, handler } of patterns) {
    const patternResult = pattern.exec(input);

    // Return the handler if the pattern matches the input URL
    if (patternResult?.pathname) {
      return handler;
    }
  }

  // Return null if no matching pattern is found
  return null;
}

/**
 * Set a unique request ID for each request.
 * @param request
 */
function setRequestId(request: NextRequest) {
  request.headers.set('x-correlation-id', uuidv4());
}
