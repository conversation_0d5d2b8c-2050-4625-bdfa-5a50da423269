{"name": "@pdfily/eslint-config", "version": "0.1.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "peerDependencies": {"eslint": ">=9.0.0", "@eslint/js": ">=9.0.0", "@next/eslint-plugin-next": ">=15.0.0", "eslint-config-prettier": ">=10.0.0", "eslint-plugin-only-warn": ">=1.0.0", "eslint-plugin-react": ">=7.0.0", "eslint-plugin-react-hooks": ">=5.0.0", "eslint-plugin-turbo": ">=2.0.0", "globals": ">=16.0.0", "typescript": ">=5.0.0", "typescript-eslint": ">=8.0.0"}, "devDependencies": {"@types/node": "^22.16.4"}}