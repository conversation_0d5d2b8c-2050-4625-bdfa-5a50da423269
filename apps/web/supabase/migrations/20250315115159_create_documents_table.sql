-- Create a new table to store documents (original and edited, single record approach)
create table if not exists public.documents (
  id uuid primary key default gen_random_uuid(), -- Unique document ID
  user_id uuid references auth.users(id) on delete cascade, -- Owner of the document (linked to users)

  -- Document details
  title text not null, -- Document title (name)
  description text, -- Optional description of the document

  original_key text not null, -- Relative storage path like 'documents/{uuid}/{timestamp}-filename.pdf'
  edited_key text, -- Relative storage path like 'documents/{uuid}/{timestamp}-filename.pdf' (optional, null if not edited)

  -- Document metadata (from original or latest file)
  name text not null, -- File name (original or latest edited file name)
  size integer, -- File size in bytes (original or latest edited file)
  type text, -- MIME type, e.g., 'pdf' | 'doc' | 'xls' | 'other'
  format text, -- File extension/format, e.g., 'pdf'
  page_count integer, -- Total number of pages in the document

  -- Document status and type
  internal_type text, -- Internal file type (dynamic types like 'PDF', 'DOCX', etc.)
  processing_status integer default 0, -- 0: PENDING, 1: READY, 2: FAILED
  is_edited boolean default false, -- Whether this is an edited document

  -- Control document visibility
  is_public boolean default false, -- Whether the document is publicly accessible (default: false)

  -- Audit fields
  created_at timestamp with time zone default now(), -- Creation timestamp
  updated_at timestamp with time zone default now(), -- Last updated timestamp
  last_modified timestamp with time zone, -- Last modified timestamp from client file metadata (optional)

  -- Constraint to ensure required fields are present
  constraint document_file_title_check check (
    char_length(title) > 0 and
    char_length(name) > 0 and
    char_length(original_key) > 0
  )
);

-- Index on title for faster searching by document name
create index document_title_idx on public.documents (title);

-- Index on user_id for quickly fetching user's documents
create index document_user_idx on public.documents (user_id);

-- Index on is_public for efficiently fetching public documents
create index document_is_public_idx on public.documents (is_public);
