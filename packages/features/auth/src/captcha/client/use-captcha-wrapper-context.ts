import { useContext, useMemo } from 'react';
import { CaptchaWrapperContext } from './captcha-wrapper-provider';

/**
 * @name useCaptchaWrapperContext
 *
 * @description A hook to get the captcha token and reset/refresh functions
 * @returns The captcha token, reset function, refresh function, and token ready status
 */
export function useCaptchaWrapperContext() {
  const context = useContext(CaptchaWrapperContext);

  if (!context) {
    throw new Error(`useCaptchaWrapperContext must be used within a CaptchaWrapperProvider`);
  }

  return useMemo(() => {
    return {
      captchaToken: context.token,
      resetCaptchaToken: context.resetCaptchaToken,
      refreshCaptchaToken: context.refreshCaptchaToken,
      isTokenReady: context.isTokenReady,
    };
  }, [context.token, context.resetCaptchaToken, context.refreshCaptchaToken, context.isTokenReady]);
}
