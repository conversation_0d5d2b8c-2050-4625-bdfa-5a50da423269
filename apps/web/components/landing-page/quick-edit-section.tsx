'use client';

import { useRef } from 'react';
import { Button } from '@pdfily/ui/button';
import { cn } from '@pdfily/ui/utils';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

interface QuickEditSectionProps {
  onFilesSelected: (files: File[]) => void;
  onError: (message: string) => void;
  maxFileSize?: number;
}

export default function QuickEditSection({ onFilesSelected, onError, maxFileSize = 50 }: QuickEditSectionProps) {
  const t = useTranslations('HomePage.quickEditionSection');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.size > maxFileSize * 1024 * 1024) {
      onError(`File size exceeds ${maxFileSize}MB.`);
      return;
    }

    onFilesSelected([file]);
  };

  return (
    <section
      className={cn(
        'flex items-center justify-center rounded-2xl bg-[#F9F9F9] px-[110px] py-[34px]',
        'desktop:px-10',
        'laptop:py-10',
        'tablet:px-0 tablet:py-10',
        'final:px-0 final:py-10',
      )}
    >
      <input ref={fileInputRef} type="file" accept=".pdf,application/pdf" onChange={handleFileChange} hidden />
      <div
        className={cn(
          'flex w-full items-center justify-between',
          'tablet:flex-col tablet:justify-center tablet:gap-8',
          'final:flex-col final:justify-center final:gap-8',
        )}
      >
        <div
          className={cn(
            'flex flex-col items-start gap-8',
            'laptop:max-w-[360px]',
            'tablet:items-center tablet:gap-6',
            'final:items-center final:gap-6',
          )}
        >
          <div
            className={cn(
              'flex flex-col items-start gap-6',
              'tablet:items-center tablet:gap-3',
              'final:items-center final:gap-3',
            )}
          >
            <h2
              className={cn(
                'font-onest text-[46px] font-medium leading-[50px] tracking-[-0.05px] text-[#1C1C1C]',
                'desktop:text-[38px] desktop:leading-10',
                'tablet:text-[30px] tablet:leading-[34px]',
                'final:text-[30px] final:leading-[34px]',
              )}
            >
              {t('title')}
            </h2>
            <p
              className={cn(
                'font-onest text-lg font-normal leading-[26px] tracking-normal text-[#585858]',
                'tablet:text-center tablet:text-[16px] tablet:leading-6',
                'final:text-center final:text-[16px] final:leading-6',
              )}
            >
              {t.rich('description', {
                br: () => <br className="tablet:hidden final:hidden" />,
              })}
            </p>
          </div>
          <Button
            className={cn(
              'h-[62px] w-[304px] rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-semibold leading-5 tracking-normal text-white hover:bg-red-600',
              'tablet:h-[50px] tablet:w-[153px]',
              'final:h-[50px] final:w-[153px]',
            )}
            onClick={handleButtonClick}
          >
            {t('button')}
          </Button>
        </div>

        <Image
          src={'/images/landing-page/quick-edit-section/Banner.svg'}
          alt="PDFily Icon"
          width={492}
          height={316}
          className={cn(
            'object-contain',
            'desktop:w-[450px]',
            'laptop:w-[320px]',
            'tablet:h-[177px] tablet:w-[276px]',
            'final:h-[177px] final:w-[276px]',
          )}
        />
      </div>
    </section>
  );
}
