import { cn } from '@pdfily/ui/utils';
import { useTranslations } from 'next-intl';

export default function WorkflowSection() {
  const t = useTranslations('HomePage.workflowSection');
  return (
    <section className="flex justify-center">
      <h2
        className={cn(
          'text-center font-onest text-[46px] font-medium leading-[50px] tracking-[-0.05px] text-[#1C1C1C]',
          'desktop:text-[30px] desktop:leading-[34px]',
          'tablet:text-[28px] tablet:leading-9',
          'final:text-[28px] final:leading-9',
        )}
      >
        {t.rich('title', {
          powerful: (chunks) => <span className="text-[#F0401D]">{chunks}</span>,
          br: () => <br />,
        })}
      </h2>
    </section>
  );
}
