import { CountryPricing, DiscountPrice } from "../../types/payment-types";

export const PRICES: Record<string, CountryPricing> = {
    us: {
        country: 'us',
        currency: 'usd',
        symbol: '$',
        subscription: {
            amount: 199.00,
            formatted: '$199.00',
            collect_tax: false
        },
        oneTime: {
            amount: 0.95,
            formatted: '$0.95',
            collect_tax: false
        }
    }
};

export const DISCOUNT_PRICES: Record<string, Record<string, DiscountPrice>> = {
    us: {
        annual: {
            amount: 89,
            currency: 'usd',
            formatted: '$89',
        }, 
        fourWeekly: {
            amount: 19.95,
            currency: 'usd',
            formatted: '$19.95',
        },
    },

};
