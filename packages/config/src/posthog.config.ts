import { z } from 'zod';
import { isEnvTrue } from '@pdfily/shared/utils';
import PostHogConfigSchema from './schemas/posthog-config.schema';

export type PostHogConfig = z.infer<typeof PostHogConfigSchema>;

const posthogConfig: PostHogConfig = PostHogConfigSchema.parse({
  apiKey: process.env.NEXT_PUBLIC_POSTHOG_API_KEY,
  host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
  isTrackingEnabled: isEnvTrue(process.env.NEXT_PUBLIC_POSTHOG_TRACKING_ENABLED),
});

export default posthogConfig;
