import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Combines and merges class names.
 *
 * This function uses `clsx` to conditionally join class names, then passes the result
 * to `twMerge` to intelligently merge Tailwind CSS classes, resolving conflicts.
 *
 * @param inputs - A list of class names (or objects/arrays of class names) to merge.
 * @returns A single string of merged class names.
 */
export function cn(...inputs: ClassValue[]): string {
    return twMerge(clsx(inputs));
}
