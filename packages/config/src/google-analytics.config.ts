import { z } from 'zod';
import { isEnvTrue } from '@pdfily/shared/utils';
import GoogleAnalyticsConfigSchema from './schemas/google-analytics-config.schema';

export type GoogleAnalyticsConfig = z.infer<typeof GoogleAnalyticsConfigSchema>;

const googleAnalyticsConfig: GoogleAnalyticsConfig = GoogleAnalyticsConfigSchema.parse({
  isTrackingEnabled: isEnvTrue(process.env.NEXT_PUBLIC_GOOGLE_TRACKING_ENABLED),
  gtmId: process.env.NEXT_PUBLIC_GTM_CONTAINER_ID,
});

export default googleAnalyticsConfig;
