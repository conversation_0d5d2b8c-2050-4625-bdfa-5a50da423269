-- Migration to create the policies to the public.documents table

-- Enable Row-Level Security on the public.documents table
alter table public.documents enable row level security;

-- Row-level security policies

-- Policy: Allow authenticated users to insert documents
create policy "Authenticated users can create a document only"
  on "public"."documents"
  as permissive
  for insert
  to public
  with check (
    (select auth.uid()) = user_id
  );

-- Policy: Allow authenticated users to view their own documents or public documents
create policy "Authenticated users can view their own or public documents"
  on "public"."documents"
  as permissive
  for select
  to authenticated
  using (
    -- Allow if the authenticated user is the owner of the document
    (select auth.uid()) = user_id
    -- Or if the document is public
    or is_public = true
  );

-- Policy: Allow users to update their own documents
create policy "Authenticated users can update their own data only"
  on "public"."documents"
  as permissive
  for update
  to public
  using (
    (select auth.uid()) = user_id
  );

-- Policy: Allow users to delete their own documents
create policy "Authenticated users can delete their own data only"
  on "public"."documents"
  as permissive
  for delete
  to public
  using (
    (select auth.uid()) = user_id
  );

-- Grant permissions to public role
grant select, insert, update, delete on public.documents to public;

