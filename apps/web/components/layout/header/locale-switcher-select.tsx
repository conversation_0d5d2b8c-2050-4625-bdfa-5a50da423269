'use client';

import { useParams } from 'next/navigation';
import { Locale } from 'next-intl';
import { ReactNode, useTransition } from 'react';
import { usePathname, useRouter } from '@/lib/i18n/navigation';
import { Select, SelectContent, SelectTrigger, SelectValue } from '@pdfily/ui/select';

type Props = {
  children: ReactNode;
  defaultValue: string;
  label: string;
};

export default function LocaleSwitcherSelect({ children, defaultValue, label }: Props) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const pathname = usePathname();
  const params = useParams();

  function onSelectChange(value: string) {
    const nextLocale = value as Locale;
    startTransition(() => {
      router.replace(
        // @ts-expect-error
        { pathname, params: params || {} },
        { locale: nextLocale },
      );
    });
  }

  return (
    <div className="relative w-20">
      <Select defaultValue={defaultValue} onValueChange={onSelectChange} disabled={isPending}>
        <SelectTrigger
          className={`w-full rounded-md bg-white px-2 py-1 text-sm transition-all duration-150 hover:bg-gray-50 focus:border-none focus:border-white disabled:cursor-not-allowed disabled:opacity-50 dark:bg-gray-800 dark:hover:bg-gray-700`}
        >
          <SelectValue placeholder="Lang" />
        </SelectTrigger>
        <SelectContent className="rounded-md border-gray-300 bg-white shadow-lg dark:border-gray-600 dark:bg-gray-800">
          {children}
        </SelectContent>
      </Select>
    </div>
  );
}
