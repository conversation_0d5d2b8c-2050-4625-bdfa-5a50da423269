'use client';

import Link from 'next/link'
import Image from 'next/image'
import { useEffect, useState, useRef } from 'react'
import { useTranslations } from 'next-intl';

import { useCaptchaWrapperContext } from '@pdfily/auth/captcha/client';
import authConfig from '@pdfily/config/auth.config';
import pathsConfig from '@pdfily/config/paths.config';
import { If } from '@pdfily/ui/if';
import { Button } from '@pdfily/ui/button';
import { Input } from '@pdfily/ui/input';
import { Label } from '@pdfily/ui/label';
import { cn } from '@pdfily/ui/utils';

import { forgotPasswordAction } from '@/app/actions/forgot-password'
import { FormMessage, Message } from '@/components/form-message'
import { SubmitButton } from '@/components/submit-button'

interface ForgotPasswordFormProps {
  searchParams: Message;
  onEmailChange: (e: string) => void;
}

export default function ForgotPasswordForm({
  searchParams,
  onEmailChange,
}: ForgotPasswordFormProps) {
  const t = useTranslations('Auth.forgotPassword');
  const { captchaToken, refreshCaptchaToken } = useCaptchaWrapperContext();
  const [email, setEmail] = useState('');
  const [hasError, setHasError] = useState(false);
  const hasRefreshedTokenRef = useRef(false);

  useEffect(() => {
    onEmailChange(email);
  }, [email]);

  useEffect(() => {
    const hasFormError = 'error' in searchParams;
    setHasError(hasFormError);

    // Automatically refresh captcha token when there's an error (loads once, not repeatedly)
    if (hasFormError && authConfig.captchaEnabled && !hasRefreshedTokenRef.current) {
      refreshCaptchaToken();
      hasRefreshedTokenRef.current = true;
    }

    // Reset refresh flag when error is cleared
    if (!hasFormError) {
      hasRefreshedTokenRef.current = false;
    }
  }, [searchParams]);

  return (
    <form className="flex flex-col">
      <div className="mb-4 space-y-2">
        <Label htmlFor="email" className="text-sm font-medium leading-5 text-[#1C1C1C]">
          {t('form.emailLabel')}
        </Label>
        <Input
          name="email"
          type="email"
          placeholder={t('form.emailPlaceholder')}
          className={cn(
            'h-[50px] w-full rounded-[10px] border border-[#E7E7E7] px-4 py-[15px]',
            'focus-visible:ring-0',
            'font-onest text-[16px] font-normal leading-5 text-[#1C1C1C] placeholder:text-[#585858]',
            'final:h-12',
          )}
          required
          onChange={(e) => setEmail(e.target.value)}
        />
      </div>

      <If condition={authConfig.captchaEnabled}>
        {/* Captcha Token */}
        <input type="hidden" name="captchaToken" value={captchaToken} />
      </If>

      <p className="text-center font-onest text-sm font-normal leading-5 text-[#585858]">
        {t.rich('form.submissionInstruction', {
          br: () => <br className="final:hidden" />,
        })}
      </p>

      {false && <FormMessage message={searchParams} />}

      <SubmitButton
        className="mb-2 mt-[26px] flex h-[50px] w-full items-center justify-center rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-medium leading-5 text-white transition-colors hover:bg-[#F0401D]/70 disabled:opacity-50"
        pendingText="Sending..."
        formAction={forgotPasswordAction}
        disabled={!email}
      >
        {t('form.submitButton')}
      </SubmitButton>

      <Link href={pathsConfig.auth.signIn}>
        <Button className="flex h-[50px] w-full items-center justify-center gap-2 rounded-[10px] border-none bg-transparent font-onest text-[16px] font-medium leading-5 text-[#1C1C1C] transition-colors hover:bg-gray-100">
          <Image
            src={'/images/auth/LeftArrow.svg'}
            alt={'arrow'}
            width={20}
            height={20}
            className={cn('object-contain')}
          />
          {t('signinLink')}
        </Button>
      </Link>
    </form>
  );
}
