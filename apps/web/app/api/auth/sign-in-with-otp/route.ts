import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';
import authConfig from '@pdfily/config/auth.config';

export async function POST(request: NextRequest) {
  try {
    // Get the email, options from the request body
    const { email, captchaToken } = await request.json();

    // Get the supabase client
    const supabase = getSupabaseServerClient();

    // Sign in with OTP
    const { data, error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        shouldCreateUser: false,
        ...(authConfig.captchaEnabled && captchaToken && {
          captchaToken,
        }),
      },
    });

    if (error) {
      return NextResponse.json({ data: { error: error.message, user: null, session: null } }, { status: 400 });
    }

    return NextResponse.json({ data: { ...data, error } }, { status: 200 });
  } catch (error: any) {
    console.log({ error });
    return NextResponse.json({ data: { error: error.message, user: null, session: null } }, { status: 400 });
  }
}
