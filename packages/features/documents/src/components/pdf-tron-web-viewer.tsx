'use client';

import type { User } from '@supabase/supabase-js';
import { useEffect } from 'react';
import usePDFTronKit from '../hooks/use-pdftronkit';
import { UseViewerKitProps } from '../types/viewer-kit.types';
import PdfTronWebViewerHeader from './pdf-tron-web-viewer-header';

interface ApryseWebViewerProps extends UseViewerKitProps {
  user: User;
}

export default function PDFTronWebViewer({ source, documentId, locale, user }: ApryseWebViewerProps) {
  const { viewer, instance } = usePDFTronKit({ source, locale });

  useEffect(() => {
    if (!instance) return;

    const modularHeader = instance.UI.getModularHeader('default-top-header');

    if (!modularHeader) return;

    const headerItems: any = modularHeader.getItems();

    if (
      !headerItems ||
      headerItems.length < 5 ||
      !Array.isArray(headerItems[0]?.items) ||
      !Array.isArray(headerItems[1].items)
    ) {
      return;
    }

    const menuItems = headerItems[0].items;
    const ribbonItems = headerItems[1].items;
    const searchToggle = headerItems[3];
    const notesToggle = headerItems[4];

    // Modify Menu item
    if (menuItems[0]) {
      menuItems[0].label = 'Menu';
      menuItems[0].img = '/images/apryse/menu.svg';
    }

    // Modify Left Panel item
    if (menuItems[2]) {
      menuItems[2].label = 'Left Panel';
      menuItems[2].img = '/images/apryse/left-panel.svg';
    }

    // Modify Hand Item
    if (menuItems[7]) {
      menuItems[7].label = 'Hand';
      menuItems[7].img = '/images/apryse/hand.svg';
    }

    // Modify Select Item
    if (menuItems[8]) {
      menuItems[8].label = 'Select';
      menuItems[8].img = '/images/apryse/select.svg';
    }

    // Modify Ribbons
    const ribbonIcons = [
      'view',
      'annotate',
      'shapes',
      'insert',
      'measure',
      'redact',
      'edit',
      'edit-text',
      'fill_sign',
      'forms',
    ];

    ribbonIcons.forEach((icon, index) => {
      if (ribbonItems[index]) {
        ribbonItems[index].img = `/images/apryse/${icon}.svg`;
      }
    });

    // Modify Search Toggle
    if (searchToggle) {
      searchToggle.label = 'Search';
      searchToggle.img = '/images/apryse/search.svg';
    }

    // Modify Comments Toggle
    if (notesToggle) {
      notesToggle.label = 'Comments';
      notesToggle.img = '/images/apryse/comments.svg';
    }

    modularHeader.setItems(headerItems);
  }, [instance]);

  return (
    <div className="h-full mt-[7px]">
      <PdfTronWebViewerHeader instance={instance} source={source} documentId={documentId} user={user} />
      <div ref={viewer} style={{ height: 'calc(100dvh - 62px)' }} />
    </div>
  );
}
