# @pdfily/documents

Feature package for document management and PDF viewing in the PDFily monorepo.

## Installation

Using pnpm:

```sh
pnpm add @pdfily/documents
```

Or with npm:

```sh
npm install @pdfily/documents
```

Or with yarn:

```sh
yarn add @pdfily/documents
```

## Usage

Import document components or hooks from this package in your other packages:

```js
import { DocumentManager } from '@pdfily/documents/components';
```

## Development

- Run `pnpm lint` to lint the code.
- Run `pnpm format` to format the code with Prettier.
- Run `pnpm typecheck` to check types.
