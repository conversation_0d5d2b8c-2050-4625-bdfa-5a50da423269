import 'server-only';

import { z } from 'zod';
import SupabaseServiceRoleKeySchema from './schemas/supabase-service-role-key.schema';

export type SupabaseServiceRoleKey = z.infer<typeof SupabaseServiceRoleKeySchema>;

/**
 * @name getServiceRoleKey
 * @description Get the Supabase Service Role Key.
 * ONLY USE IN SERVER-SIDE CODE. DO NOT EXPOSE THIS TO CLIENT-SIDE CODE.
 */
export function getServiceRoleKey(): SupabaseServiceRoleKey {
  return SupabaseServiceRoleKeySchema.parse(process.env.SUPABASE_SERVICE_ROLE_KEY);
}
