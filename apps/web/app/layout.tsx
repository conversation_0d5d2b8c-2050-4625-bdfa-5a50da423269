import { Metadata } from 'next';
import { cookies } from 'next/headers';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'next/font/google';
import { RootProviders } from '@/components/root-providers';
import ScriptLoader from '@/components/script-loader';
import { generateRootMetadata } from '@/lib/root-metadata';
import '@/styles/globals.css';
import GoogleAnalyticsTracker from '@/lib/tracking/google-tracking';

// Get the PDF editor metadata
export const metadata: Metadata = generateRootMetadata();

const onest = Onest({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-onest',
});

const geist = Geist({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-geist',
});

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const theme = await getTheme();
  const lang = await getLang();

  return (
    <html
      className={`${onest.className} ${onest.variable} ${geist.variable} overflow-x-hidden`}
      suppressHydrationWarning
    >
      <head>
        <ScriptLoader />
        <GoogleAnalyticsTracker />
      </head>
      <body className="w-full overflow-hidden bg-background text-foreground">
        <RootProviders theme={theme} lang={lang}>
          {children}
        </RootProviders>
      </body>
    </html>
  );
}

/**
 * Retrieves the current theme from cookies.
 *
 * @async
 * @function getTheme
 * @returns {Promise<'light' | 'dark' | 'system'>} A promise that resolves to the theme value.
 */
async function getTheme(): Promise<'light' | 'dark' | 'system'> {
  const cookiesStore = await cookies();
  return cookiesStore.get('theme')?.value as 'light' | 'dark' | 'system';
}

/**
 * Retrieves the default browser language.
 *
 * Uses the `navigator.language` to get the primary language or falls back to `en` if not available.
 * Supports modern browsers by checking `navigator.languages` first.
 *
 * @returns {string} The default browser language.
 *
 * @example
 * const lang = await getLang();
 * console.log(lang); // "en-US" or "fr" or "de" etc.
 */
export async function getLang(): Promise<string> {
  try {
    if (typeof navigator !== 'undefined') {
      // Check the preferred languages first, then fall back to the primary language
      const preferredLanguage = navigator.languages?.[0] || navigator.language;
      return preferredLanguage || 'en';
    }
    return 'en'; // Fallback language if navigator is not available
  } catch (error) {
    return 'en';
  }
}
