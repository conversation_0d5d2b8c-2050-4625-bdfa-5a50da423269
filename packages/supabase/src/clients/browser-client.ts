import { createBrowserClient } from '@supabase/ssr';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../database.types';
import { getSupabaseClientKeys } from '../get-supabase-client-keys';

/**
 * Creates a Supabase client instance for use in the browser.
 *
 * @returns Supabase client instance configured for browser usage
 */
export function getSupabaseBrowserClient<GenericSchema = Database>(): SupabaseClient<GenericSchema> {
  // Get Supabase client keys from environment or secure storage
  const keys = getSupabaseClientKeys();

  const supabaseUrl: string = keys.url; // Supabase project URL
  const supabaseAnonKey: string = keys.anonKey; // Supabase public Anon API key

  // Return a new Supabase client configured for the browser
  return createBrowserClient<GenericSchema>(supabaseUrl, supabaseAnonKey);
}