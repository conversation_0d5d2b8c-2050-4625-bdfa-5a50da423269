'use client';

import type { User } from '@supabase/supabase-js';
import { cn } from '@pdfily/ui/utils';
import { useAuth } from '@/components/session-provider';
import DocumentManager from '@/components/my-documents/document-manager';

export default function MyDocumentsLayout(props: React.PropsWithChildren<{ user: User | null }>) {
  const { user = props.user } = useAuth();

  return (
    <div className={cn('flex w-full max-w-[1440px] flex-col items-stretch')}>
      {props.children}

      {user?.id && <DocumentManager userId={user.id} />}

      <div
        className={cn(
          'ml-[-100%] mr-[-100%] flex flex-col bg-[#F9F9F9] pb-[60px] pl-[100%] pr-[100%] pt-7',
          'final:pb-10 final:pt-4',
        )}
      >
        {/* <ToolsSection IsMembersAreaPage /> */}
      </div>
    </div>
  );
}
