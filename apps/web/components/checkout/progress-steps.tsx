import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { cn } from '@pdfily/ui/utils';

interface ProgressStepsProps {
  currentStep: number
}

const steps = [
  { id: 1, name: 'documentEdited' },
  { id: 2, name: 'planSelected' },
  { id: 3, name: 'payment' },
  { id: 4, name: 'completed' }
];

export default function ProgressSteps({ currentStep }: ProgressStepsProps) {
  const t = useTranslations('Checkout.stepsProgress');

  return (
    <div className={cn('flex items-center gap-2', 'desktop:hidden', 'final:hidden')}>
      {steps.map((step) => {
        const isCompleted = currentStep > step.id
        const isActive = currentStep === step.id

        return (
          <div key={step.id} className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              {isCompleted ? (
                <Image
                  src={'/images/checkout/CircleCheck.svg'}
                  alt={'Check'}
                  width={22}
                  height={22}
                  className={cn('object-contain')}
                />
              ) : (
                <span
                  className={cn(
                    'flex h-[22px] w-[22px] items-center justify-center rounded-full',
                    'font-onest text-sm font-normal leading-[22px] tracking-normal',
                    isActive
                      ? 'border-[0.5px] border-[#1FC7941A] bg-[#E9F9F4] text-[#1FC794]'
                      : 'bg-[#F1F1F1] text-[#585858]',
                  )}
                >
                  {step.id}
                </span>
              )}
              <p
                className={cn(
                  'font-onest text-[16px] font-normal leading-5 tracking-normal',
                  isCompleted || isActive ? 'text-[#1C1C1C]' : 'text-[#585858]',
                )}
              >
                {t(step.name)}
              </p>
            </div>
            {step.id !== steps.length && (
              <div className={cn('h-px w-[38px]', isCompleted ? 'bg-[#D5F4EA]' : 'bg-[#E7E7E7]')}></div>
            )}
          </div>
        )
      })}
    </div>
  )
}
