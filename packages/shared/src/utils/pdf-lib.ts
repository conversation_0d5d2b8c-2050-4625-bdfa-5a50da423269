import { PDFDocument } from 'pdf-lib';

/**
 * Get the number of pages in a PDF from a File, Buffer, ArrayBuffer, or Uint8Array.
 *
 * @param {File | Buffer | ArrayBuffer | Uint8Array} input - The PDF input data.
 * @returns {Promise<number>} - The number of pages in the PDF.
 */
export async function getPdfPageCount(
  input: File | Buffer | ArrayBuffer | Uint8Array
): Promise<number> {
  try {
    // Convert File to ArrayBuffer if needed
    const arrayBuffer = input instanceof File ? await input.arrayBuffer() : input;

    const pdfDoc = await PDFDocument.load(arrayBuffer);
    const pageCount = pdfDoc.getPageCount();

    if (isNaN(pageCount) || pageCount < 1) {
      console.warn(`⚠️ Invalid page count retrieved from PDF: ${pageCount}`);
      return 0;
    }

    return pageCount;
  } catch (error: unknown) {
    console.error(`❌ Error reading PDF: ${(error as Error).message}`);
    return 0;
  }
}
