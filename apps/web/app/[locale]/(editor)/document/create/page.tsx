'use client';

import { useState, useEffect } from 'react';
import { useLocale } from 'next-intl';
import { PDFViewerRendererContainer } from '@pdfily/documents/components';
import { useDocument } from '@pdfily/documents/hooks/use-document';
import { useLastEditFileId } from '@pdfily/documents/hooks/use-last-edit-file-id';
import { useAuth } from '@/components/session-provider';
import { useRouter } from '@/lib/i18n/navigation';

/**
 * Document create page
 */
export default function Page() {
  const router = useRouter();
  const locale = useLocale();
  const { user, isLoading: isAuthLoading } = useAuth();
  const { lastEditFileId: documentId } = useLastEditFileId();
  const [source, setSource] = useState<string | null>(null);
  const { document, error, isLoading: isDocumentLoading } = useDocument(documentId);

  useEffect(() => {
    if (!user || !documentId) return;

    if (error) {
      router.push('/');
      return;
    }

    if (document) {
      const newSource = document.edited_file_url ?? document.original_file_url;
      setSource(newSource);
    }
  }, [documentId, document, error, router, source, user]);

  // Show a skeleton loader if authentication or document is still loading
  if (isAuthLoading || isDocumentLoading || !source) {
    return (
      <div className="flex items-center justify-center h-screen flex-col gap-y-2">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4">Preparing your file</p>
      </div>
    );
  }

  // Show a message if no user is authenticated
  if (!user) {
    return (
      <div className="flex w-full flex-col">
        <div className="flex items-center justify-center h-screen flex-col gap-y-2">
          <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="ml-4">Loading document viewer...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col">
      <PDFViewerRendererContainer source={source} documentId={documentId} locale={locale} user={user} />
    </div>
  );
}
