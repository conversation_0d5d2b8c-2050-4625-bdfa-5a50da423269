'use client';

import type { User } from '@supabase/supabase-js';

import { useUser } from '@pdfily/supabase/hooks/use-user';

export default function DashboardPage(props: { user: User | null }) {
  const queryFn = useUser(props.user);
  const user = queryFn.data ?? props.user ?? null;

  return (
    <div className="flex-1 w-full flex flex-col gap-12">
      <div className="w-full">
        <div className="bg-accent text-sm p-3 px-5 rounded-md text-foreground flex gap-3 items-center">
          This is a dashboard page that you can only see as an authenticated user
        </div>
      </div>
      <div className="flex flex-col gap-2 items-start">
        <h2 className="font-bold text-2xl mb-4">Your user details</h2>
        <pre className="text-xs font-mono p-4 rounded border max-h-50 overflow-auto">
           {JSON.stringify(user, null, 2)}
         </pre>
      </div>
  </div>
  );
}

