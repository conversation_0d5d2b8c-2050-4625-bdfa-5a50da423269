import { z } from 'zod';
import SupabaseClientSchema from './schemas/supabase-client.schema';

export type SupabaseClient = z.infer<typeof SupabaseClientSchema>;

/**
 * Returns and validates the Supabase client keys from the environment.
 */
export function getSupabaseClientKeys(): SupabaseClient {
  return SupabaseClientSchema.parse({
    url: process.env.NEXT_PUBLIC_SUPABASE_URL,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  });
}
