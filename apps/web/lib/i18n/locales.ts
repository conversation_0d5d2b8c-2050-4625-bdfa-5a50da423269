/**
 * Valid locale prefix strategies
 */
export type LocalePrefix = 'always' | 'as-needed' | 'never';

/**
 * The single source-of-truth for every supported locale.
 * Add new items here and the whole app will immediately know about them.
 */
export enum Locale {
  En = 'en',
  Es = 'es',
  It = 'it',
  Fr = 'fr',
  Pt = 'pt'
}

/**
 * Get locale prefix strategy from environment variable.
 * Defaults to 'never' if the value is invalid or undefined.
 */
export const getLocalePrefix = (): LocalePrefix => {
  const prefix = process.env.NEXT_PUBLIC_LOCALE_PREFIX;

  return (['always', 'as-needed', 'never'] as const).includes(prefix as LocalePrefix)
    ? (prefix as LocalePrefix)
    : 'never';
};

/** Get default locale from environment variable with fallback */
export const getDefaultLocale = (): Locale => {
  // Always return English as the default locale
  return Locale.En;
};

/** The locale that will be used when no match is found. */
export const defaultLocale: Locale = getDefaultLocale();

/** All available locales */
export const locales: string[] = Object.values(Locale);
