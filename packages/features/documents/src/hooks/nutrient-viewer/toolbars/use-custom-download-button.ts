import type { User } from '@supabase/supabase-js';
import { Instance, ToolbarItem } from '@nutrient-sdk/viewer';
import { useEffect, useMemo } from 'react';
import Swal from 'sweetalert2';

import {
  UploadResponse,
  UploadSuccessResponse,
  useUploadFileByDocument,
} from '@pdfily/supabase/hooks/use-upload-file-by-document';
import { useDownloadLogic } from '../../use-check-subscription-and-download';

/**
 * Hook to memoize and return a custom save/download toolbar button.
 *
 * @param instance - Nutrient viewer instance.
 * @param documentId - The ID of the document being edited.
 * @param uploadFile - A function to upload the exported PDF.
 * @returns Memoized custom save button for Nutrient Viewer toolbar.
 */
export function useCustomDownloadButton(
  instance: Instance | null,
  documentId: string | null | undefined,
  user: User,
): ToolbarItem {
  const { handleDownload } = useDownloadLogic(user);

  /**
   * Edit and save file to API (memoized)
   */
  const uploadFile = useUploadFileByDocument({
    onSuccess: () => {},
    onError: (error) => {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error?.message || 'Failed to edit and save document.',
      });
    },
  });

  useEffect(() => {
    instance?.setToolbarItems((items) =>
      items.map((item) => {
        if (item.type === 'print') {
          return {
            ...item,
            type: 'custom',
            className: 'PSPDFKit-Toolbar-Button-Print',
            onPress: async () => {
              if (!instance || !documentId || !user) return;

              try {
                const arrayBuffer = await instance.exportPDF({ incremental: true });
                const blob = new Blob([arrayBuffer], { type: 'application/pdf' });

                const res: UploadResponse = await uploadFile(blob, documentId);
                if ('error' in res) throw 'error';

                // Download file
                await handleDownload(documentId, res as UploadSuccessResponse);
              } catch (error) {
                Swal.fire({
                  icon: 'error',
                  title: 'Error',
                  text: 'Failed to upload edited PDF.',
                });
              }
            },
          };
        }
        return item;
      }),
    );
  }, [instance]);

  return useMemo(() => {
    return {
      id: 'download-pdf',
      type: 'custom',
      title: 'Done',
      className: 'download-pdf',
      onPress: async () => {
        if (!instance || !documentId || !user) return;

        try {
          const arrayBuffer = await instance.exportPDF({ incremental: true });
          const blob = new Blob([arrayBuffer], { type: 'application/pdf' });

          const res: UploadResponse = await uploadFile(blob, documentId);
          if ('error' in res) throw 'error';

          // Download file
          await handleDownload(documentId, res as UploadSuccessResponse);
        } catch (error) {
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to upload edited PDF.',
          });
        }
      },
    };
  }, [instance, documentId, uploadFile]);
}
