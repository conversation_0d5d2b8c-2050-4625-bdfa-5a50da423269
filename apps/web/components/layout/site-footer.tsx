import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { cn } from '@pdfily/ui/utils';
import { Link } from '@/lib/i18n/navigation';
import LocaleSwitcher from '@/components/layout/header/locale-switcher';

const items = [
  {
    name: 'mastercard',
    icon: '/images/footer/MasterCard.svg',
    icon_mobile: '/images/footer/MasterCard_mobile.svg',
  },
  {
    name: 'visa',
    icon: '/images/footer/Visa.svg',
    icon_mobile: '/images/footer/Visa_mobile.svg',
  },
  {
    name: 'paypal',
    icon: '/images/footer/PayPal.svg',
    icon_mobile: '/images/footer/PayPal_mobile.svg',
  },
  {
    name: 'amex',
    icon: '/images/footer/AmEx.png',
    icon_mobile: '/images/footer/AmEx_mobile.svg',
  },
  {
    name: 'master-secure',
    icon: '/images/footer/MasterSecure.svg',
    icon_mobile: '/images/footer/MasterSecure_mobile.svg',
  },
  {
    name: 'visa-secure',
    icon: '/images/footer/VisaSecure.svg',
    icon_mobile: '/images/footer/VisaSecure_mobile.svg',
  },
];

export default function SiteFooter() {
  const t = useTranslations('Footer');

  return (
    <footer className="flex w-full justify-center">
      <div
        className={cn(
          'flex w-full max-w-[1440px] flex-col items-stretch gap-8 px-20 py-[60px]',
          'mac:px-4',
          'tablet:gap-5 tablet:px-4 tablet:py-10',
          'final:gap-5 final:px-4 final:py-10',
        )}
      >
        <div
          className={cn(
            'flex items-center justify-between',
            'tablet:flex-col-reverse tablet:items-start tablet:gap-5',
            'final:flex-col-reverse final:items-start final:gap-5',
          )}
        >
          <p
            className={cn(
              'font-onest text-lg font-normal leading-5 tracking-normal text-[#585858]',
              'tablet:text-[16px] tablet:leading-5',
              'final:text-[16px] final:leading-5',
            )}
          >
            {t('copyright')}
          </p>
          <div className={cn('flex items-center gap-10', 'tablet:gap-5', 'final:gap-5')}>
            <LocaleSwitcher />

            <Link
              href="/privacy-policy"
              className={cn(
                'font-onest text-lg font-medium leading-5 tracking-normal text-[#1C1C1C] hover:text-gray-900',
                'tablet:text-[16px] tablet:leading-5',
                'final:text-[16px] final:leading-5',
              )}
            >
              {t('privacy')}
            </Link>
            <Link
              href="/terms-conditions"
              className={cn(
                'font-onest text-lg font-medium leading-5 tracking-normal text-[#1C1C1C] hover:text-gray-900',
                'tablet:text-[16px] tablet:leading-5',
                'final:text-[16px] final:leading-5',
              )}
            >
              {t('terms')}
            </Link>
          </div>
        </div>

        <div className="h-px bg-[#E8E8E8]"></div>

        <div className={cn('flex items-center justify-between', 'tablet:hidden', 'final:hidden')}>
          {items.map((item, index) => (
            <Image
              key={index}
              src={item.icon}
              alt={item.name}
              width={116}
              height={28}
              className={cn('object-contain')}
            />
          ))}
        </div>

        <div className={cn('flex items-center justify-between', 'tablet:flex', 'hidden final:flex')}>
          {items.map((item, index) => (
            <Image
              key={index}
              src={item.icon_mobile}
              alt={item.name}
              width={58}
              height={14}
              className={cn('object-contain', 'h-[14px] w-auto')}
            />
          ))}
        </div>
        {/* <ThemeSwitcher /> */}
      </div>
    </footer>
  );
}
