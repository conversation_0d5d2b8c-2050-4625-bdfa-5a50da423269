// utils/fetch-svg-icon.ts

/**
 * Fetches an SVG icon as a string from the specified path.
 *
 * @param path - The URL or relative path to the SVG file.
 * @returns A promise that resolves to the SVG content as a string, or an empty string if the fetch fails.
 *
 * @example
 * const svg = await fetchSvgIcon('/icons/my-icon.svg');
 * element.innerHTML = svg;
 */
export async function fetchSvgIcon(path: string): Promise<string> {
  try {
    const response = await fetch(path);
    return await response.text();
  } catch (error) {
    console.error(`❌ Error fetching SVG icon at ${path}:`, error);
    return '';
  }
}
