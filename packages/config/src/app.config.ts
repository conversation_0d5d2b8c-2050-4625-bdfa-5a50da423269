import { z } from 'zod';
import { isEnvTrue } from '@pdfily/shared/utils';
import AppConfigSchema from './schemas/app-config.schema';

export enum PDF_EDITOR {
  PDFTRON = 'pdftron',
  NUTRIENT = 'nutrient',
}

export type AppConfig = z.infer<typeof AppConfigSchema>;

// Parse and validate the configuration at startup.
const appConfig: AppConfig = AppConfigSchema.parse({
  title: process.env.NEXT_PUBLIC_SITE_TITLE,
  description: process.env.NEXT_PUBLIC_SITE_DESCRIPTION,
  url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  theme: process.env.NEXT_PUBLIC_DEFAULT_THEME_MODE ?? 'light',
  lang: process.env.NEXT_PUBLIC_DEFAULT_LANG ?? 'en',
  pdfEditor: process.env.NEXT_PUBLIC_PDF_EDITOR ?? PDF_EDITOR.PDFTRON, // add this if needed
  pdfViewerLicenseKey: process.env.NEXT_PUBLIC_PDF_VIEWER_LICENSE_KEY,
  nutrientLicenceKey: process.env.NEXT_PUBLIC_PDF_NUTRIENT_LICENSE_KEY,
  isNutrientLicensedEnabled: isEnvTrue(process.env.NEXT_PUBLIC_PDF_NUTRIENT_LICENSED_ENABLED),
  sendGridApiKey: process.env.SMTP_PASS,
  sendGridSenderEmail: process.env.SMTP_FROM_EMAIL,
  sendGridSupportReceiverEmail: process.env.SENDGRID_SUPPORT_RECEIVER_EMAIL,
  sendGridContactFormTemplateId: process.env.SENDGRID_CONTACT_TEMPLATE_ID,
});

export default appConfig;
