'use client';

import { useState, useEffect, useCallback } from 'react';
import { useLocale } from 'next-intl';
import { useDocument } from '@pdfily/documents/hooks/use-document';
import { PDFViewerRendererContainer } from '@pdfily/documents/components';
import { useRouter } from '@/lib/i18n/navigation';
import { useAuth } from '@/components/session-provider';

interface PageProps {
  params: Promise<{ id: string }>;
}

/**
 * Document edit page
 */
export default function Page({ params }: PageProps) {
  const router = useRouter();
  const locale = useLocale();
  const { user, isLoading: isAuthLoading } = useAuth();
  const [source, setSource] = useState<string | null>(null);
  const [documentId, setDocumentId] = useState<string>('');

  /**
   * Unwraps the params and sets the document ID.
   */
  const unwrapParams = useCallback(async () => {
    try {
      const id = (await params).id;
      setDocumentId(id);
    } catch (error) {
      console.error(`Error unwrapping params for document ID`, error);
      router.push('/');
    }
  }, [params, router]);

  useEffect(() => {
    unwrapParams();
  }, [unwrapParams]);

  // Fetch document data using the retrieved document ID
  const { document, error, isLoading: isDocumentLoading } = useDocument(documentId);

  useEffect(() => {
    if (document) {
      setSource(document.edited_file_url ?? document.original_file_url);
    }

    if (error) {
      // Redirect to homepage if an error occurs while fetching the document
      router.push('/');
    }
  }, [document, error, router]);

  // Show a skeleton loader if authentication or document is still loading
  if (isAuthLoading || isDocumentLoading || !source) {
    return (
      <div className="flex items-center justify-center h-screen flex-col gap-y-2">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4">Preparing your file</p>
      </div>
    );
  }

  // Show a message if no user is authenticated
  if (!user) {
    return (
      <div className="flex items-center justify-center h-screen flex-col gap-y-2">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4">Preparing your file</p>
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col gap-4 p-4">
      <PDFViewerRendererContainer source={source} documentId={documentId} locale={locale} user={user!} />
    </div>
  );
}
