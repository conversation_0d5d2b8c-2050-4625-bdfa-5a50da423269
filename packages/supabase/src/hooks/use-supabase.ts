import { useMemo } from 'react';
import { getSupabaseBrowserClient } from '../clients/browser-client';
import { Database } from '../database.types';

/**
 * @name useSupabase
 * @description A React hook to initialize and memoize the Supabase browser client
 *
 * @returns Supabase client instance
 */
export function useSupabase<Db = Database>(): ReturnType<typeof getSupabaseBrowserClient<Db>> {
  return useMemo(() => getSupabaseBrowserClient<Db>(), []);
}
