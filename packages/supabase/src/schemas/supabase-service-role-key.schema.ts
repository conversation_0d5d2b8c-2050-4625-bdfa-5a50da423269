import { z } from "zod";

// Define the message to display when the Supabase Service Role Key is not provided
const message = 'Invalid Supabase Service Role Key. Please add the environment variable SUPABASE_SERVICE_ROLE_KEY.';

/**
 * Defines the shape of your Supabase Service Role Key and validates
 */
export const SupabaseServiceRoleKeySchema = z
  .string({ required_error: message})
  .min(1, { message: message });

export default SupabaseServiceRoleKeySchema;
