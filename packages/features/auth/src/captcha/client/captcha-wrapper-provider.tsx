'use client';

import { createContext, useCallback, useRef, useState } from 'react';
import { TurnstileInstance } from '@marsidev/react-turnstile';

/**
 * Represents the context for captcha authentication.
 */
interface CaptchaWrapperContextType {
  token: string;
  setToken: (token: string) => void;
  instance: TurnstileInstance | null;
  setInstance: (ref: TurnstileInstance) => void;
  resetCaptchaToken: () => void;
  refreshCaptchaToken: () => Promise<string>;
  isTokenReady: boolean;
}

/**
 * @name CaptchaWrapperContext
 *
 * @description
 * Creates a React context to manage the captcha token and instance.
 * It provides functions to update the captcha token, set the instance,
 * reset the token, and refresh the token.
 *
 * @defaultValue
 * {
 *   token: '',
 *   instance: null,
 *   setToken: () => '',
 *   setInstance: () => {},
 *   resetCaptchaToken: () => {},
 *   refreshCaptchaToken: () => Promise.resolve(''),
 *   isTokenReady: false
 * }
 */
export const CaptchaWrapperContext = createContext<CaptchaWrapperContextType>({
  token: '',
  instance: null,
  setToken: (_: string) => {
    // do nothing
    return '';
  },
  setInstance: () => {
    // do nothing
  },
  resetCaptchaToken: () => {
    // do nothing
  },
  refreshCaptchaToken: () => Promise.resolve(''),
  isTokenReady: false,
});

/**
 * @name CaptchaWrapperProvider
 *
 * @description
 * React provider component to manage the captcha token and instance state.
 *
 * @param {object} props - The provider properties.
 * @param {React.ReactNode} props.children - The child components to be wrapped by the provider.
 *
 * @example
 * <CaptchaWrapperProvider>
 *   <YourComponent />
 * </CaptchaWrapperProvider>
 */
export function CaptchaWrapperProvider({ children }: { children: React.ReactNode }) {
  const [token, setToken] = useState<string>('');
  const instanceRef = useRef<TurnstileInstance | null>(null);
  const resolveRef = useRef<((token: string) => void) | null>(null);

  const setInstance = useCallback((ref: TurnstileInstance) => {
    instanceRef.current = ref;
  }, []);

  // Reset captcha token (clears the current token)
  const resetCaptchaToken = useCallback(() => {
    if (instanceRef.current) {
      instanceRef.current.reset();
      setToken(''); // Clear the token immediately
    }
  }, []);

  // Refresh captcha token (gets a new token and returns it)
  const refreshCaptchaToken = useCallback(() => {
    return new Promise<string>((resolve, reject) => {
      if (!instanceRef.current) {
        reject(new Error('Turnstile instance not available'));
        return;
      }

      // Store the resolve function to be called when the token is received
      resolveRef.current = (newToken: string) => {
        setToken(newToken);
        resolve(newToken);
      };

      // Reset and execute to get a new token
      instanceRef.current.reset();
      instanceRef.current.execute();
    });
  }, []);

  // Check if token is ready
  const isTokenReady = Boolean(token);

  const contextValue: CaptchaWrapperContextType = {
    token,
    setToken: (newToken: string) => {
      setToken(newToken);
      // If there's a pending promise, resolve it with the new token
      if (resolveRef.current) {
        resolveRef.current(newToken);
        resolveRef.current = null; // Clear the resolver
      }
    },
    instance: instanceRef.current,
    setInstance,
    resetCaptchaToken,
    refreshCaptchaToken,
    isTokenReady,
  };

  return (
    <CaptchaWrapperContext.Provider value={contextValue}>
      {children}
    </CaptchaWrapperContext.Provider>
  );
}
