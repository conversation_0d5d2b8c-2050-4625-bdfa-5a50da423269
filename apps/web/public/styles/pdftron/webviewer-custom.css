[data-element='default-top-header'] {
  background-color: #fafafa !important;
  box-shadow:
    0px 1px 0px 0px #e7e7e7,
    0px 4px 25px 0px #00000017 !important;
  padding: 0 !important;
  outline: none !important;
}

[data-element='groupedLeftHeaderButtons'] {
  gap: 0 !important;
}

.ModularHeaderItems {
  gap: 0 !important;
}

[data-element='zoom-toggle-button'] {
  min-width: unset !important;
  margin: 0 12px 0 4px !important;
  padding: 0 !important;
}

.ToggleElementButton {
  width: auto !important;
}

.ToggleElementButton.is-mobile {
  display: flex !important;
  align-items: center !important;
}

[data-element='default-ribbon-group'] {
  display: flex !important;
  align-items: center !important;
  flex-grow: 1 !important;
}

.Dropdown__item-object .Icon {
  display: none;
}

.Dropdown.FlexDropdown.row {
  border: 0.5px solid #e7e7e7;
  outline: none !important;
}

.App.is-web-component:not(.is-in-desktop-only-mode) .Dropdown {
  height: 52px;
}

.Dropdown__items {
  top: 58px !important;
  box-shadow: 2px 8px 20px 0px #84899233;
  box-shadow: 1px 2px 4px 1px #9b9ea329;
  padding: 2px;
}

.Dropdown__item {
  border-radius: 4px;
}

.Dropdown__item.selected.active {
  background: #fef2ef;
  color: #585858;
  outline: none !important;
}

.Divider {
  margin: 8px 0px !important;
  height: auto !important;
  color: #e7e7e7 !important;
  align-self: unset !important;
}

.Button.active {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
}

/* data-element : menuButton */
[data-element='menuButton'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
}

[data-element='menuButton']:hover,
[data-element='menuButton']:active,
[data-element='menuButton']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
}

/* data-element : leftPanelButton */
[data-element='leftPanelButton'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
}

[data-element='leftPanelButton']:hover,
[data-element='leftPanelButton']:active,
[data-element='leftPanelButton']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
}

/* data-element : view-controls-toggle-button */
[data-element='view-controls-toggle-button'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
}

[data-element='view-controls-toggle-button']:hover,
[data-element='view-controls-toggle-button']:active,
[data-element='view-controls-toggle-button']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
}

[data-element='view-controls-toggle-button'] .Icon {
  content: url('/images/apryse/view-controls.svg');
  width: auto !important;
  height: 24px !important;
}

[data-element='zoomOutButton'] {
  content: url('/images/apryse/zoom-out.svg');
  min-width: 20px !important;
  height: 20px !important;
  margin: 0 8px 0 0 !important;
  padding: 0 !important;
}

[data-element='zoomInButton'] {
  content: url('/images/apryse/zoom-in.svg');
  min-width: 20px !important;
  height: 20px !important;
  padding: 0 !important;
}

[data-element='view-controls-toggle-button']::after {
  content: 'View Controls';
}

/* data-element : zoom-container */
[data-element='zoom-container'] {
  height: auto !important;
  padding: 0 !important;
  margin: 8px 14px !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 18px !important;
  font-family: Onest !important;
  color: #020202 !important;
  background-color: #ffffff !important;
}

/* data-element : panToolButton */
[data-element='panToolButton'] {
  /* width: 100% !important; */
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
}

[data-element='panToolButton']:hover,
[data-element='panToolButton']:active,
[data-element='panToolButton']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
}

/* data-element : annotationEditToolButton */
[data-element='annotationEditToolButton'] {
  /* width: 100% !important; */
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
}

[data-element='annotationEditToolButton']:hover,
[data-element='annotationEditToolButton']:active,
[data-element='annotationEditToolButton']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
}

.RibbonGroup {
  gap: 0 !important;
}

/* data-element : toolbarGroup-View */
[data-element='toolbarGroup-View'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='toolbarGroup-View']:hover,
[data-element='toolbarGroup-View']:active,
[data-element='toolbarGroup-View']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : toolbarGroup-Annotate */
[data-element='toolbarGroup-Annotate'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='toolbarGroup-Annotate']:hover,
[data-element='toolbarGroup-Annotate']:active,
[data-element='toolbarGroup-Annotate']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : toolbarGroup-Shapes */
[data-element='toolbarGroup-Shapes'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='toolbarGroup-Shapes']:hover,
[data-element='toolbarGroup-Shapes']:active,
[data-element='toolbarGroup-Shapes']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : toolbarGroup-Insert */
[data-element='toolbarGroup-Insert'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='toolbarGroup-Insert']:hover,
[data-element='toolbarGroup-Insert']:active,
[data-element='toolbarGroup-Insert']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : toolbarGroup-Measure */
[data-element='toolbarGroup-Measure'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='toolbarGroup-Measure']:hover,
[data-element='toolbarGroup-Measure']:active,
[data-element='toolbarGroup-Measure']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : toolbarGroup-Redact */
[data-element='toolbarGroup-Redact'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='toolbarGroup-Redact']:hover,
[data-element='toolbarGroup-Redact']:active,
[data-element='toolbarGroup-Redact']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : toolbarGroup-Edit */
[data-element='toolbarGroup-Edit'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='toolbarGroup-Edit']:hover,
[data-element='toolbarGroup-Edit']:active,
[data-element='toolbarGroup-Edit']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : toolbarGroup-EditText */
[data-element='toolbarGroup-EditText'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='toolbarGroup-EditText']:hover,
[data-element='toolbarGroup-EditText']:active,
[data-element='toolbarGroup-EditText']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : toolbarGroup-FillAndSign */
[data-element='toolbarGroup-FillAndSign'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='toolbarGroup-FillAndSign']:hover,
[data-element='toolbarGroup-FillAndSign']:active,
[data-element='toolbarGroup-FillAndSign']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : toolbarGroup-Forms */
[data-element='toolbarGroup-Forms'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='toolbarGroup-Forms']:hover,
[data-element='toolbarGroup-Forms']:active,
[data-element='toolbarGroup-Forms']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : searchPanelToggle */
[data-element='searchPanelToggle'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='searchPanelToggle']:hover,
[data-element='searchPanelToggle']:active,
[data-element='searchPanelToggle']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : notesPanelToggle */
[data-element='notesPanelToggle'] {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  padding: 8px 14px !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  font-family: 'Onest', sans-serif !important;
  color: #585858 !important;
  cursor: pointer !important;
  border: none !important;
}

[data-element='notesPanelToggle']:hover,
[data-element='notesPanelToggle']:active,
[data-element='notesPanelToggle']:focus {
  background-color: #fef2ef !important;
  color: #f0401d !important;
  box-shadow: none !important;
  border: none !important;
}

/* data-element : default-ribbon-groupDropdown */
[data-element='default-ribbon-groupDropdown'] {
  font-family: 'Onest', sans-serif !important;
}

.Flyout .FlyoutContainer .flyout-item-container:nth-of-type(1),
.Flyout .FlyoutContainer .flyout-item-container:nth-of-type(3) {
  display: none !important;
}
