'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import pathsConfig from '@pdfily/config/paths.config';
import { Button } from '@pdfily/ui/button';
import { cn } from '@pdfily/ui/utils';
import { Link } from '@/lib/i18n/navigation';

/**
 * AuthButtons - Renders the authentication buttons (Log In).
 *
 * @example
 * <AuthButtons />
 */
const AuthButtons = () => {
  const t = useTranslations('Header');

  return (
    <Link href={pathsConfig.auth.signIn}>
      <Button
        variant="outline"
        className={cn(
          'h-[50px] w-[112px] rounded-[10px] border border-[#F0401D] text-[#F0401D]',
          'hover:bg-red-50 hover:text-red-500',
          'font-onest text-[16px] font-medium leading-5 tracking-normal',
          'small:hidden final:hidden',
        )}
      >
        {t('signInButton')}
      </Button>
    </Link>
  )
}

export default AuthButtons
