/**
 * Cleans the file name to make it compatible with Supabase Storage.
 * - <PERSON><PERSON><PERSON> accents
 * - Replaces spaces with underscores
 * - Removes special characters
 * - Preserves the file extension
 */
export function sanitizeFileName(fileName: string): string {
  // Separate the name and the extension
  const lastDotIndex = fileName.lastIndexOf('.');
  const name = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
  const extension = lastDotIndex > 0 ? fileName.substring(lastDotIndex) : '';

  // Clean the name
  const cleanName = name
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove accents
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/[^a-zA-Z0-9._-]/g, '') // Remove special characters
    .replace(/_+/g, '_') // Collapse multiple underscores into one
    .replace(/^[._-]+|[._-]+$/g, '') // Trim special characters at start/end
    .substring(0, 80); // Limit length

  // Clean the extension
  const cleanExtension = extension.toLowerCase().replace(/[^a-z0-9.]/g, ''); // Remove invalid characters from extension

  return `${cleanName}${cleanExtension}`;
}
