import { z } from "zod";
import S3StorageConfigSchema from "./schemas/s3-storage.schema";

export type S3StorageConfig = z.infer<typeof S3StorageConfigSchema>;

// Parse and validate the configuration at startup.
const s3StorageConfig: S3StorageConfig = S3StorageConfigSchema.parse({
  accessKeyId: process.env.S3_ACCESS_KEY,
  secretAccessKey: process.env.S3_SECRET_KEY,
  endpointUrl: process.env.S3_HOST,
  region: process.env.S3_REGION,
  bucket: process.env.S3_BUCKET ?? 'uploads',
});

export default s3StorageConfig;
