import { useRef, useState, RefObject } from 'react';

/**
 * A reusable base hook for PDF viewer integration (e.g. PDFTron or Nutrient).
 * It provides shared state management for viewer container ref, instance, and loading state.
 *
 * @template TInstance - The type of the PDF viewer instance (e.g., WebViewerInstance or NutrientViewerInstance).
 * @returns {{
 *   viewer: RefObject<HTMLDivElement | null>,
 *   instance: TInstance | null,
 *   setInstance: React.Dispatch<React.SetStateAction<TInstance | null>>,
 *   loading: boolean,
 *   setLoading: React.Dispatch<React.SetStateAction<boolean>>
 * }}
 */
export function usePDFViewerBase<TInstance>() {
  const viewer: RefObject<HTMLDivElement | null> = useRef<HTMLDivElement | null>(null);
  const [instance, setInstance] = useState<TInstance | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  return {
    viewer,
    instance,
    setInstance,
    loading,
    setLoading
  };
}

