import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';
import appConfig from '@pdfily/config/app.config';

/**
 * Handles OAuth sign-in request for popup authentication using Supabase authentication.
 *
 * This endpoint expects a JSON body containing:
 * - `provider` (optional): the OAuth provider to use (default is `'google'`)
 * - `path`: the redirect path after successful authentication
 *
 * It returns a JSON response containing either:
 * - `url`: the URL to open in the popup for provider authentication
 * - or `error`: an error message in case of failure
 *
 * @param {NextRequest} request - The incoming POST request
 * @returns {Promise<NextResponse>} A JSON response with the OAuth redirect URL or an error
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { provider = 'google', path } = await request.json();
    const supabase = getSupabaseServerClient();

    // Create callback URL for popup
    const callbackUrl = new URL('/auth/popup-callback', appConfig.url);
    callbackUrl.searchParams.set('redirect_to', path);

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: callbackUrl.toString(),
        queryParams: { 
          prompt: 'select_account' // Force account selection for Google
        },
        skipBrowserRedirect: true, // Important for popup flow
      },
    });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    return NextResponse.json({ url: data.url });
  } catch (error) {
    console.error('Popup OAuth error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
