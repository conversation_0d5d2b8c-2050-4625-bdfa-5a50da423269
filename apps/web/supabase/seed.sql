-- Insert SolidGate Active Subscription
insert into public.subscriptions (
  id,
  provider,
  stripe_customer_id,
  solidgate_customer_id,
  subscription_id,
  plan,
  status,
  started_at,
  current_period_end,
  has_paid_trial,
  currency,
  next_billing_at,
  metadata,
  created_at,
  updated_at
) values (
  gen_random_uuid(),              -- id
  'solidgate',                    -- provider
  'stripe_cus_001',                -- stripe_customer_id
  'solidgate_cus_001',             -- solidgate_customer_id
  'solidgate_sub_001',             -- subscription_id
  'pro',                           -- plan
  'active',                        -- status
  now() - interval '5 days',       -- started_at
  now() + interval '25 days',      -- current_period_end
  true,                            -- has_paid_trial
  'USD',                           -- currency
  now() + interval '25 days',      -- next_billing_at
  '{}'::jsonb,                     -- metadata
  now(),                           -- created_at
  now()                            -- updated_at
);

-- Insert SolidGate Inactive Subscription (canceled/expired)
insert into public.subscriptions (
  id,
  provider,
  stripe_customer_id,
  solidgate_customer_id,
  subscription_id,
  plan,
  status,
  started_at,
  current_period_end,
  has_paid_trial,
  currency,
  next_billing_at,
  metadata,
  created_at,
  updated_at
) values (
  gen_random_uuid(),              -- id
  'solidgate',                    -- provider
  'stripe_cus_002',                -- stripe_customer_id
  'solidgate_cus_002',             -- solidgate_customer_id
  'solidgate_sub_002',             -- subscription_id
  'basic',                         -- plan
  'canceled',                      -- status
  now() - interval '60 days',      -- started_at (60 days ago)
  now() - interval '30 days',      -- current_period_end (30 days ago, expired)
  false,                           -- has_paid_trial (let's say no)
  'USD',                           -- currency
  now() - interval '30 days',      -- next_billing_at (no next billing for canceled, but logically set same as expired)
  '{}'::jsonb,                     -- metadata
  now(),                           -- created_at
  now()                            -- updated_at
);

