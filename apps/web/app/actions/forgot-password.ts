"use server";

import { headers } from "next/headers";
import { redirect } from "next/navigation";

import authConfig from '@pdfily/config/auth.config';
import pathsConfig from "@pdfily/config/paths.config";
import { encodedRedirect } from "@pdfily/shared/utils";
import { getSupabaseServerClient } from "@pdfily/supabase/server-client";

/**
 * Handles the forgot password action by sending a reset link to the user's email.
 *
 * @param {FormData} formData - The form data containing the user's email and optional callback URL.
 *
 * @description
 * This function attempts to send a password reset link using Supabase authentication.
 * If successful, it redirects to a success page or callback URL. If failed, it returns an error redirect.
 */
export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const captchaToken = formData.get("captchaToken")?.toString();
  const callbackUrl = formData.get("callbackUrl")?.toString();
  const origin = (await headers()).get("origin");

  if (!email) {
    return encodedRedirect(
      "error",
      pathsConfig.auth.forgotPassword,
      'Email is required'
    );
  }

  // Get supabase client
  const supabase = getSupabaseServerClient();

  // Get the redirect URL
  const redirectTo = `${origin}/auth/callback?redirect_to=/reset-password`;

  // Reset password for email
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo,
    ...(authConfig.captchaEnabled && { captchaToken }),
  });

  if (error) {
    return encodedRedirect(
      "error",
      pathsConfig.auth.forgotPassword,
      "Could not reset password",
    );
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return encodedRedirect(
    "success",
    pathsConfig.auth.forgotPassword,
    "Check your email for a link to reset your password.",
  );
};