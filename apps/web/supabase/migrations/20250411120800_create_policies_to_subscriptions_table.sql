-- Migration to create the policies to the public.subscriptions table

-- Enable Row-Level Security on the public.subscriptions table
alter table public.subscriptions enable row level security;

-- Row-level security policies

-- Policy: Allow users to read their own subscriptions
create policy "Users can read their own subscription"
  on "public"."subscriptions"
  for select
  using (auth.uid() = user_id);

-- Policy: Allow users to insert subscriptions
create policy "Users can create their own subscription"
  on "public"."subscriptions"
  for insert
  with check (auth.uid() = user_id);