/**
 * Utility function to download a blob file.
 * @param {Blob} blob - The file blob to download.
 * @param {Response} response - The fetch response to extract the file name.
 * @param {string} defaultFileName - The default file name to use if none is found.
 */
export const downloadBlob = (blob: Blob, response: Response, defaultFileName: string) => {
  const url = window.URL.createObjectURL(blob);

  // Get the file name from the Content-Disposition header
  const contentDisposition = response.headers.get('Content-Disposition');
  let fileName = defaultFileName;

  if (contentDisposition) {
    const match = contentDisposition.match(/filename="?(.+?)"?$/);
    if (match && match[1]) {
      fileName = match[1];
    }
  }

  // Create a temporary anchor element to trigger the download
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();

  // Cleanup
  a.remove();
  window.URL.revokeObjectURL(url);
};
