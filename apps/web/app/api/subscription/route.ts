import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = getSupabaseServerClient(); // Get Supabase DB client
    const {
      data: { user },
    } = await supabase.auth.getUser(); // Get authenticated user

    if (!user) {
      return NextResponse.json({
        error: 'Unauthorized',
        message: 'Please signin and try again',
        statusCode: 401,
        timestamp: new Date().toISOString(),
      });
    }

    // Fetch user's subscription from Supabase
    const { data, error } = await supabase.from('subscriptions').select('*').eq('user_id', user.id).single();

    if (error || !data) {
      return NextResponse.json(
        {
          error: 'Subscription not found',
          message: `The user subscription does not exist or may have been deleted.`,
          statusCode: 404,
          timestamp: new Date().toISOString(),
        },
        { status: 404 },
      );
    }

    return NextResponse.json(data, { status: 200 });
  } catch (err) {
    const error = err as Error;
    console.error('❌ Error fetching subscription:', {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'An unexpected error occurred while retrieving the subscription. Please try again later.',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
