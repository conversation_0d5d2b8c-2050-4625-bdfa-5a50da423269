#!/bin/bash

# Absolute path to root of the repo (this file is in root)
ROOT="$(git rev-parse --show-toplevel)"

# Path to pdftron static assets in node_modules
VIEWER_DIST="$ROOT/apps/web/node_modules/@pdftron/webviewer/public"

# Old (deprecated) target directory (in case it was used before)
OLD_TARGET_DIR="$ROOT/apps/web/public/pdftron-webviewer"

# Path to public/lib/pdftron-webviewer inside apps/web
TARGET_DIR="$ROOT/apps/web/public/lib/pdftron-webviewer"

# Log paths
echo "🔍 Source: $VIEWER_DIST"
echo "🎯 New Destination: $TARGET_DIR"
echo "🗑️ Old Destination (to be removed): $OLD_TARGET_DIR"

# Check if source dist exists
if [ ! -d "$VIEWER_DIST" ]; then
  echo "❌ Error: PDFTron public directory not found at $VIEWER_DIST"
  exit 1
fi

# Remove old directory if it exists
if [ -d "$OLD_TARGET_DIR" ]; then
  echo "🧹 Removing old PDFTron assets from: $OLD_TARGET_DIR"
  rm -rf "$OLD_TARGET_DIR"
fi

# Remove target directory if it exists
if [ -d "$TARGET_DIR" ]; then
  echo "🧹 Removing old PDFTron assets from: $TARGET_DIR"
  rm -rf "$TARGET_DIR"
fi

# Recreate target directory
echo "📂 Recreating directory: $TARGET_DIR"
mkdir -p "$TARGET_DIR"

# Copy new assets
echo "📦 Copying new PDFTron WebViewer assets..."
start=$(date +%s)
cp -R "$VIEWER_DIST/"* "$TARGET_DIR"
end=$(date +%s)

echo "✅ PDFTron WebViewer assets updated successfully in $((end - start)) seconds!"
