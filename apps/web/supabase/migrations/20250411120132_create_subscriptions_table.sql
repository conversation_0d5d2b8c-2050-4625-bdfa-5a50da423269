-- Create a new table to store subscriptions (Stripe or SolidGate)
create table if not exists public.subscriptions (
  id uuid primary key default gen_random_uuid(), -- Unique document ID
  user_id uuid references auth.users(id) on delete cascade, -- Owner of the subscription

  -- Subscription details
  provider text not null, -- 'stripe' or 'solidgate'
  customer_id text not null, -- e.g. Stripe or SolidGate customer ID
  subscription_id text not null, -- Subscription ID from provider
  plan text not null, -- e.g. 'basic', 'pro', etc.
  status text not null, -- 'active', 'trialing', 'canceled', etc.
  started_at timestamp with time zone default now(),
  current_period_end timestamp with time zone,
  metadata jsonb default '{}'::jsonb,

  -- Audit fields
  created_at timestamp with time zone default now(),
  updated_at timestamp with time zone default now()
);

-- Index for user-based queries (e.g. show current user's subscription)
create index idx_subscriptions_user_id on public.subscriptions(user_id);

-- Index for filtering by status and expiry date (e.g. active subscriptions)
create index idx_subscriptions_status_period_end on public.subscriptions(status, current_period_end);

-- Index for finding subscription by customer_id and provider (e.g. from webhook)
create index idx_subscriptions_provider_customer on public.subscriptions(provider, customer_id);

-- Index for locating subscription by provider + subscription ID (e.g. subscription update from webhook)
create index idx_subscriptions_provider_subscription on public.subscriptions(provider, subscription_id);

-- Index for sorting by expiration date (used in view or analytics)
create index idx_subscriptions_current_period_end on public.subscriptions(current_period_end);