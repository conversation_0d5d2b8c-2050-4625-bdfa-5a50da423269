'use client';

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@pdfily/ui/dialog';
import { Progress } from '@pdfily/ui/progress';
import { useTranslations } from 'next-intl';

interface ProgressModalProps {
  isOpen: boolean;
  progress: number;
  onClose: () => void;
}

export default function ProgressModal({ isOpen, progress, onClose }: ProgressModalProps) {
  const t = useTranslations('HomePage.bannerSection.progressModal');

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="rounded-xl p-6 shadow-lg">
        <DialogHeader>
          <DialogTitle className="text-2xl text-[#1c1c1c]">{t('uploading')}</DialogTitle>
          <DialogDescription className="text-lg text-[#585858]">{t('pleaseWait')}</DialogDescription>
        </DialogHeader>
        <div className="mt-4 space-y-2">
          <Progress value={progress} className="h-4 rounded-lg" />
          <p className="mt-2 text-center text-sm font-medium">{progress}%</p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
