'use client';

import React from 'react';
import Image from 'next/image';
import type { User } from '@supabase/supabase-js';
// import type { WebViewerInstance } from '@pdftron/webviewer';
// import { useCustomDownloadButton } from '../hooks/apryse-viewer/toolbars/use-custom-download-button';
import { useCustomDownloadButton } from '../hooks/nutrient-viewer/toolbars/use-custom-download-button';
import { Instance } from '@nutrient-sdk/viewer';

interface PdfNutrientWebViewerHeaderProps {
  instance: Instance | null;
  documentId: string | null | undefined;
  user: User;
}

export default function PdfNutrientWebViewerHeader({ instance, documentId, user }: PdfNutrientWebViewerHeaderProps) {
  const customDownloadButton = useCustomDownloadButton(instance, documentId, user);

  return (
    <div className="px-8 small:px-4 py-2 border-b border-white bg-white flex justify-between items-center md:gap-3">
      <Image src="/images/header/Logo.svg" alt="PDFily Logo" width={121} height={36} className="w-[121px] h-auto" />
      {customDownloadButton && (
        <button
          onClick={customDownloadButton.onPress}
          style={{
            color: '#F0401D',
            border: '1px solid #F0401D',
            borderRadius: '10px',
            padding: '10px 30px',
            background: 'white',
            fontWeight: 500,
            fontSize: '16px',
            lineHeight: '20px',
          }}
        >
          Done
        </button>
      )}
    </div>
  );
}
