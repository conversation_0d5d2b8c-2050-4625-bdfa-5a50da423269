import type { Config } from 'tailwindcss';

// We want each package to be responsible for its own content.
const config: Omit<Config, 'content'> = {
  theme: {
    screens: {
      tv: { max: '1920px' },
      mac: { max: '1440px' },
      desktop: { max: '1280px' },
      laptop: { max: '1024px' },
      tablet: { max: '768px' },
      small: { max: '640px' },
      final: { max: '375px' },
    },
    extend: {
      backgroundImage: {
        'glow-conic': 'conic-gradient(from 180deg at 50% 50%, #2a8af6 0deg, #a853ba 180deg, #e92a67 360deg)',
      },
    },
  },
  plugins: [],
};
export default config;
