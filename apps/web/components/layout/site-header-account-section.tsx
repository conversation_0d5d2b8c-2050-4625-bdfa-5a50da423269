'use client';

import { User } from '@supabase/supabase-js';

import { useSignOut } from '@pdfily/supabase/hooks/use-sign-out';

import { useAuth } from '../session-provider';
import PersonalAccountDropdown from './header/personal-account-dropdown';
import AuthButtons from './header/buttons/auth-buttons';

interface SiteHeaderAccountSectionProps {
  user: User | null;
}

/**
 * @name SiteHeaderAccountSection
 *
 * @description
 * Renders the user's account section in the site header.
 *
 * @param {SiteHeaderAccountSectionProps} props - The component props.
 */
export function SiteHeaderAccountSection({ user }: React.PropsWithChildren<SiteHeaderAccountSectionProps>) {
  const { isAuthenticated } = useAuth();

  if (!(user && isAuthenticated)) {
    return <AuthButtons />;
  }

  return <SuspendedPersonalAccountDropdown user={user} />;
}

/**
 * @name SuspendedPersonalAccountDropdown
 *
 * @description
 * Renders a personal account dropdown if the user is authenticated.
 * Otherwise, it displays authentication buttons.
 *
 * @param {SiteHeaderAccountSectionProps} props - The component props containing user information.
 *
 * @example
 * <SuspendedPersonalAccountDropdown user={user} />
 */
function SuspendedPersonalAccountDropdown(props: SiteHeaderAccountSectionProps) {
  const signOutQuery = useSignOut();
  const { user, isAuthenticated } = useAuth();

  if (user && isAuthenticated) {
    return <PersonalAccountDropdown user={user} signOutRequested={() => signOutQuery.mutateAsync()} />;
  }

  return <AuthButtons />;
}
