/* common */
.PSPDFKit-Toolbar {
  min-height: 62px;
  box-shadow: 0px 4px 25px 0px #00000017;
}

.PSPDFKit-Toolbar .BaselineUI-Separator {
  background: #e7e7e7;
  min-height: 46px !important;
  display: none;
}

.PSPDFKit-Toolbar-Button-Redo + .BaselineUI-Separator,
.PSPDFKit-Toolbar-Button-Print + .BaselineUI-Separator {
  display: block;
}

.PSPDFKit-Toolbar-Button-active,
.PSPDFKit-Toolbar .BaselineUI-ToggleIconButton[aria-pressed='true'] {
  background: #fef2ef;
}

.PSPDFKit-Toolbar .BaselineUI-ActionIconButton,
.PSPDFKit-Toolbar .BaselineUI-ToggleIconButton {
  height: 62px;
  padding: 8px 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.PSPDFKit-Toolbar .BaselineUI-ActionIconButton:focus {
  box-shadow: none;
}

.PSPDFKit-Toolbar .BaselineUI-ActionIconButton[data-disabled='true'] > span > svg > path {
  fill: #aeaeae;
}

.PSPDFKit-Toolbar .BaselineUI-ActionIconButton[data-disabled='true']::after {
  color: #aeaeae;
}

.PSPDFKit-Toolbar .BaselineUI-ActionIconButton > span,
.PSPDFKit-Toolbar .BaselineUI-ToggleIconButton > span {
  width: 26px;
  height: 26px;
}

.PSPDFKit-Toolbar .BaselineUI-ActionIconButton::after,
.PSPDFKit-Toolbar .BaselineUI-ToggleIconButton::after {
  font-family: 'Onest';
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  color: #585858;
  letter-spacing: 0px;
}

/* dropdown */
.PSPDFKit-Toolbar-DropdownGroup {
  height: 62px;
  position: relative;
}

.PSPDFKit-Toolbar-DropdownGroup > .PSPDFKit-Toolbar-Button {
  width: 100%;
  height: 100%;
  padding: 8px 16px;

  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.PSPDFKit-Toolbar-DropdownGroup > .PSPDFKit-Toolbar-Button > span {
  width: 26px;
  height: 26px;
}

.PSPDFKit-Toolbar-DropdownGroup > .PSPDFKit-Toolbar-Button::after {
  font-family: 'Onest';
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  color: #585858;

  position: absolute;
  left: 16px;
  bottom: 8px;
  width: calc(100% - 32px);
  text-align: center;
}

.PSPDFKit-Toolbar-DropdownGroup > .BaselineUI-Select {
  position: absolute;
  top: 15px;
  right: 4px;
  width: 12px !important;
  height: 12px;
}

.PSPDFKit-Toolbar-DropdownGroup > .BaselineUI-Select > .PSPDFKit-Input-Dropdown-Button {
  background: transparent;
  color: #585858;
  min-height: 12px;
  width: 12px;
  height: 12px;
  padding: 0px;

  display: flex;
  align-items: center;
  justify-content: center;
}

.PSPDFKit-Toolbar-DropdownGroup > .BaselineUI-Select > .PSPDFKit-Input-Dropdown-Button > svg {
  width: 9px;
  height: 9px;
  color: #585858;
}

.PSPDFKit-Input-Dropdown-Item {
  height: 62px;
  padding: 8px 16px;
}

.PSPDFKit-Input-Dropdown-Item > div {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.PSPDFKit-Input-Dropdown-Item[data-selected='true'] {
  background: #fef2ef;
}

.PSPDFKit-Input-Dropdown-Item > div > span:nth-child(1) {
  display: none;
}

.PSPDFKit-Input-Dropdown-Item > div > span:nth-child(2) {
  width: 26px;
  height: 26px;
}

.PSPDFKit-Input-Dropdown-Item > div > span:nth-child(3) {
  font-family: 'Onest';
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  color: #585858;
}

.BaselineUI-Popover-Content:has(.PSPDFKit-Input-Dropdown-Item) {
  border-radius: 10px;
}

.BaselineUI-ListBox:has(.PSPDFKit-Input-Dropdown-Item) {
  display: flex;
  flex-direction: row !important;
  padding: 6px !important;
  border-radius: 10px;
  box-shadow: 1px 2px 4px 1px #9b9ea329;
}

/* Thumbnails */
/* .PSPDFKit-Toolbar-DropdownGroup-Sidebar {
  width: 95px !important;
} */

.PSPDFKit-Toolbar-DropdownGroup-Sidebar > .PSPDFKit-Toolbar-Button {
  padding-left: 16px;
  width: 95px;
}

.PSPDFKit-Toolbar-DropdownGroup-Sidebar > .PSPDFKit-Toolbar-Button > span {
  margin-left: auto;
  margin-right: auto;
}

.PSPDFKit-Toolbar-Button-Sidebar-Thumbnails:not(.PSPDFKit-Input-Dropdown-Item)::after {
  content: 'Thumbnails';
}

.PSPDFKit-Toolbar-Button-Sidebar-Thumbnails.PSPDFKit-Input-Dropdown-Item {
  width: 95px !important;
}

.PSPDFKit-Toolbar-Button-Sidebar-Document-Outline:not(.PSPDFKit-Input-Dropdown-Item)::after {
  content: 'Outline';
}

.PSPDFKit-Toolbar-Button-Sidebar-Document-Outline.PSPDFKit-Input-Dropdown-Item {
  width: 72px;
}

.PSPDFKit-Toolbar-Button-Sidebar-Annotations:not(.PSPDFKit-Input-Dropdown-Item)::after {
  content: 'Annotations';
}

.PSPDFKit-Toolbar-Button-Sidebar-Annotations.PSPDFKit-Input-Dropdown-Item {
  width: 101px;
}

.PSPDFKit-Toolbar-Button-Sidebar-Bookmarks:not(.PSPDFKit-Input-Dropdown-Item)::after {
  content: 'Bookmarks';
}

.PSPDFKit-Toolbar-Button-Sidebar-Bookmarks.PSPDFKit-Input-Dropdown-Item {
  width: 94px;
}

/* Shapes */
.PSPDFKit-Toolbar-DropdownGroup-Shapes {
  width: 74px;
}

.PSPDFKit-Toolbar-DropdownGroup-Shapes > .BaselineUI-Select {
  right: 4px;
  top: 15px;
}

.PSPDFKit-Toolbar-Button-Line-Annotation:not(.PSPDFKit-Toolbar-Button-Variant-Arrow):not(
    .PSPDFKit-Input-Dropdown-Item
  )::after {
  content: 'Line';
}

.PSPDFKit-Toolbar-Button-Line-Annotation.PSPDFKit-Input-Dropdown-Item {
  width: 58px;
}

.PSPDFKit-Toolbar-Button-Line-Annotation.PSPDFKit-Toolbar-Button-Variant-Arrow:not(
    .PSPDFKit-Input-Dropdown-Item
  )::after {
  content: 'Arrow';
}

.PSPDFKit-Toolbar-Button-Line-Annotation.PSPDFKit-Toolbar-Button-Variant-Arrow.PSPDFKit-Input-Dropdown-Item {
  width: 67px;
}

.PSPDFKit-Toolbar-Button-Rectangle-Annotation:not(.PSPDFKit-Input-Dropdown-Item)::after {
  content: 'Box';
}

.PSPDFKit-Toolbar-Button-Rectangle-Annotation.PSPDFKit-Input-Dropdown-Item {
  width: 58px;
}

.PSPDFKit-Toolbar-Button-Ellipse-Annotation:not(.PSPDFKit-Input-Dropdown-Item)::after {
  content: 'Circle';
}

.PSPDFKit-Toolbar-Button-Ellipse-Annotation.PSPDFKit-Input-Dropdown-Item {
  width: 64px;
}

.PSPDFKit-Toolbar-Button-Polygon-Annotation:not(.PSPDFKit-Input-Dropdown-Item)::after {
  content: 'Polygon';
}

.PSPDFKit-Toolbar-Button-Polygon-Annotation.PSPDFKit-Input-Dropdown-Item {
  width: 78px;
}

/* Ink */
.PSPDFKit-Toolbar-Button-Ink-Annotation {
  width: 62px;
}

.PSPDFKit-Toolbar-Button-Ink-Annotation::after {
  content: 'Draw';
}

.PSPDFKit-Toolbar-Button-Ink-Eraser {
  width: 68px;
}

.PSPDFKit-Toolbar-Button-Ink-Eraser::after {
  content: 'Eraser';
}

.PSPDFKit-Toolbar-Button-Ink-Annotation.PSPDFKit-Toolbar-Button-Variant-Highlighter {
  width: 83px;
}

.PSPDFKit-Toolbar-Button-Ink-Annotation.PSPDFKit-Toolbar-Button-Variant-Highlighter::after {
  content: 'Highlight';
}

.PSPDFKit-Toolbar-Button-TextHighlighter-Annotation {
  width: 108px;
}

.PSPDFKit-Toolbar-Button-TextHighlighter-Annotation::after {
  content: 'Text highlight';
}

/* Undo, Redo */
.PSPDFKit-Toolbar-Button-Undo,
.PSPDFKit-Toolbar-Button-Redo {
  width: 63px;
}

.PSPDFKit-Toolbar-Button-Undo::after {
  content: 'Undo';
}

.PSPDFKit-Toolbar-Button-Undo > span {
  margin-right: 4px;
}

.PSPDFKit-Toolbar-Button-Redo::after {
  content: 'Redo';
}

.PSPDFKit-Toolbar-Button-Redo > span {
  margin-left: 4px;
}

/* Text */
.PSPDFKit-Toolbar-Button-Text-Annotation {
  width: 58px;
}

.PSPDFKit-Toolbar-Button-Text-Annotation::after {
  content: 'Text';
}

.PSPDFKit-Toolbar-Button-Content-Editor {
  width: 80px;
}

.PSPDFKit-Toolbar-Button-Content-Editor::after {
  content: 'Edit Text';
  letter-spacing: -0.1px !important;
}

/* Image & Stamp*/
.PSPDFKit-Toolbar-Button-Image-Annotation {
  width: 66px;
}

.PSPDFKit-Toolbar-Button-Image-Annotation::after {
  content: 'Image';
}

.PSPDFKit-Toolbar-Button-Stamp-Annotation {
  width: 69px;
}

.PSPDFKit-Toolbar-Button-Stamp-Annotation::after {
  content: 'Stamp';
}

/* Link & Note*/
.PSPDFKit-Toolbar-Button-Link-Annotation {
  width: 58px;
}

.PSPDFKit-Toolbar-Button-Link-Annotation::after {
  content: 'Link';
}

.PSPDFKit-Toolbar-Button-Note-Annotation {
  width: 60px;
}

.PSPDFKit-Toolbar-Button-Note-Annotation::after {
  content: 'Note';
}

/* Sign */
.PSPDFKit-Toolbar-Button-Signature {
  width: 58px;
}

.PSPDFKit-Toolbar-Button-Signature::after {
  content: 'Sign';
}

/* Document Actions */
.PSPDFKit-Toolbar-Button-Document-Editor {
  width: 127px;
}

.PSPDFKit-Toolbar-Button-Document-Editor::after {
  content: 'Document editor';
}

.PSPDFKit-Toolbar-Button-Print {
  width: 59px;
}

.PSPDFKit-Toolbar-Button-Print::after {
  content: 'Print';
}

.PSPDFKit-Toolbar-Button-Search {
  width: 72px;
}

.PSPDFKit-Toolbar-Button-Search::after {
  content: 'Search';
}

/* Secondary Toolbar*/
.PSPDFKit-Content-Editing-Toolbar,
.PSPDFKit-Annotation-Toolbar > .BaselineUI-Toolbar {
  overflow-x: scroll !important;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer et Edge */
}

.PSPDFKit-Toolbar::-webkit-scrollbar,
.PSPDFKit-Annotation-Toolbar > .BaselineUI-Toolbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari et Opera */
}
