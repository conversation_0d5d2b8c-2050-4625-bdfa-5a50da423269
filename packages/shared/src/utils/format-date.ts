/**
 * Formats a date string into one of two formats:
 * - Date with time: "March 22, 2025, 02:35:48 PM"
 * - Date only: "March 22, 2025"
 *
 * @param {string | null} dateString - The date string to format.
 * @param {boolean} includeTime - Whether to include the time in the output.
 * @returns {string} The formatted date string.
 *
 * @example
 * formatDate("2025-03-22T14:35:48", true); // "March 22, 2025, 02:35:48 PM"
 * formatDate("2025-03-22T14:35:48", false); // "March 22, 2025"
 */
export const formatDate = (
  dateString: string | null,
  includeTime: boolean = false
): string => {
  if (!dateString) return "";

  const date = new Date(dateString);

  if (isNaN(date.getTime())) {
    console.warn(`Invalid date string: ${dateString}`);
    return "";
  }

  const dateOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "long",
    day: "numeric",
  };

  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false
  };

  const formattedDate = date.toLocaleDateString("en-US", dateOptions);

  if (includeTime) {
    const formattedTime = date.toLocaleTimeString("en-US", timeOptions);
    return `${formattedDate}, ${formattedTime}`;
  }

  return formattedDate;
};
