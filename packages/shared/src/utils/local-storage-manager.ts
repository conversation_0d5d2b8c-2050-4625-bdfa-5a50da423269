import lscache from 'lscache';

export class LocalStorageManager {
  /**
   * Set an item in localStorage
   * @param key The key under which the value will be stored
   * @param value The value to store (string only)
   */
  static setItem(key: string, value: string): void {
    try {
      lscache.set(key, value);
    } catch (error) {
      console.error(`Error saving to localStorage with key: ${key}`, error);
    }
  }

  /**
   * Get an item from localStorage
   * @param key The key to retrieve
   * @returns The stored string value or null if not found
   */
  static getItem(key: string): string | null {
    try {
      return lscache.get(key);
    } catch (error) {
      console.error(`Error reading from localStorage with key: ${key}`, error);
      return null;
    }
  }

  /**
   * Remove an item from localStorage
   * @param key The key to remove
   */
  static removeItem(key: string): void {
    try {
      lscache.remove(key);
    } catch (error) {
      console.error(`Error removing localStorage item with key: ${key}`, error);
    }
  }

  /**
   * Clear all localStorage items
   */
  static clear(): void {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Error clearing localStorage', error);
    }
  }
}
