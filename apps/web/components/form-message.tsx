import Image from 'next/image';
import { cn } from '@pdfily/ui/utils';
import { useTranslations } from 'next-intl';

export type Message = { success: string } | { error: string } | { message: string };

export function FormMessage({ message }: { message: Message }) {
  const t = useTranslations('Auth.forgotPassword');
  return (
    <div className="flex w-full max-w-md flex-col gap-2 text-sm">
      {'success' in message && (
        <div className="mt-5 border-l-2 border-foreground px-4 text-foreground">{message.success}</div>
      )}

      {/* sign-in page error */}
      {'error' in message && (
        <div className="mt-5 flex h-[72px] items-start gap-4 rounded-[10px] bg-[#FFF2F5] p-4">
          <div className="flex h-10 w-10 items-center justify-center rounded-[6px] bg-[#FFDAE2]">
            <Image
              src={'/images/auth/AlertCircle.svg'}
              alt={'alert'}
              width={26}
              height={26}
              className={cn('object-contain')}
            />
          </div>
          <p className="font-onest text-sm font-normal leading-5 text-[#1C1C1C]">
            {t.rich('formMessage', {
              br: () => <br />,
            })}
          </p>
        </div>
      )}
      {'message' in message && <div className="mt-5 border-l-2 px-4 text-foreground">{message.message}</div>}
    </div>
  );
}
