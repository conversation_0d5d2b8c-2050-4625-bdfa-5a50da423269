import Stripe from 'https://esm.sh/stripe@12.0.0';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Configure environment variables
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY') || '';
const stripeWebhookSecret = Deno.env.get('STRIPE_WEBHOOK_SIGNING_SECRET') || '';
const subscriptionPriceId = Deno.env.get('STRIPE_SUBSCRIPTION_PRICE_ID') || '';
const fourWeeksSubscriptionPriceId = Deno.env.get('STRIPE_4_WEEKS_SUBSCRIPTOIN_PRICE_ID') || '';

const endPointSecret = !!stripeWebhookSecret;

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

// Initialize Stripe client
const stripeClient = new Stripe(stripeSecretKey, { apiVersion: '2025-04-30.basil' });

const cryptoProvider = Stripe.createSubtleCryptoProvider();

/**
 * Handles incoming Stripe webhook requests
 * @param req - The incoming HTTP request containing Stripe webhook data
 * @returns A Response object with status code and JSON body
 * @description This function:
 * 1. Verifies the webhook signature if endpoint secret is configured
 * 2. Constructs the Stripe event from the raw request body
 * 3. Routes the event to appropriate handler based on event type
 * 4. Returns success/error response
 */
Deno.serve(async (request) => {
  try {
    // Initialize event
    let event;

    // Get the raw body of the request
    const rawBody = await request.text();

    if (endPointSecret && rawBody) {
      // Get the stripe signature from the request headers
      const stripeSignature = request.headers.get('stripe-signature');

      try {
        event = await stripeClient.webhooks.constructEventAsync(
          rawBody,
          stripeSignature,
          stripeWebhookSecret,
          undefined,
          cryptoProvider,
        );
      } catch (_error) {
        console.log(`⚠️ Webhook signature verification failed.`, _error);
        return new Response(JSON.stringify({ error: 'Webhook signature verification failed' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event);
        break;
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        await handleSubscription(event);
        break;
      default:
        console.log(`Unhandled event type ${event.type}.`);
    }
    // Return a success response after handling the event
    return new Response(JSON.stringify({ ok: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (_error) {
    console.error(`Stripe webhook error`, _error);
    return new Response(JSON.stringify({ error: `Stripe webhook error`, _error }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }
});

/**
 * Handles a successful Stripe payment intent webhook event
 * @param event - The Stripe webhook event containing payment intent data
 * @description This function processes successful payment intents by:
 * 1. Retrieving the full payment intent details
 * 2. Checking if it's a subscription payment (skips if true)
 * 3. Handling trial payments and user profile updates
 * 4. Creating/updating Stripe customer records
 */
async function handlePaymentIntentSucceeded(event: Stripe.Event) {
  const paymentIntentIdFromWebhook = event.data.object.id;

  const paymentIntent = await stripeClient.paymentIntents.retrieve(paymentIntentIdFromWebhook);

  if (paymentIntent.customer) {
    console.log('Skipping handling successful payment intent, payment is a subscription.');
    return; // Skip if it's a subscription payment
  }

  const { email, name, isTrial: isTrialString, userId: user_id } = paymentIntent.metadata;
  const isTrial = isTrialString === 'true';

  // Check if the user already exists
  const { data: existingUser } = await supabase.auth.admin.getUserById(user_id);

  let userId;
  if (existingUser?.user) {
    userId = existingUser.user.id;
  }
  const displayName = existingUser?.user?.user_metadata?.name || name;

  // Check if the user has already paid the trial fee
  const { data: userProfile } = userId
    ? await supabase.from('subscriptions').select('*').eq('user_id', userId).single()
    : { data: null };

  const hasPaidTrial = userProfile?.has_paid_trial === true;

  if (hasPaidTrial) {
    console.log(`User with email ${email} has already paid the trial fee.`);
    return; // Skip further processing to avoid duplicate charge
  }

  // Handle existing user or new user
  if (userId) {
    // Update display name if provided
    if (name && displayName !== name) {
      await supabase.auth.admin.updateUserById(userId, {
        user_metadata: { ...existingUser?.user?.user_metadata, name },
      });
      console.log(`Updated displayName for user ${email} to ${name}`);
    }
  }

  const paymentMethod = await stripeClient.paymentMethods.retrieve(paymentIntent.payment_method as string);

  const customer = await stripeClient.customers.create({
    email,
    name,
    payment_method: paymentIntent.payment_method as string,
    invoice_settings: {
      default_payment_method: paymentIntent.payment_method as string,
    },
    address: {
      city: paymentMethod.billing_details.address?.city ?? undefined,
      country: paymentMethod.billing_details.address?.country ?? undefined,
      postal_code: paymentMethod.billing_details.address?.postal_code ?? undefined,
      line1: paymentMethod.billing_details.address?.line1 ?? undefined,
      line2: paymentMethod.billing_details.address?.line2 ?? undefined,
      state: paymentMethod.billing_details.address?.state ?? undefined,
    },
    metadata: {
      name,
      uid: userId,
    },
  });

  const subscriptionParams: Stripe.SubscriptionCreateParams = {
    customer: customer.id,
    items: [{ price: isTrial ? fourWeeksSubscriptionPriceId : subscriptionPriceId }],
    metadata: {
      email,
      uid: userId,
    },
    automatic_tax: { enabled: false },
    payment_behavior: 'error_if_incomplete',
  };

  // Add conditional trial properties
  if (isTrial) {
    subscriptionParams.trial_period_days = 7;
    subscriptionParams.trial_settings = {
      end_behavior: { missing_payment_method: 'cancel' },
    };
    subscriptionParams.proration_behavior = 'none';
  }

  // Create the subscription
  const subscription = await stripeClient.subscriptions.create(subscriptionParams);

  // Update user claims
  const { data: updatedUser, error: claimsError } = await supabase.auth.admin.updateUserById(userId!, {
    user_metadata: {
      ...existingUser?.user?.user_metadata,
      current_period_end: subscription.items.data[0].current_period_end * 1000,
      scheduled_cancel_at: subscription.cancel_at * 1000,
      stripe_customer_id: customer.id,
      is_subscribed: ['active', 'trialing'].includes(subscription.status),
    },
  });

  console.log({ updatedUser });

  if (claimsError) {
    console.error(`Failed to update user claims: ${claimsError.message}`);
  }

  // Check if the user already has a subscription record
  const { data: existingSubscription, error: selectError } = await supabase
    .from('subscriptions')
    .select('id')
    .eq('user_id', userId)
    .single();

  if (selectError && selectError.code !== 'PGRST116') {
    // PGRST116 means "no result found", which is expected if the record doesn't exist
    console.error('Error checking subscription:', selectError);
  }

  const subscriptionData = {
    user_id: userId,
    currency: paymentIntent.currency,
    provider: 'stripe',
    plan: subscription.items.data[0].plan.id,
    stripe_customer_id: customer.id,
    subscription_id: subscription.id,
    status: subscription.status,
    next_billing_at: new Date(subscription.items.data[0].current_period_end * 1000),
    started_at: new Date().toISOString(),
    has_paid_trial: true,
  };

  if (existingSubscription) {
    // Update if a record exists
    const { error: updateError } = await supabase.from('subscriptions').update(subscriptionData).eq('user_id', userId);

    if (updateError) {
      console.error('Error updating subscription:', updateError);
    }
  } else {
    // Insert if no record exists
    const { error: insertError } = await supabase.from('subscriptions').insert(subscriptionData);

    if (insertError) {
      console.error('Error inserting subscription:', insertError);
    }
  }
}

/**
 * Handles subscription events from Stripe
 * @param event - The Stripe webhook event containing subscription data
 * @description This function processes subscription events by:
 * 1. Ignoring incomplete subscriptions and trial subscriptions that have been created
 * 2. Updating the user profile with the latest subscription details
 * 3. Updating user claims (via RLS policies in Supabase)
 */
async function handleSubscription(event: Stripe.Event) {
  const subscription = event.data.object;

  if (
    subscription.status === 'incomplete' ||
    (subscription.status == 'trialing' && event.type === 'customer.subscription.created')
  ) {
    return; // Ignore incomplete subscriptions and trial subscriptions that have been created
  }

  const userId = subscription.metadata.uid;

  // Get the associated user
  const { data: existingUser } = await supabase.auth.admin.getUserById(userId);

  // Check if the user already has a subscription record
  const { data: existingSubscription, error: selectError } = await supabase
    .from('subscriptions')
    .select('id')
    .eq('user_id', userId)
    .single();

  if (selectError && selectError.code !== 'PGRST116') {
    console.error('Error checking subscription:', selectError);
  }

  const subscriptionData = {
    user_id: userId,
    currency: subscription.currency,
    provider: 'stripe',
    plan: subscription.items.data[0].plan.id,
    stripe_customer_id: subscription.customer,
    subscription_id: subscription.id,
    status: subscription.status,
    next_billing_at: new Date(subscription.items.data[0].current_period_end * 1000),
    started_at: new Date().toISOString(),
    has_paid_trial: true,
  };

  // Update user profile
  if (existingSubscription) {
    // Update if a record exists
    const { error: updateError } = await supabase.from('subscriptions').update(subscriptionData).eq('user_id', userId);

    if (updateError) {
      console.error('Error updating subscription:', updateError);
    }
  } else {
    // Insert if no record exists
    const { error: insertError } = await supabase.from('subscriptions').insert(subscriptionData);

    if (insertError) {
      console.error('Error inserting subscription:', insertError);
    }
  }

  // Update user claims
  const updateUserClaimsPromise = supabase.auth.admin.updateUserById(subscription.metadata.uid, {
    user_metadata: {
      ...existingUser?.user?.user_metadata,
      current_period_end: subscription.items.data[0].current_period_end * 1000,
      scheduled_cancel_at: subscription.cancel_at * 1000,
      stripe_customer_id: subscription.customer,
      is_subscribed: ['active', 'trialing'].includes(subscription.status),
    },
  });

  await updateUserClaimsPromise;
}
