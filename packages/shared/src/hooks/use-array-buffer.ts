'use client';

import { useState, useEffect } from 'react';

/**
 * Represents the return type of the `useArrayBuffer` hook.
 */
export type UseArrayBufferResponse = {
  arrayBuffer: ArrayBuffer | null;
  loading: boolean;
};

/**
 * A custom React hook to read a file and return its content as an ArrayBuffer.
 *
 * @param file The file to be read. Can be `null` if no file is selected.
 * @returns An object containing the ArrayBuffer result and loading state.
 *
 * @example
 * const { arrayBuffer, loading } = useArrayBuffer(file);
 */
export const useArrayBuffer = (file: File | null): UseArrayBufferResponse => {
  const [arrayBuffer, setArrayBuffer] = useState<ArrayBuffer | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (!file) {
      setArrayBuffer(null);
      setLoading(false);
      return;
    }

    const reader = new FileReader();

    // On load, set the array buffer and loading state
    const onLoad = (event: ProgressEvent<FileReader>) => {
      setArrayBuffer(event.target?.result as ArrayBuffer);
      setLoading(false);
    };

    // On error, log the error and set loading state to false
    const onError = (error: ProgressEvent<FileReader>) => {
      console.error('Error reading file:', error);
      setLoading(false);
    };

    setLoading(true);
    reader.addEventListener('load', onLoad);
    reader.addEventListener('error', onError);
    reader.readAsArrayBuffer(file);

    // Cleanup function
    return () => {
      reader.removeEventListener('load', onLoad);
      reader.removeEventListener('error', onError);
      // Abort reading if necessary
      if (reader.readyState === reader.LOADING) {
        reader.abort();
      }
    };
  }, [file]);

  return { arrayBuffer, loading };
};
