'use client';

import { isBrowser, LocalStorageManager } from '@pdfily/shared/utils';
import { useCallback, useEffect, useState } from 'react';

// configure this as you wish
const LAST_EDIT_FILE_KEY = 'last-edit-file';

export function useLastEditFile() {
  const [lastEditFile, setLastEditFile] = useState<string | null>(null);

  // Run only on the client
  useEffect(() => {
    const storedFile = getLastEditFileFromLocalStorage();
    setLastEditFile(storedFile);
  }, []);

  const setFile = useCallback((file: string) => {
    setLastEditFile(file);
    storeLastEditFileInLocalStorage(file);
  }, []);

  const clearFile = useCallback(() => {
    setLastEditFile(null);
    removeLastEditFileFromLocalStorage();
  }, []);

  return {
    lastEditFile,
    setFile,
    clearFile,
  };
}

/**
 * Stores the last edited file ID in localStorage.
 *
 * This function ensures that localStorage is accessed only in the browser environment.
 * If the provided file ID is `null` or `undefined`, it removes the key from localStorage.
 *
 * @param {string | null} file - The file ID to store in localStorage.
 * @returns {void} This function does not return anything.
 */
function storeLastEditFileInLocalStorage(file: string | null): void {
  if (!isBrowser()) {
    return;
  }

  if (file) {
    LocalStorageManager.setItem(LAST_EDIT_FILE_KEY, file);
  } else {
    LocalStorageManager.removeItem(LAST_EDIT_FILE_KEY);
  }
}

/**
 * Retrieves the last edited file ID from localStorage.
 *
 * Ensures that localStorage is accessed only in the browser environment.
 * If the key is not found in localStorage, it returns `null`.
 *
 * @returns {string | null} The last edited file ID if found, otherwise `null`.
 */
function getLastEditFileFromLocalStorage(): string | null {
  if (!isBrowser()) {
    return null;
  }

  return LocalStorageManager.getItem(LAST_EDIT_FILE_KEY);
}

/**
 * Removes the last edited file ID from localStorage.
 *
 * Ensures that localStorage is accessed only in the browser environment.
 * If the key does not exist in localStorage, the function performs no action.
 *
 * @returns {void} This function does not return anything.
 */
function removeLastEditFileFromLocalStorage(): void {
  if (!isBrowser()) {
    return;
  }

  LocalStorageManager.removeItem(LAST_EDIT_FILE_KEY);
}

