import { z } from 'zod';
import PaymentConfigSchema from './schemas/payment-config.schema';

export type PaymentConfig = z.infer<typeof PaymentConfigSchema>;

const paymentConfig: PaymentConfig = PaymentConfigSchema.parse({
  apiKey: process.env.STRIPE_SECRET_KEY,
  webhookSecret: process.env.STRIPE_WEBHOOK_SIGNING_SECRET,
  options: {
    publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
    apiVersion: '2025-04-30.basil',
  },
});

export default paymentConfig;
