import type { NextRequest } from 'next/server';

/**
 * Returns CORS headers with dynamic origin handling.
 *
 * @param request - Next.js request object
 * @returns A record of CORS headers
 */
export const getCorsHeaders = (request: NextRequest): Record<string, string> => {
  const origin = request.headers.get('origin') || '*';

  return {
    'Access-Control-Allow-Origin': origin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': request.headers.get('access-control-request-headers') || '*',
    'Access-Control-Max-Age': '86400',
  };
};