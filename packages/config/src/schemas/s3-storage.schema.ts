import { z } from "zod";

/**
 * Defines the shape of Supabase S3 storage config and validates required fields.
 */
export const S3StorageConfigSchema = z.object({
  accessKeyId: z
    .string({
      required_error: "S3_ACCESS_KEY is required",
      description: "AWS/S3 Access Key ID for authentication.",
    })
    .trim()
    .min(1, { message: "S3_ACCESS_KEY must not be empty" }),

  secretAccessKey: z
    .string({
      required_error: "S3_SECRET_KEY is required",
      description: "AWS/S3 Secret Access Key for authentication.",
    })
    .trim()
    .min(1, { message: "S3_SECRET_KEY must not be empty" }),

  endpointUrl: z
    .string({
      required_error: "S3_HOST is required",
      description: "The endpoint URL for Supabase S3-compatible storage.",
    })
    .trim()
    .url({ message: "S3_HOST must be a valid URL" }),

  region: z
    .string({
      required_error: "S3_REGION is required",
      description: "The AWS region where the S3 storage is hosted.",
    })
    .trim()
    .min(1, { message: "S3_REGION must not be empty" }),

  bucket: z
    .string({
      description: "The bucket name where files will be stored.",
    })
    .trim()
    .min(1, { message: "S3_BUCKET must not be empty" })
    .optional(), // ✅ Now optional
});

export default S3StorageConfigSchema;
