import { Instance as NutrientInstance } from '@nutrient-sdk/viewer';
import { WebViewerInstance as PDFTronInstance } from '@pdftron/webviewer';
import { RefObject } from 'react';

/**
 * Common props for both PDF viewers (Nutrient & PDFTron).
 */
export interface UseViewerKitProps {
  source: string | null;
  documentId?: string | null;
  locale?: string;
  isUndoRedo?: boolean;
  setIsUndoRedo?: React.Dispatch<React.SetStateAction<boolean>>;
}

/**
 * Common viewer ref + loading state (generic over instance type).
 */
export interface UseViewerKitResponse<T> {
  viewer: RefObject<HTMLDivElement | null>;
  instance: T | null;
  loading: boolean;
}

// Type aliases for each specific viewer
export type UsePDFTronKitResponse = UseViewerKitResponse<PDFTronInstance>;
export type UsePSPDFKitResponse = UseViewerKitResponse<NutrientInstance>;
