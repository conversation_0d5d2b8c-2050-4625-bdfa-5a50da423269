'use client';

import type { User } from '@supabase/supabase-js';
import { useSearchParams } from 'next/navigation';
import { useLeavePageConfirmation } from '@pdfily/shared/hooks';
import appConfig, { PDF_EDITOR } from '@pdfily/config/app.config';
import { If } from '@pdfily/ui/if';
import PDFTronWebViewer from './pdf-tron-web-viewer';
import NutrientWebViewer from './pdf-nutrient-web-viewer';
import { UseViewerKitProps } from '../types/viewer-kit.types';

interface PDFViewerRendererContainerProps extends UseViewerKitProps {
  user: User;
}

/**
 * PDFViewerRendererContainer is a wrapper component that renders both
 * the Nutrient and PDFTron viewers within a full-screen layout.
 * It also enables a confirmation prompt when the user tries to leave the page.
 *
 * @param {UseViewerKitProps} props - Viewer props including source, documentId, and optional locale.
 * @param {string | ArrayBuffer | null} props.source - The document source (URL or binary).
 * @param {string | null} props.documentId - Unique document identifier (if any).
 * @param {string} [props.locale] - Optional locale/language setting.
 * @param {User} props.user - The user object from Supabase.
 */
export default function PDFViewerRendererContainer({
  source,
  documentId,
  locale,
  user,
}: PDFViewerRendererContainerProps) {
  const searchParams = useSearchParams();
  const editorParam = searchParams.get('editor');

  // Determine the PDF editor type (query param or fallback to config)
  const storedEditor = typeof window !== 'undefined' ? localStorage.getItem('selectedEditor') : null;

  const viewerType = Object.values(PDF_EDITOR).includes(editorParam as PDF_EDITOR)
    ? (editorParam as PDF_EDITOR)
    : Object.values(PDF_EDITOR).includes(storedEditor as PDF_EDITOR)
      ? (storedEditor as PDF_EDITOR)
      : appConfig.pdfEditor;

  // Prompt before leaving
  useLeavePageConfirmation(true);

  return (
    <div className="relative w-full">
      <If condition={viewerType === PDF_EDITOR.NUTRIENT}>
        <NutrientWebViewer source={source} documentId={documentId} locale={locale} user={user} />
      </If>

      <If condition={viewerType === PDF_EDITOR.PDFTRON}>
        <PDFTronWebViewer source={source} documentId={documentId} locale={locale} user={user} />
      </If>

      <If condition={!viewerType}>
        <div className="flex items-center justify-center h-full text-red-500">
          <p>❌ No PDF editor configured. Please set NEXT_PUBLIC_PDF_EDITOR in your environment.</p>
        </div>
      </If>
    </div>
  );
}
