# @pdfily/config

Centralized configuration schemas and files for the PDFily monorepo.

## Installation

Using pnpm:

```sh
pnpm add @pdfily/config
```

Or with npm:

```sh
npm install @pdfily/config
```

Or with yarn:

```sh
yarn add @pdfily/config
```

## Usage

Import config objects or schemas from this package in your other packages:

```js
import { appConfig } from '@pdfily/config/app.config';
```

## Development

- Run `pnpm lint` to lint the code.
- Run `pnpm format` to format the code with Prettier.
- Run `pnpm typecheck` to check types.
