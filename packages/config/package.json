{"name": "@pdfily/config", "private": true, "version": "0.1.0", "description": "Centralized configuration schemas and files for the PDFily monorepo.", "type": "module", "scripts": {"format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\""}, "dependencies": {"zod": "^3.25.76", "@pdfily/shared": "workspace:*"}, "devDependencies": {"@pdfily/prettier-config": "workspace:*", "@pdfily/typescript-config": "workspace:*", "@types/node": "^22.16.4", "typescript": "5.8.3"}, "exports": {"./app.config": "./src/app.config.ts", "./auth.config": "./src/auth.config.ts", "./paths.config": "./src/paths.config.ts", "./s3-config": "./src/s3-storage.config.ts", "./sendgrid.config": "./src/sendgrid.config.ts", "./posthog.config": "./src/posthog.config.ts", "./payment.config": "./src/payment.config.ts", "./google-analytics.config": "./src/google-analytics.config.ts", "./schemas": "./src/schemas/index.ts"}, "turbo": {"pipeline": {"build": {"outputs": []}}}, "typesVersions": {"*": {"*": ["src/*"]}}, "prettier": "@pdfily/prettier-config"}