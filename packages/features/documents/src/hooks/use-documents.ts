import { useState, useEffect, useRef, useCallback } from "react";
import { useSupabase } from "@pdfily/supabase/hooks/use-supabase";
import { Database } from "@pdfily/supabase/database";

type Document = Pick<
  Database['public']['Tables']['documents']['Row'],
  'id' | 'title' | 'type' | 'size' | 'last_modified' | 'internal_type'
>;

/**
 * Custom hook to fetch documents for a given user ID.
 *
 * @param userId - The ID of the user whose documents are to be fetched.
 * @returns An object containing documents, loading state, error message, and a method to refresh documents.
 */
export const useDocuments = (userId: string) => {
  const supabase = useSupabase();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Ref to track initial load
  const isMounted = useRef(false);

  /**
   * Fetches documents from the Supabase database.
   */
  const fetchDocuments = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("documents")
        .select("id, title, type, last_modified, size, internal_type")
        .eq("user_id", userId);

      if (error) {
        throw new Error(`Error while fetching documents: ${error.message}`);
      }

      setDocuments(data || []);
    } catch (err: any) {
      setError(err.message || "Failed to fetch documents");
      console.error(`Error while fetching documents: ${err}`);
    } finally {
      setLoading(false);
    }
  }, [userId, supabase]);

  useEffect(() => {
    // Prevent double call on mount
    if (!isMounted.current) {
      isMounted.current = true;
      fetchDocuments();
    }
  }, [fetchDocuments]);

  /**
   * Refreshes the document list by calling the fetchDocuments method.
   */
  const refreshDocuments = useCallback(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  return { documents, loading, error, refreshDocuments };
};
