-- ===================================
-- Modify the subscriptions table safely
-- ===================================

-- 0. Drop the old index that depends on customer_id
drop index if exists public.idx_subscriptions_provider_customer;

-- 1. Rename 'customer_id' to 'stripe_customer_id' (keep existing data safe)
alter table if exists public.subscriptions
rename column customer_id to stripe_customer_id;

-- 2. Make sure 'stripe_customer_id' is nullable (drop NOT NULL constraint)
alter table if exists public.subscriptions
alter column stripe_customer_id drop not null;

-- 3. Add new fields (all are nullable by default)
alter table if exists public.subscriptions
add column if not exists has_paid_trial boolean default false,
add column if not exists currency text, -- e.g. 'USD', 'EUR', 'INR'
add column if not exists next_billing_at timestamp with time zone,
add column if not exists solidgate_customer_id text;

-- 4. Create new indexes for new fields
create index if not exists idx_subscriptions_provider_stripe_customer
    on public.subscriptions (provider, stripe_customer_id);

create index if not exists idx_subscriptions_provider_solidgate_customer
    on public.subscriptions (provider, solidgate_customer_id);
