'use client';

import type { User } from '@supabase/supabase-js';
import { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@pdfily/ui/button';
import { cn } from '@pdfily/ui/utils';
import { AppLogo } from '@/components/app-logo';
import { SiteHeaderAccountSection } from '@/components/layout/site-header-account-section';
import HamburgerButton from '@/components/layout/header/buttons/hamburger-button';
import ContactButton from '@/components/layout/header/buttons/contact-button';
import MyDocumentButton from '@/components/layout/header/buttons/my-document-button';
import { useAuth } from '@/components/session-provider';
import { Link } from '@/lib/i18n/navigation';
import { useTranslations } from 'next-intl';

interface SiteHeaderProps {
  user?: User | null;
}

export default function SiteHeader({ user }: SiteHeaderProps) {
  const t = useTranslations('Header');
  const { isLoading } = useAuth();
  const [isAuthenticated, setIsAuthenticated] = useState(!!user);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    setIsAuthenticated(!!user);
  }, [user]); // Re-run the effect whenever user changes

  useEffect(() => {
    const setScrollLock = (locked: boolean) => {
      const value = locked ? 'hidden' : '';
      document.documentElement.style.overflow = value;
      document.body.style.overflow = value;
    };

    setScrollLock(mobileMenuOpen);
    return () => setScrollLock(false);
  }, [mobileMenuOpen]);

  return (
    <header
      className={cn(
        'flex h-full w-full items-center justify-between',
        'font-onest text-[16px] font-medium leading-5 tracking-normal',
      )}
    >
      <div className={cn('flex items-center gap-[46px]', 'tablet:gap-[26px]', 'final:gap-[26px]')}>
        <AppLogo href="/" />
        {/* <ToolDropdown /> */}
      </div>

      <div className="flex items-center gap-10">
        <div className="flex items-center gap-9">
          {isLoading ? null : <MyDocumentButton />}
          <ContactButton />
        </div>

        {isLoading ? null : <SiteHeaderAccountSection user={user ?? null} />}
        <HamburgerButton onClick={() => setMobileMenuOpen(true)} />
      </div>

      {/* Mobile Menu Overlay */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 z-50 flex flex-col gap-8 bg-white">
          <div
            className={cn(
              'small:h-[52px]',
              'final:h-[52px]',
              'border-b border-b-[#E7E7E7]',
              'flex items-center justify-between p-4',
              'font-onest text-[16px] font-medium leading-5 tracking-normal',
            )}
          >
            <div className={cn('flex items-center gap-[46px]', 'tablet:gap-[26px]', 'final:gap-[26px]')}>
              <AppLogo href="/" />
              {/* <ToolDropdown /> */}
            </div>

            <Button variant="ghost" className="p-0 hover:bg-transparent" onClick={() => setMobileMenuOpen(false)}>
              <X className="h-[26px] w-[26px]" />
            </Button>
          </div>

          <div className="flex flex-col px-4">
            {isAuthenticated ? null : (
              <Button
                variant="outline"
                className={cn(
                  'mb-5 h-12 w-full rounded-[10px] border border-[#F0401D] text-[#F0401D] hover:bg-red-50 hover:text-red-500',
                  'font-onest text-[16px] font-medium leading-5 tracking-normal',
                )}
                onClick={() => setIsAuthenticated(true)}
              >
                {t('signInButton')}
              </Button>
            )}

            {isAuthenticated ? (
              <>
                <Link
                  href="/my-documents"
                  className="-mt-[18px] py-[18px] font-onest text-xl font-medium leading-7 tracking-normal text-[#1C1C1C]"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {t('myDocumentsButton')}
                </Link>

                <div className="h-px w-full bg-gray-200"></div>

                <Link
                  href="/settings"
                  className="py-[18px] font-onest text-xl font-medium leading-7 tracking-normal text-[#1C1C1C]"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {t('settings')}
                </Link>
                <div className="h-px w-full bg-gray-200"></div>
              </>
            ) : null}

            <Link
              href="/contact-us"
              className="py-[18px] font-onest text-xl font-medium leading-7 tracking-normal text-[#1C1C1C]"
              onClick={() => setMobileMenuOpen(false)}
            >
              {t('contactUsButton')}
            </Link>

            <div className="h-px w-full bg-gray-200"></div>

            {isAuthenticated ? (
              <Button
                variant="outline"
                className={cn(
                  'mb-5 h-12 w-[calc(100%-2rem)] rounded-[10px] border border-[#E7E7E7] text-[#1C1C1C] hover:bg-gray-50 hover:text-gray-500',
                  'font-onest text-[16px] font-medium leading-5 tracking-normal',
                  'fixed bottom-5',
                )}
                onClick={() => {
                  setIsAuthenticated(false);
                  setMobileMenuOpen(false);
                }}
              >
                {t('logout')}
              </Button>
            ) : null}
          </div>
        </div>
      )}
    </header>
  );
}
