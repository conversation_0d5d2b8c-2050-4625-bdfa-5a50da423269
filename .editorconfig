# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# TypeScript and JavaScript files
[*.{ts,tsx,js,jsx}]
indent_size = 2
max_line_length = 120

# JSON files
[*.json]
indent_size = 2
max_line_length = 120

# YAML files
[*.{yml,yaml}]
indent_size = 2
max_line_length = 120

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# CSS, SCSS, and other style files
[*.{css,scss,sass,less}]
indent_size = 2
max_line_length = 120

# HTML files
[*.html]
indent_size = 2
max_line_length = 120

# Shell scripts
[*.sh]
indent_size = 2

# Package files
[package.json]
indent_size = 2

# Lock files
[pnpm-lock.yaml]
indent_size = 2

# Configuration files
[*.{toml,ini}]
indent_size = 2
