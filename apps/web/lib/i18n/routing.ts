import { defineRouting } from 'next-intl/routing'
import { locales, defaultLocale, getLocalePrefix } from './locales'

/**
 * Routing configuration for the app.
 */
export const routing = defineRouting({
  // A list of all locales that are supported
  locales,

  // Used when no locale matches
  defaultLocale,

  // Configurable locale prefix strategy via environment variable
  localePrefix: getLocalePrefix()
})
