import { useEffect, useState } from 'react';
import { Instance } from '@nutrient-sdk/viewer';
import { getInitialViewState } from '../lib/nutrient-viewer/initial-view-state';
import { UseViewerKitProps, UsePSPDFKitResponse } from '../types/viewer-kit.types';
import { usePDFViewerBase } from './use-pdf-viewer-base';
import { getCustomizedToolbarItems } from '../lib/nutrient-viewer/toolbar-items';
import appConfig from '@pdfily/config/app.config';

// Utility to detect if it's desktop
const isDesktop = (): boolean => {
  if (typeof window === 'undefined') return true;
  return window.innerWidth >= 768;
};

/**
 * Loads PSPDFKit/Nutrient Viewer into the container.
 *
 * @param container - HTML div where the viewer will be rendered.
 * @param document - Document URL or binary.
 * @param isDesktopMode - Boolean indicating if we're in desktop mode.
 * @returns Promise of PSPDFKit instance or undefined.
 */
export const loadPSPDFKit = async (
  container: HTMLDivElement | null,
  document: string | ArrayBuffer,
  locale: string = 'en',
  isDesktopMode: boolean = true,
): Promise<Instance | undefined> => {
  if (!container || typeof window === 'undefined' || !window.NutrientViewer) {
    console.warn('Cannot load PSPDFKit: Invalid container or NutrientViewer not available');
    return undefined;
  }

  const PSPDFKit = window.NutrientViewer;

  PSPDFKit.unload(container);
  await new Promise((resolve) => setTimeout(resolve, 0));

  const initialViewState = getInitialViewState(PSPDFKit);
  const origin = window.location.origin;
  const styleBasePath = `${origin}/styles/pspdfkit`;

  const toolbarItems = await getCustomizedToolbarItems();
  const filteredToolbarItems = isDesktopMode
    ? toolbarItems
    : toolbarItems.filter((item) => item.id !== 'undo' && item.id !== 'redo');

  try {
    PSPDFKit.Options.ANNOTATION_TOOLBAR_RESPONSIVE_BREAKPOINT = 20;
    const instance = await PSPDFKit.load({
      licenseKey: appConfig.isNutrientLicensedEnabled ? appConfig.nutrientLicenceKey : '',
      locale,
      container,
      document,
      initialViewState,
      toolbarItems: filteredToolbarItems,
      baseUrl: `${origin}/lib/nutrient-viewer/`,
      styleSheets: [
        `${styleBasePath}/global.css`,
        `${styleBasePath}/custom.css`,
        `${styleBasePath}/tool-bar.css`,
        `${styleBasePath}/side-bar.css`,
        `${styleBasePath}/viewport.css`,
        `${styleBasePath}/annotation-tool-bar.css`,
      ],
      enableClipboardActions: true,
      enableHistory: true,
      toolbarPlacement: isDesktopMode ? PSPDFKit.ToolbarPlacement.TOP : PSPDFKit.ToolbarPlacement.BOTTOM,
      customUI: {
        [PSPDFKit.UIElement.Sidebar]: {
          [PSPDFKit.SidebarMode.ANNOTATIONS]: ({ containerNode }) => {
            const customDeleteButton = window.document.createElement('button');
            customDeleteButton.addEventListener('click', () => {
              const deleteButton = (containerNode as HTMLDivElement).querySelector<HTMLButtonElement>(
                '.PSPDFKit-Sidebar-Annotations-Delete',
              );
              if (deleteButton) deleteButton?.click();
            });

            return {
              node: containerNode,
              onRenderItem: ({ itemContainerNode }) => {
                const lastChild = itemContainerNode.lastChild;
                if (lastChild?.nodeName === 'DIV') {
                  itemContainerNode.appendChild(customDeleteButton);
                }
              },
            };
          },
        },
      },
    });

    return instance;
  } catch (error: any) {
    console.error('Error loading PSPDFKit:', error, { container, document, locale, isDesktopMode });
    throw new Error(`Error while loading PSPDFKit: ${error.message}`);
  }
};

/**
 * React hook to initialize and manage PSPDFKit viewer.
 */
const usePSPDFKit = ({ source, locale, isUndoRedo, setIsUndoRedo }: UseViewerKitProps): UsePSPDFKitResponse => {
  const { viewer, instance, setInstance, loading, setLoading } = usePDFViewerBase<Instance | null>();
  const [desktopMode, setDesktopMode] = useState(isDesktop());

  // Watch for resize events to detect desktop/mobile mode
  useEffect(() => {
    const handleResize = () => {
      const nowDesktop = isDesktop();
      setDesktopMode((prev) => {
        if (prev !== nowDesktop) return nowDesktop;
        return prev;
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    setLoading(true);
    const container = viewer.current;

    if (!source || !container) {
      setLoading(false);
      return;
    }

    loadPSPDFKit(container, source, locale, desktopMode)
      .then((instance) => {
        if (instance) {
          instance.addEventListener('history.willChange', () => {
            setIsUndoRedo?.((prev) => {
              const updated = !prev;
              return updated;
            });
          });
          // Remove the 'Save as' button from the Document Editor
          // This prevents users from saving modified files without going through the subscription flow
          instance.setDocumentEditorFooterItems((items) => items.filter((item) => item.type !== 'save-as'));

          setInstance(instance);
          console.log('PSPDFKit was initialized successfully.');
        }
      })
      .finally(() => {
        setLoading(false);
      });

    return () => {
      if (container && typeof window !== 'undefined' && window.NutrientViewer) {
        window.NutrientViewer.unload(container);
      }
    };
  }, [source, desktopMode]);

  return { viewer, instance, loading };
};

export default usePSPDFKit;
