import { useMemo } from 'react';
import axios from 'axios';
import s3Config from '@pdfily/config/s3-config';
import { getFileType, getPdfPageCount, sanitizeFileName } from '@pdfily/shared/utils';
import { useSupabase } from './use-supabase';
import { getPresignedUploadUrl } from '../lib/s3/get-presigned-upload-url';
import { Database } from '../database.types';

type DocumentUpdate = Database['public']['Tables']['documents']['Update'];

/**
 * Represents a structured error response object.
 *
 * @interface ErrorResponse
 */
export interface UploadErrorResponse {
  error: any;
  message: string;
  statusCode: number;
  timestamp: string;
}

export interface UploadSuccessResponse {
  documentId: string;
  file: {
    file_name: string | undefined;
    file_key: string | null | undefined;
    file_size: number | null | undefined;
    file_url: string | null;
  };
}

export type UploadResponse = UploadSuccessResponse | UploadErrorResponse;

interface UploadEditFileOptionsPros {
  onSuccess?: (success: UploadSuccessResponse) => void;
  onError?: (error: UploadErrorResponse) => void;
}

/**
 * React hook to update (replace) an existing file in Supabase S3 and update its metadata.
 *
 * @param options - Upload and progress options
 * @returns uploadEditFile - Function to upload and replace an existing file
 */
/**
 * React hook to update (replace) an existing file in Supabase Storage and update its metadata.
 */
export function useUploadFileByDocument({ onSuccess, onError }: UploadEditFileOptionsPros) {
  const supabase = useSupabase();

  const uploadFile = useMemo(
    () =>
      async (file: Blob | undefined, documentId: string): Promise<UploadResponse> => {
        if (!file) {
          const errorResponse = {
            message: 'No file provided for upload.',
            error: null,
            statusCode: 400,
            timestamp: new Date().toISOString(),
          };

          onError?.(errorResponse);
          return errorResponse;
        }

        // Get user details if authenticated ===
        const { data: { user } = {} } = await supabase.auth.getUser();

        if (!user) {
          const errorResponse = {
            message: 'Failed to upload and edit document',
            error: 'User not authenticated',
            statusCode: 401,
            timestamp: new Date().toISOString(),
          };

          onError?.(errorResponse);
          return errorResponse;
        }

        try {
          // Get existing document metadata
          const { data: document, error: fetchError } = await supabase
            .from('documents')
            .select('*')
            .eq('id', documentId)
            .single();

          if (fetchError || !document) {
            const errorResponse = {
              message: 'Failed to fetch document metadata.',
              error: fetchError?.message,
              statusCode: 500,
              timestamp: new Date().toISOString(),
            };

            onError?.(errorResponse);
            return errorResponse;
          }

          const bucket = s3Config.bucket ?? 'uploads';
          const sanitizedFileName = sanitizeFileName(document.name);
          const fileKey = `documents/${documentId}/${Date.now()}-${sanitizedFileName}`;

          // Generate signed file URL ===
          const preSignedUrl = await getPresignedUploadUrl(bucket, fileKey);

          if (!preSignedUrl) {
            const errorResponse = {
              message: 'Failed to upload and edit document',
              error: 'Failed to generate pre-signed URL',
              statusCode: 500,
              timestamp: new Date().toISOString(),
            };

            onError?.(errorResponse);
            return errorResponse;
          }

          // Upload to S3 using pre-signed URL and Axios ===
          const uploadResponse = await axios.put(preSignedUrl, file, {
            headers: {
              'Content-Type': file.type,
              'Cache-Control': 'max-age=3600',
              Accept: 'application/json',
            },
          });

          if (uploadResponse.status !== 200) {
            const errorResponse = {
              message: 'Failed to upload file to S3',
              error: uploadResponse.statusText,
              statusCode: 500,
              timestamp: new Date().toISOString(),
            };

            onError?.(errorResponse);
            return errorResponse;
          }

          // Extract metadata ===
          const pageCount = await getPdfPageCount(await file.arrayBuffer());
          const fileType = getFileType(file.type);

          // Update document metadata in Supabase DB
          const updatedMetadata: DocumentUpdate = {
            name: document.name,
            user_id: user?.id,
            edited_key: fileKey,
            size: file.size,
            type: fileType,
            format: fileType.toUpperCase(),
            page_count: pageCount,
            is_edited: true,
            last_modified: new Date().toISOString(),
          };

          // Save metadata to Supabase DB ===
          const { error: updateError } = await supabase.from('documents').update(updatedMetadata).eq('id', documentId);

          if (updateError) {
            const errorResponse = {
              message: 'Failed to update document metadata.',
              error: updateError?.message,
              statusCode: 500,
              timestamp: new Date().toISOString(),
            };

            onError?.(errorResponse);
            return errorResponse;
          }

          const response: UploadSuccessResponse = {
            documentId,
            file: {
              file_name: updatedMetadata.name,
              file_key: updatedMetadata.edited_key,
              file_size: updatedMetadata.size,
              file_url: preSignedUrl,
            },
          };

          if (onSuccess) {
            onSuccess?.(response);
          }

          return response;
        } catch (error: any) {
          const errorResponse = {
            message: 'Internal server error while editing document.',
            error: error?.message,
            statusCode: 500,
            timestamp: new Date().toISOString(),
          };

          onError?.(errorResponse);
          return errorResponse;
        }
      },
    [supabase, onSuccess, onError],
  );

  return uploadFile;
}
