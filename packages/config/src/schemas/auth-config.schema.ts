import { z } from 'zod';

const AuthConfigSchema = z.object({
  // CAPTCHA configuration
  captchaTokenSiteKey: z
    .string({
      description: 'The reCAPTCHA site key.',
    })
    .optional(),

  captchaEnabled: z
    .boolean({
      description: 'Whether CAPTCHA verification is enabled',
    })
    .optional(),

  // Google Auth configuration
  googleAuthEnabled: z
    .boolean({
      description: 'Wether the Google Authentication is enabled',
    })
    .optional(),
});

export default AuthConfigSchema;
