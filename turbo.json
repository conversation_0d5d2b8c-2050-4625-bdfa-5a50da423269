{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*", "!README.md"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"], "env": ["SUPABASE_PROJECT_REF", "SUPABASE_SERVICE_ROLE_KEY", "S3_HOST", "S3_REGION", "S3_ACCESS_KEY", "S3_SECRET_KEY", "S3_BUCKET"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "spell-check": {"inputs": ["**/*.md", "**/*.mdx"]}}}