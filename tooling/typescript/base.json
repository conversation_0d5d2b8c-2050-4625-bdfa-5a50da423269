{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "es2022", "module": "esnext", "moduleResolution": "bundler", "lib": ["dom", "dom.iterable", "esnext"], "jsx": "preserve", "allowJs": true, "resolveJsonModule": true, "incremental": true, "isolatedModules": true, "esModuleInterop": true, "strict": true, "noUncheckedIndexedAccess": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "noEmit": true}, "exclude": ["node_modules", "build", "dist", ".next"]}