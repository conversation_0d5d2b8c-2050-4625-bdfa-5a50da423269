import { z } from "zod";

/**
 * Defines the shape of your paths config and validates
 */
export const PathsConfigSchema = z.object({
  auth: z.object({
    signIn: z.string().min(1),
    signUp: z.string().min(1),
    forgotPassword: z.string().min(1),
    resetPassword: z.string().min(1),
    callback: z.string().min(1),
  }),
  app: z.object({
    home: z.string().min(1),
    profileSettings: z.string().min(1),
  }),
});

export default PathsConfigSchema;
