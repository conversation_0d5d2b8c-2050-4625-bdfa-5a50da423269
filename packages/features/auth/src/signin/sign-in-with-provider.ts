import Swal from 'sweetalert2';
import { ProviderType } from '@pdfily/supabase/types';

/**
 * Initiates sign-in with the specified OAuth provider by sending a POST request to the server.
 *
 * This function sends the selected provider and redirect path to the API route `/api/auth/sign-in-with-provider`,
 * then redirects the user to the provider's authentication page if successful.
 *
 * If the request fails or the response does not include a redirect URL, it displays an error using SweetAlert.
 *
 * @param {ProviderType} provider - The name of the OAuth provider (e.g., 'google', 'github')
 * @param {string} path - The path to redirect to after successful authentication
 */
export const signInWithProviderAction = async (provider: ProviderType, path: string) => {
  try {
    const response = await fetch('/api/auth/sign-in-with-provider', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ provider, path }),
    });

    const data = await response.json();

    if (data.url) {
      window.location.href = data.url;
    } else {
      throw new Error(data.error || 'Authentication failed');
    }
  } catch (error) {
    console.error('Google sign-in error:', error);
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: `Failed to sign in with ${provider}. Please try again.`,
    });
  }
};
