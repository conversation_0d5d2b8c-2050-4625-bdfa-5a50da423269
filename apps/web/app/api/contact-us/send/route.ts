import { NextRequest, NextResponse } from 'next/server';
import sgMail, { MailDataRequired } from '@sendgrid/mail';
import { ContactFormSchema } from '@pdfily/config/schemas';
import sendGridConfigs from '@pdfily/config/sendgrid.config';

// Get Sendgrid configs
const { apiKey, senderEmail, supportReceiverEmail, contactFormTemplateId } = sendGridConfigs;

sgMail.setApiKey(apiKey);

/**
 * Handles POST requests for the contact form.
 *
 * - Validates the incoming JSON body using the ContactFormSchema (Zod).
 * - Sends an email using SendGrid if validation passes.
 *
 * @param {NextRequest} request - The incoming HTTP request from Next.js.
 * @returns {Promise<NextResponse>} - A JSON response indicating success or failure.
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // validate the request body
    const body = await request.json();
    const validationResult = ContactFormSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Bad Request',
          message: 'Invalid input data provided.',
          allowedFields: ['email', 'name', 'message'],
          details: validationResult.error.errors,
          statusCode: 400,
          timestamp: new Date().toISOString(),
        },
        { status: 400 },
      );
    }

    // Extract validated data
    const { name, email, message } = validationResult.data;

    // Prepare the email using the dynamic SendGrid template
    const msg: MailDataRequired = {
      to: supportReceiverEmail,
      replyTo: { email },
      from: senderEmail,
      templateId: contactFormTemplateId,
      dynamicTemplateData: {
        name,
        email,
        message,
      },
    };

    // Send the email
    await sgMail.send(msg);

    return NextResponse.json({ message: 'Message sent successfully', error: null, statusCode: 200 }, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        message: 'Internal Server Error',
        error: 'Internal Server Error',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
