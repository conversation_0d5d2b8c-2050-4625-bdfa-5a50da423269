'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useCallback, useMemo, useState } from 'react';
import Swal from 'sweetalert2';

import { stringifyObject } from '@pdfily/shared/utils';
import { cn } from '@pdfily/ui/utils';
import { useLastEditFileId } from '@pdfily/documents/hooks/use-last-edit-file-id';
import { useLastEditFile } from '@pdfily/documents/hooks/use-last-edit-file';
import { useUploadFile } from '@pdfily/supabase/hooks/use-upload-file';

import FileUploadBox from '@/components/landing-page/file-upload-box';
import FileUploadMobile from '@/components/landing-page/file-upload-mobile';
import ProgressModal from '@/components/progress-modal';
import { useRouter } from '@/lib/i18n/navigation';

const items = ['writting', 'signing', 'form'];

const overall = {
  overallRating: 4.5,
  overallReviews: 125,
};

export default function BannerSection() {
  const router = useRouter();
  const t = useTranslations('HomePage');
  const [progress, setProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const { setFileId } = useLastEditFileId();
  const { setFile } = useLastEditFile();

  /**
   * Handles upload errors (memoized)
   */
  const handleError = useCallback((message: string) => {
    console.log(`Error while uploading file: ${message}`);
  }, []);

  /**
   * Upload file to API (memoized)
   */
  const uploadFile = useUploadFile({
    // ✅ tracks the upload %
    setProgress: (percent) => {
      setProgress(percent);
    },
    // ✅ sets the document ID
    setDocumentId: (documentId) => {
      setFileId(documentId);
    },
    // ✅ sets the document object
    onSuccess: (document) => {
      setFile(stringifyObject(document));
      router.push('/document/create');
    },
    // ✅ handles upload errors
    onError: (error) => {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error?.message || 'Failed to upload file.',
      });
    },
  });

  /**
   * Handles file selection and upload (memoized)
   */
  const handleFilesSelected = useMemo(() => {
    return async (files: File[]) => {
      const file = files?.[0];

      if (!file) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Error while uploading file: No file selected.',
        });
        return;
      }

      setIsUploading(true);
      setProgress(0);

      try {
        await uploadFile(file);
      } catch (error) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to upload file or missing documentId.',
        });
      } finally {
        setIsUploading(false);
      }
    };
  }, [uploadFile, setIsUploading, setProgress]);

  return (
    <section className={cn('flex items-center justify-between')}>
      <div className={cn('flex flex-col items-start gap-[34px]', 'final:w-full')}>
        <div className={cn('flex flex-col items-start', 'final:w-full')}>
          <h1
            className={cn(
              'font-onest text-[58px] font-semibold leading-[62px] tracking-[-0.05px] text-[#1C1C1C]',
              'laptop:text-[38px] laptop:leading-[42px]',
              'final:text-[38px] final:leading-[42px]',
            )}
          >
            {t.rich('bannerSection.title', {
              fast: (chunks) => <span className="text-[#F0401D]">{chunks}</span>,
            })}
          </h1>

          <p
            className={cn(
              'mt-5 font-onest text-lg font-medium leading-[26px] tracking-normal text-[#585858]',
              'laptop:mt-3 laptop:text-[16px] laptop:leading-6',
              'final:mt-3 final:text-[16px] final:leading-6',
            )}
          >
            {t.rich('bannerSection.description', {
              br: () => <br />,
            })}
          </p>

          <div className={cn('mt-8 flex flex-col gap-3', 'laptop:mt-5', 'final:mt-5')}>
            {items.map((item, index) => (
              <div key={item} className={cn('flex items-center justify-start gap-3.5', 'laptop:gap-3', 'final:gap-3')}>
                <div
                  className={cn(
                    'flex h-7 w-7 items-center justify-center rounded-full bg-[#1FC794] p-1.5',
                    'laptop:h-[22px] laptop:w-[22px]',
                    'final:h-[22px] final:w-[22px]',
                  )}
                >
                  <Image
                    src="/images/landing-page/banner-section/Check.svg"
                    alt="Tools logo"
                    width={12}
                    height={12}
                    className="object-contain"
                  />
                </div>
                <span
                  className={cn(
                    'font-onest text-lg font-normal leading-[22px] tracking-normal text-[#585858]',
                    'laptop:text-[16px] laptop:leading-5',
                    'final:text-[16px] final:leading-5',
                  )}
                >
                  {t(`bannerSection.featureItems.${item}`)}
                </span>
              </div>
            ))}
          </div>

          <FileUploadMobile onFilesSelected={handleFilesSelected} maxFileSize={50} onError={handleError} />
        </div>

        <p
          className={cn(
            'pl-[27px] font-onest text-[16px] font-normal leading-6 tracking-normal text-[#9B9A9A]',
            'laptop:hidden',
            'final:hidden',
          )}
        >
          {t('bannerSection.awardWinnng')}
        </p>

        <div className={cn('flex items-center gap-4', 'laptop:hidden', 'final:hidden')}>
          <span className="font-onest text-[36px] font-medium leading-10 tracking-[-0.05px] text-[#F0401D]">
            {overall.overallRating}
          </span>
          <div className="flex gap-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <Image
                key={star}
                src={
                  overall.overallRating >= star
                    ? '/images/landing-page/testimonials-section/StarFilled.svg'
                    : overall.overallRating >= star - 0.5
                      ? '/images/landing-page/testimonials-section/StarHalf.svg'
                      : '/images/landing-page/testimonials-section/StarEmpty.svg'
                }
                alt="Star Icon"
                width={24}
                height={24}
                className="object-contain"
              />
            ))}
          </div>
          {t.rich('bannerSection.reviews', {
            reviewtag: (chunks) => (
              <span className="font-onest text-lg font-normal leading-[20px] tracking-normal text-[#585858]">
                {chunks}
              </span>
            ),
            counttag: (chunks) => <span className="font-medium text-[#1C1C1C]">{chunks}</span>,
            count: overall.overallReviews,
          })}
        </div>
      </div>

      <FileUploadBox onFilesSelected={handleFilesSelected} maxFileSize={50} onError={handleError} />

      {isUploading && <ProgressModal isOpen={isUploading} progress={progress} onClose={() => setIsUploading(false)} />}
    </section>
  );
}
