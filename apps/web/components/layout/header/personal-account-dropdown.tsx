'use client';

import type { User } from '@supabase/supabase-js';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import pathsConfig from '@pdfily/config/paths.config';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@pdfily/ui/dropdown-menu';
import { cn, extractInitials } from '@pdfily/ui/utils';
import { Link } from '@/lib/i18n/navigation';

interface PersonalAccountDropdownProps {
  user: User
  signOutRequested: () => unknown
}

/**
 * PersonalAccountDropdown - Displays a dropdown menu with settings and logout.
 *
 * @param {PersonalAccountDropdownProps} props - The component props.
 */
function PersonalAccountDropdown({ user, signOutRequested }: PersonalAccountDropdownProps) {
  const t = useTranslations('Header');

  return (
    <div className={cn('small:hidden', 'final:hidden', 'cursor-pointer')}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          {true ? (
            <div
              className={cn(
                'flex h-[50px] w-[50px] items-center justify-center rounded-full bg-[#F0401D1A]',
                'font-onest text-xl font-medium leading-9 tracking-[-0.05px] text-[#F0401D]',
              )}
            >
              {extractInitials(user.email ?? '')}
            </div>
          ) : (
            <Image
              src={`/images/my-documents/Actions.svg`}
              alt="more actions"
              width={20}
              height={20}
              className={cn('cursor-pointer object-contain')}
            />
          )}
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className={cn(
            'flex flex-col gap-1 border-[0.5px] border-[#E7E7E7] bg-white p-2 shadow-[2px_8px_20px_0px_#4852641F]',
          )}
        >
          <DropdownMenuItem className="p-0">
            <Link href={pathsConfig.app.profileSettings} className={cn('flex items-center gap-2 px-2 py-1.5')}>
              <Image
                src={`/images/header/Settings.svg`}
                alt="open"
                width={18}
                height={18}
                className={cn('object-contain')}
              />
              <span className="font-onest text-[16px] font-medium leading-5 text-[#1C1C1C]">
                {t('settings')}
              </span>
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={signOutRequested} className="flex items-center gap-2 px-2 py-1.5">
            <Image
              src={`/images/header/Logout.svg`}
              alt="open"
              width={18}
              height={18}
              className={cn('object-contain')}
            />
            <span className="font-onest text-[16px] font-medium leading-5 text-[#1C1C1C]">
              {t('logout')}
            </span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

export default PersonalAccountDropdown
