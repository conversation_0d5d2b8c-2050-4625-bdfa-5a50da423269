'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Button } from '@pdfily/ui/button';
import { Input } from '@pdfily/ui/input';
import { Textarea } from '@pdfily/ui/textarea';
import FaqSection from '@/components/landing-page/faq-section';
import { cn } from '@pdfily/ui/utils';

export default function ContactUs() {
  const t = useTranslations('ContactPage');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear field-specific error when user starts typing
    setErrors((prev) => ({ ...prev, [name]: '' }));
    setSubmitError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});
    setSubmitError(null);
    setSubmitSuccess(false);

    try {
      const response = await fetch('/api/contact-us/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 400 && errorData.errors) {
          // Handle Zod validation errors
          const fieldErrors: { [key: string]: string } = {};
          errorData.errors.forEach((err: { path: string[]; message: string }) => {
            fieldErrors[`${err?.path[0]}`] = err.message;
          });
          setErrors(fieldErrors);
        } else {
          // Handle other errors (e.g., server errors)
          setSubmitError(errorData.message || t('form.submitError'));
        }
        setIsSubmitting(false);
        return;
      }

      // Success case
      setFormData({ name: '', email: '', message: '' });
      setSubmitSuccess(true);
    } catch (error) {
      setSubmitError(t('form.submitError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={cn('flex w-full max-w-7xl flex-col items-center', 'mac:w-full mac:max-w-full mac:px-4')}>
      <div
        className={cn(
          'flex w-full items-start justify-between pb-20 pt-[60px]',
          'desktop:flex-col desktop:justify-start desktop:gap-[26px] desktop:pb-10 desktop:pt-5',
          'final:flex-col final:justify-start final:gap-[26px] final:pb-10 final:pt-5',
        )}
      >
        <div className={cn('flex flex-col gap-5', 'final:gap-3')}>
          <h1
            className={cn(
              'font-geist text-[58px] font-semibold leading-[62px] tracking-[-0.05px] text-[#1C1C1C]',
              'desktop:text-[38px] desktop:leading-[42px]',
              'final:text-[38px] final:leading-[42px]',
            )}
          >
            {t('title')}
          </h1>
          <p
            className={cn(
              'font-mori text-lg font-normal leading-[26px] text-[#585858]',
              'desktop:text-[16px] desktop:leading-6',
              'final:text-[16px] final:leading-6',
            )}
          >
            {t.rich('description', {
              br: () => <br />,
            })}
          </p>
        </div>

        <div
          className={cn(
            'flex w-[847px] flex-col gap-6 rounded-3xl border border-[#F9F9F9] p-8 shadow-[2px_8px_14px_0px_#6881B114]',
            'desktop:w-full desktop:p-4',
            'final:w-full final:p-4',
          )}
        >
          <form onSubmit={handleSubmit} className="flex flex-col">
            <h2
              className={cn(
                'mb-6 font-onest text-2xl font-medium leading-7 tracking-[-0.05px]',
                'desktop:mb-5 desktop:text-xl desktop:leading-[26px]',
                'final:mb-5 final:text-xl final:leading-[26px]',
              )}
            >
              {t.rich('form.formTitle', {
                br: () => <br className="hidden desktop:block final:block" />,
              })}
            </h2>

            {submitSuccess && (
              <div className="mb-4 rounded-[10px] bg-green-100 p-3 text-green-800">{t('form.successMessage')}</div>
            )}
            {submitError && <div className="mb-4 rounded-[10px] bg-red-100 p-3 text-red-800">{submitError}</div>}

            <div
              className={cn(
                'mb-5 flex items-center gap-5',
                'desktop:flex-col desktop:gap-3',
                'final:flex-col final:gap-3',
              )}
            >
              <div className={cn('flex-1', 'desktop:w-full', 'final:w-full')}>
                <label htmlFor="name" className="mb-2 font-onest text-sm font-medium leading-5 text-[#1C1C1C]">
                  {t('form.nameLabelAndPlaceholder')}
                </label>
                <Input
                  id="name"
                  name="name"
                  placeholder={t('form.nameLabelAndPlaceholder')}
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className={cn(
                    'h-[50px] w-full rounded-[10px] border border-[#E7E7E7] p-4 font-onest text-[16px] font-normal leading-5 text-[#1C1C1C] placeholder:text-[#585858]',
                    errors.name && 'border-red-500',
                    'desktop:h-12',
                    'final:h-12',
                  )}
                />
                {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
              </div>

              <div className={cn('flex-1', 'desktop:w-full', 'final:w-full')}>
                <label htmlFor="email" className="mb-2 font-onest text-sm font-medium leading-5 text-[#1C1C1C]">
                  {t('form.emailLabel')}
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder={t('form.emailPlaceholder')}
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className={cn(
                    'h-[50px] w-full rounded-[10px] border border-[#E7E7E7] p-4 font-onest text-[16px] font-normal leading-5 text-[#1C1C1C] placeholder:text-[#585858]',
                    errors.email && 'border-red-500',
                    'desktop:h-12',
                    'final:h-12',
                  )}
                />
                {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
              </div>
            </div>

            <div className="mb-7">
              <label htmlFor="message" className="mb-2 font-onest text-sm font-medium leading-5 text-[#1C1C1C]">
                {t('form.messageLabel')}
              </label>
              <Textarea
                id="message"
                name="message"
                placeholder={t('form.messagePlaceholder')}
                value={formData.message}
                onChange={handleChange}
                required
                className={cn(
                  'h-40 w-full resize-none rounded-[10px] border border-[#E7E7E7] p-4 font-onest text-[16px] font-normal leading-5 text-[#1C1C1C] placeholder:text-[#585858]',
                  errors.message && 'border-red-500',
                )}
              />
              {errors.message && <p className="mt-1 text-sm text-red-500">{errors.message}</p>}
            </div>

            <Button
              type="submit"
              disabled={isSubmitting}
              className={cn(
                'flex h-[50px] w-60 items-center justify-center rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-medium leading-5 text-white hover:bg-[#d83e15]',
                'desktop:w-full',
                'final:w-full',
              )}
            >
              {isSubmitting ? t('form.loadingSubmission') : t('form.submitButton')}
            </Button>
          </form>
        </div>
      </div>

      <div className={cn('relative w-full py-20', 'final:py-10')}>
        <div
          className={cn(
            'absolute',
            'left-1/2 top-1/2 -z-10 h-full w-screen -translate-x-1/2 -translate-y-1/2 bg-[#F9F9F9]',
          )}
        ></div>
        <FaqSection showTitle={false} />
      </div>
    </div>
  );
}
