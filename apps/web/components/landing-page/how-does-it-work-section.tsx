'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useRef, type ChangeEvent } from 'react';
import { cn } from '@pdfily/ui/utils';

const items = [
  {
    image: '/images/landing-page/how-does-it-work-section/Upload.svg',
    action: 'upload',
  },
  {
    image: '/images/landing-page/how-does-it-work-section/Edit.svg',
    action: 'edit',
  },
  {
    image: '/images/landing-page/how-does-it-work-section/Download.svg',
    action: 'download',
  },
];

interface HowDoesItWorkSectionProps {
  onFilesSelected: (files: File[]) => void;
  maxFileSize?: number;
  onError?: (error: string) => void;
  accept?: string;
}

export default function HowDoesItWorkSection({
  onFilesSelected,
  maxFileSize = 50,
  onError,
  accept = '.pdf,application/pdf',
}: HowDoesItWorkSectionProps) {
  const t = useTranslations('HomePage.howDoesItWorkSection');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const acceptedTypes = accept.split(',').map((type) => type.trim().toLowerCase());
  const maxSizeBytes = maxFileSize * 1024 * 1024;

  const handleFileInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (!files.length) return;

    const validFiles = validateFiles(files);
    if (validFiles.length > 0) {
      onFilesSelected(validFiles);
    }

    // Reset the input for re-selection
    e.target.value = '';
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const validateFiles = (files: File[]): File[] => {
    const validFiles: File[] = [];
    const errors: string[] = [];

    files.forEach((file) => {
      const fileType = file.type.toLowerCase();
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      const isTypeAccepted = acceptedTypes.some(
        (type) =>
          type === fileType ||
          (type.startsWith('.') && fileExtension === type.slice(1)) ||
          (type.endsWith('/*') && fileType.startsWith(type.replace('/*', ''))),
      );

      if (!isTypeAccepted) {
        errors.push(`File "${file.name}" is not an accepted type.`);
        return;
      }

      if (file.size > maxSizeBytes) {
        errors.push(`File "${file.name}" exceeds the ${maxFileSize} MB limit.`);
        return;
      }

      validFiles.push(file);
    });

    if (errors.length > 0) {
      const message = errors.join(' ');
      onError?.(message);
    }

    return validFiles;
  };

  return (
    <section className={cn('flex flex-col items-center gap-[46px]', 'desktop:gap-[26px]', 'final:gap-[26px]')}>
      <h2
        className={cn(
          'text-center font-onest text-[46px] font-medium leading-[50px] tracking-[-0.05px] text-[#1C1C1C]',
          'desktop:text-[30px] desktop:leading-[34px]',
          'final:text-[30px] final:leading-[34px]',
        )}
      >
        {t('title')}
      </h2>

      <div
        className={cn(
          'grid w-full grid-cols-3 justify-items-center',
          'desktop:grid-cols-1 desktop:gap-y-3',
          'final:grid-cols-1 final:gap-y-3',
        )}
      >
        {items.map((item, index) => (
          <div
            key={index}
            className={cn(
              'shadow-[2px_8px_14px_0px_#6881B114]',
              'flex h-[234px] w-[413px] flex-col items-center gap-4 rounded-2xl border border-[#E7E7E7] bg-white p-6',
              'mobile:h-[192px] mobile:w-full mobile:gap-[14px] mobile:p-5',
              'final:h-[192px] final:w-full final:gap-[14px] final:p-5',
              item.action === 'upload' && 'cursor-pointer',
            )}
            onClick={item.action === 'upload' ? handleUploadClick : undefined}
          >
            <Image
              src={item.image}
              alt="Icon"
              width={86}
              height={86}
              className={cn('object-contain', 'mobile:h-16 mobile:w-16', 'final:h-16 final:w-16')}
            />

            <div className={cn('flex flex-col items-center gap-2', 'mobile:gap-[10px]', 'final:gap-[10px]')}>
              <h3
                className={cn(
                  'font-onest text-2xl font-medium leading-[28px] tracking-normal text-[#1C1C1C]',
                  'mobile:text-xl mobile:leading-6',
                  'final:text-xl final:leading-6',
                )}
              >
                {t(`steps.${item.action}.action`)}
              </h3>
              <p
                className={cn(
                  'text-center font-onest text-[16px] font-normal leading-[22px] tracking-normal text-[#585858]',
                  'mobile:text-sm mobile:leading-5',
                  'final:text-sm final:leading-5',
                )}
              >
                {t(`steps.${item.action}.description`)}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Hidden file input */}
      <input type="file" ref={fileInputRef} onChange={handleFileInputChange} accept={accept} className="hidden" />
    </section>
  );
}
