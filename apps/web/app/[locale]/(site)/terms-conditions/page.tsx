'use client'

import { useEffect } from 'react'
import { cn } from '@pdfily/ui/utils'
import { Link } from '@/lib/i18n/navigation'

export default function TermsConditions() {
  useEffect(() => {
    document.body.style.overflowX = 'visible'

    return () => {
      document.body.style.overflowX = 'hidden'
    }
  }, [])

  return (
    <div
      className={cn(
        'flex w-full max-w-7xl items-start justify-between py-[60px]',
        'mac:px-10',
        'laptop:flex-col laptop:gap-8',
        'small:flex-col small:gap-8 small:px-4 small:py-5 small:pb-0',
        'final:flex-col final:gap-8 final:px-4 final:py-5 final:pb-0',
      )}
    >
      {/* Sidebar Navigation */}
      <div className={cn('sticky top-10 flex flex-col items-start', 'laptop:relative laptop:top-0')}>
        <h2
          className={cn(
            'font-onest text-[58px] font-medium leading-[68px] tracking-[-0.05px] text-[#1C1C1C]',
            'laptop:hidden',
            'final:hidden',
          )}
        >
          Terms and
          <br />
          Conditions
        </h2>
        <h2
          className={cn(
            'hidden font-onest text-[30px] font-medium leading-[34px] tracking-[-0.05px] text-[#1C1C1C]',
            'laptop:block final:block',
          )}
        >
          Terms and Conditions
        </h2>

        <p
          className={cn(
            'mt-9 text-center font-onest text-lg font-normal leading-[26px] text-[#585858]',
            'small:mt-4 small:text-[16px] small:leading-5',
            'final:mt-4 final:text-[16px] final:leading-5',
          )}
        >
          Last Updated: <span className={cn('font-medium text-[#1C1C1C]')}>July 3, 2024</span>
        </p>

        <ol
          className={cn(
            'mt-10 space-y-3 font-onest text-lg font-medium leading-[26px] text-[#1C1C1C]',
            'small:mt-6 small:text-[16px]',
            'final:mt-6 final:text-[16px]',
          )}
        >
          {[
            'Introduction',
            'Accounts and Membership',
            'Billing and Payments',
            'Pricing',
            'Accuracy of Information',
            'Links to Other Resources',
            'Prohibited Uses',
            'Limitation of Liability',
            'Changes and Amendments',
            'Acceptance of These Terms',
            'Contacting Us',
          ].map((item, index) => (
            <li key={item}>
              <Link
                href={`#${item.toLowerCase().replace(/\s+/g, '-')}`}
                className={cn('transition-colors hover:text-gray-900')}
              >
                {index + 1}. {item}
              </Link>
            </li>
          ))}
        </ol>
      </div>

      {/* Main Content */}
      <div
        className={cn(
          'flex w-[728px] flex-col gap-10',
          'desktop:w-[600px]',
          'laptop:w-full',
          'small:w-full small:gap-6',
          'final:w-full final:gap-6',
        )}
      >
        {/* Introduction Section */}
        <section id="introduction" className={cn('flex flex-col gap-5', 'final:gap-3')}>
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Introduction
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            These terms and conditions (“Agreement”) set forth the general terms and conditions of your use of the
            mozaicmind.com website (“Website” or “Service”) and any of its related products and services (collectively,
            “Services”). This Agreement is legally binding between you (“User”, “you” or “your”) and Mediavibe, FZCO.
            (“Mediavibe, FZCO”, “we”, “us” or “our”). If you are entering into this agreement on behalf of a business or
            other legal entity, you represent that you have the authority to bind such entity to this agreement, in
            which case the terms “User”, “you” or “your” shall refer to such entity. If you do not have such authority,
            or if you do not agree with the terms of this agreement, you must not accept this agreement and may not
            access and use the Website and Services. By accessing and using the Website and Services, you acknowledge
            that you have read, understood, and agree to be bound by the terms of this Agreement. You acknowledge that
            this Agreement is a contract between you and Mediavibe, FZCO, even though it is electronic and is not
            physically signed by you, and it governs your use of the Website and Services.
          </p>
        </section>

        {/* Accounts and Membership Section */}
        <section
          id="accounts-and-membership"
          className={cn('mt-6 flex flex-col gap-5', 'small:mt-0', 'final:mt-0 final:gap-3')}
        >
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Accounts and Membership
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            You must be at least 18 years of age to use the Website and Services. By using the Website and Services and
            by agreeing to this Agreement you warrant and represent that you are at least 18 years of age. If you create
            an account on the Website, you are responsible for maintaining the security of your account and you are
            fully responsible for all activities that occur under the account and any other actions taken in connection
            with it. We may, but have no obligation to, monitor and review new accounts before you may sign in and start
            using the Services. Providing false contact information of any kind may result in the termination of your
            account. You must immediately notify us of any unauthorized uses of your account or any other breaches of
            security. We will not be liable for any acts or omissions by you, including any damages of any kind incurred
            as a result of such acts or omissions. We may suspend, disable, or delete your account (or any part thereof)
            if we determine that you have violated any provision of this Agreement or that your conduct or content would
            tend to damage our reputation and goodwill. If we delete your account for the foregoing reasons, you may not
            re-register for our Services. We may block your email address and Internet protocol address to prevent
            further registration.
          </p>
        </section>

        {/* Billing and Payments Section */}
        <section
          id="billing-and-payments"
          className={cn('mt-6 flex flex-col gap-5', 'small:mt-0', 'final:mt-0 final:gap-3')}
        >
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Billing and Payments
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            You shall pay all fees or charges to your account in accordance with the fees, charges, and billing terms in
            effect at the time a fee or charge is due and payable. Where Services are offered on a trial basis, payment
            may be required after the trial period ends, and not when you enter your billing details (which may be
            required prior to the commencement of the trial period). If auto-renewal is enabled for the Services you
            have subscribed for, you will be charged automatically in accordance with the term you selected. Sensitive
            and private data exchange happens over a SSL secured communication channel and is encrypted and protected
            with digital signatures, and the Website and Services are also in compliance with PCI vulnerability
            standards in order to create as secure of an environment as possible for Users. Scans for malware are
            performed on a regular basis for additional security and protection. If, in our judgment, your purchase
            constitutes a high-risk transaction, we will require you to provide us with a copy of your valid
            government-issued photo identification, and possibly a copy of a recent bank statement for the credit or
            debit card used for the purchase. We reserve the right to refuse any order you place with us. We may, in our
            sole discretion, limit or cancel quantities purchased per person, per household or per order. These
            restrictions may include orders placed by or under the same customer account, the same credit card, and/or
            orders that use the same billing and/or shipping address. In the event that we make a change to or cancel an
            order, we may attempt to notify you by contacting the e-mail and/or billing address/phone number provided at
            the time the order was made.
          </p>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className={cn('mt-12 flex flex-col gap-5', 'small:mt-0', 'final:mt-0 final:gap-3')}>
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Pricing
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            The Services are offered with a 2-day trial at $1.95 and billed at $29.95 every 4 weeks after that.
          </p>
        </section>

        {/* Accuracy of Information Section */}
        <section
          id="accuracy-of-information"
          className={cn('mt-6 flex flex-col gap-5', 'small:mt-0', 'final:mt-0 final:gap-3')}
        >
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Accuracy of Information
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            Occasionally there may be information on the Website that contains typographical errors, inaccuracies or
            omissions that may relate to product descriptions, pricing, promotions and offers. We reserve the right to
            correct any errors, inaccuracies or omissions, and to change or update information or cancel orders if any
            information on the Website or Services is inaccurate at any time without prior notice (including after you
            have submitted your order). We undertake no obligation to update, amend or clarify information on the
            Website including, without limitation, pricing information, except as required by law. No specified update
            or refresh date applied on the Website should be taken to indicate that all information on the Website or
            Services has been modified or updated.
          </p>
        </section>

        {/* Links to Other Resources Section */}
        <section id="links-to-other-resources" className={cn('flex flex-col gap-5', 'final:gap-3')}>
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Links to Other Resources
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            Although the Website and Services may link to other resources (such as websites, mobile applications, etc.),
            we are not, directly or indirectly, implying any approval, association, sponsorship, endorsement, or
            affiliation with any linked resource, unless specifically stated herein. We are not responsible for
            examining or evaluating, and we do not warrant the offerings of any businesses or individuals or the content
            of their resources. We do not assume any responsibility or liability for the actions, products, services,
            and content of any other third parties. You should carefully review the legal statements and other
            conditions of use of any resource which you access through a link on the Website. Your linking to any other
            off-site resources is at your own risk
          </p>
        </section>

        {/* Prohibited Uses Section */}
        <section id="prohibited-uses" className={cn('flex flex-col gap-5', 'final:gap-3')}>
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Prohibited Uses
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            In addition to other terms as set forth in the Agreement, you are prohibited from using the Website and
            Services or Content: (a) for any unlawful purpose; (b) to solicit others to perform or participate in any
            unlawful acts; (c) to violate any international, federal, provincial or state regulations, rules, laws, or
            local ordinances; (d) to infringe upon or violate our intellectual property rights or the intellectual
            property rights of others; (e) to harass, abuse, insult, harm, defame, slander, disparage, intimidate, or
            discriminate based on gender, sexual orientation, religion, ethnicity, race, age, national origin, or
            disability; (f) to submit false or misleading information; (g) to upload or transmit viruses or any other
            type of malicious code that will or may be used in any way that will affect the functionality or operation
            of the Website and Services, third party products and services, or the Internet; (h) to spam, phish, pharm,
            pretext, spider, crawl, or scrape; (i) for any obscene or immoral purpose; or (j) to interfere with or
            circumvent the security features of the Website and Services, third party products and services, or the
            Internet. We reserve the right to terminate your use of the Website and Services for violating any of the
            prohibited uses.
          </p>
        </section>

        {/* Limitation of Liability Section */}
        <section id="limitation-of-liability" className={cn('flex flex-col gap-5', 'final:gap-3')}>
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Limitation of Liability
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            To the fullest extent permitted by applicable law, in no event will Mediavibe, FZCO, its affiliates,
            directors, officers, employees, agents, suppliers or licensors be liable to any person for any indirect,
            incidental, special, punitive, cover or consequential damages (including, without limitation, damages for
            lost profits, revenue, sales, goodwill, use of content, impact on business, business interruption, loss of
            anticipated savings, loss of business opportunity) however caused, under any theory of liability, including,
            without limitation, contract, tort, warranty, breach of statutory duty, negligence or otherwise, even if the
            liable party has been advised as to the possibility of such damages or could have foreseen such damages. To
            the maximum extent permitted by applicable law, the aggregate liability of Mediavibe, FZCO and its
            affiliates, officers, employees, agents, suppliers and licensors relating to the services will be limited to
            an amount no greater than one euro or any amounts actually paid in cash by you to Mediavibe, FZCO for the
            prior one month period prior to the first event or occurrence giving rise to such liability. The limitations
            and exclusions also apply if this remedy does not fully compensate you for any losses or fails of its
            essential purpose.
          </p>
        </section>

        {/* Changes and Amendments Section */}
        <section
          id="changes-and-amendments"
          className={cn('mt-6 flex flex-col gap-5', 'small:mt-0', 'final:mt-0 final:gap-3')}
        >
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Changes and Amendments
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            We reserve the right to modify this Agreement or its terms related to the Website and Services at any time
            at our discretion. When we do, we will revise the updated date at the bottom of this page. We may also
            provide notice to you in other ways at our discretion, such as through the contact information you have
            provided. An updated version of this Agreement will be effective immediately upon the posting of the revised
            Agreement unless otherwise specified. Your continued use of the Website and Services after the effective
            date of the revised Agreement (or such other act specified at that time) will constitute your consent to
            those changes.
          </p>
        </section>

        {/* Acceptance of These Terms Section */}
        <section id="acceptance-of-these-terms" className={cn('flex flex-col gap-5', 'final:gap-3')}>
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Acceptance of These Terms
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            You acknowledge that you have read this Agreement and agree to all its terms and conditions. By accessing
            and using the Website and Services you agree to be bound by this Agreement. If you do not agree to abide by
            the terms of this Agreement, you are not authorized to access or use the Website and Services.
          </p>
        </section>

        {/* Contacting Us Section */}
        <section id="contacting-us" className={cn('flex flex-col gap-5', 'final:gap-3')}>
          <h2
            className={cn(
              'font-onest text-[32px] font-medium leading-10 text-[#1C1C1C]',
              'final:text-xl final:leading-6',
            )}
          >
            Contacting Us
          </h2>
          <p
            className={cn(
              'font-onest text-[16px] font-normal leading-6 text-[#585858]',
              'final:text-sm final:leading-[22px]',
            )}
          >
            If you have any questions, concerns, or complaints regarding this Agreement, we encourage you to contact us
            using the details below:
            <br />
            <a href="#your-choices" className="font-semibold text-[#F0401D] underline">
              <EMAIL>
            </a>
          </p>
        </section>
      </div>
    </div>
  )
}
