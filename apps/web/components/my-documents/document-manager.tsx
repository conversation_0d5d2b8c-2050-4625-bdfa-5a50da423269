'use client';

import axios from 'axios';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useState, useEffect, useCallback, useMemo } from 'react';
import Swal from 'sweetalert2';
import { useCaptchaWrapperContext } from '@pdfily/auth/captcha/client';

import { useDocuments } from '@pdfily/documents/hooks/use-documents';
import { useLastEditFileId } from '@pdfily/documents/hooks/use-last-edit-file-id';
import { downloadBlob, formatDate, formatFileSize } from '@pdfily/shared/utils';
import { Database } from '@pdfily/supabase/database';
import { useSupabase } from '@pdfily/supabase/hooks/use-supabase';
import { useUploadFile } from '@pdfily/supabase/hooks/use-upload-file';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@pdfily/ui/dropdown-menu';
import { Spinner } from '@pdfily/ui/spinner';
import { Button } from '@pdfily/ui/button';
import { Input } from '@pdfily/ui/input';
import { Tabs, TabsList, TabsTrigger } from '@pdfily/ui/tabs';
import { cn } from '@pdfily/ui/utils';
import FileUploader from '@/components/my-documents/file-uploader';
import UploadModal from '@/components/my-documents/upload-modal';
import ProgressModal from '@/components/progress-modal';
import { useAuth } from '@/components/session-provider';
import { Link, usePathname, useRouter } from '@/lib/i18n/navigation';

type Document = Pick<
  Database['public']['Tables']['documents']['Row'],
  'id' | 'title' | 'type' | 'size' | 'last_modified' | 'internal_type'
>;

const tabs = [
  { value: 'all', name: 'all', href: '/my-documents' },
  { value: 'recent', name: 'recent', href: '/my-documents/recent' },
];

export default function DocumentManager({ userId }: React.PropsWithChildren<{ userId: string }>) {
  const path = usePathname();
  const supabase = useSupabase();
  const router = useRouter();
  const t = useTranslations('MyDocuments');
  const { user } = useAuth();
  const { refreshCaptchaToken } = useCaptchaWrapperContext();
  const { documents, refreshDocuments, loading } = useDocuments(userId);
  const { lastEditFileId, setFileId } = useLastEditFileId();
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState(path === '/my-documents/recent' ? 'recent' : 'all');
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  // Mock data for demonstration
  useEffect(() => {
    setFilteredDocuments(documents);
  }, [documents]);

  // Filter documents based on search query
  useEffect(() => {
    const filtered = documents.filter((doc) => doc.title.toLowerCase().includes(searchQuery.toLowerCase()));
    setFilteredDocuments(filtered);
  }, [searchQuery, documents]);

  // Filter documents based on active tab
  useEffect(() => {
    if (activeTab === 'all') {
      setFilteredDocuments(documents.filter((doc) => doc.title.toLowerCase().includes(searchQuery.toLowerCase())));
    } else if (activeTab === 'recent') {
      // For demo purposes, consider "recents" as documents from today
      setFilteredDocuments(
        documents.filter(
          (doc) =>
            doc.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
            doc.last_modified &&
            doc.last_modified.includes('Today'),
        ),
      );
    }
  }, [activeTab, documents, searchQuery]);

  /**
   * Handles upload errors (memoized)
   */
  const handleError = useCallback((message: string) => {
    console.log(`Error while uploading file: ${message}`);
    // You could show a toast notification here
  }, []);

  /**
   * Handles the search query change event.
   *
   * @param {React.ChangeEvent<HTMLInputElement>} e - The change event triggered by the search input.
   */
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>): void => {
    setSearchQuery(e.target.value);
  };

  /**
   * Asynchronously deletes a document from the server.
   *
   * This function sends an HTTP DELETE request to the server to remove a document
   * identified by the given `documentId`.
   *
   * @param {string} documentId - The unique identifier of the document to be deleted.
   * @returns {Promise<void>} - A promise that resolves when the deletion process is complete.
   */
  const deleteDocument = useCallback(async (documentId: string): Promise<void> => {
    try {
      const response = await axios.delete(`/api/v1/files/${encodeURIComponent(documentId)}`, {
        headers: {
          Accept: 'application/json',
        },
      });

      if (response.status === 200) {
        Swal.fire('Deleted!', 'Your document has been deleted.', 'success');
        refreshDocuments(); // Call the refresh method after deletion
      } else {
        Swal.fire('Error', 'Failed to delete the document.', 'error');
      }
    } catch (error) {
      Swal.fire('Error', 'Something went wrong!', 'error');
    }
  }, []);

  /**
   * Handles the deletion of a document by showing a confirmation prompt.
   *
   * @param {Document} document - The document object containing the ID and title to be deleted.
   * @returns {void}
   */
  const handleDelete = useCallback(
    (document: Document) => {
      Swal.fire({
        title: `Are you sure you want to delete ${document.title}?`,
        text: 'This action cannot be undone!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Delete File',
        cancelButtonText: 'Cancel',
      }).then((result) => {
        result.isConfirmed && deleteDocument(document.id);
      });
    },
    [deleteDocument],
  );

  /**
   * Handles the download of a document.
   */
  const documentDownload = useCallback(async (file: Document) => {
    try {
      const response = await fetch(`/api/v1/files/${encodeURIComponent(file.id)}/download`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
        },
      });

      if (!response.ok) {
        const { error, message } = await response.json();
        Swal.fire({
          icon: 'error',
          title: error ?? 'Oops...',
          text: message ?? 'Something went wrong while downloading the file.',
        });
        return;
      }

      const blob = await response.blob();
      downloadBlob(blob, response, file.title);
    } catch (error) {
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'Something went wrong while downloading the document.',
      });
    }
  }, []);

  /**
   * Initiates the download of a document.
   *
   * @param {Document} document - The document to download.
   */
  const handleDownload = async (document: Document): Promise<void> => {
    // Refresh captcha token before download
    refreshCaptchaToken();

    const { data } = await supabase.rpc('has_subscription_status', { user_id: userId });
    const subscription = data?.[0];
    const hasSubscription = (subscription?.has_subscription || user?.user_metadata.is_subscribed) ?? false;

    if (!hasSubscription) {
      setFileId(document.id);
      router.push('/checkout');
      return;
    }

    await documentDownload(document);
  };

  /**
   * Opens the document in a viewer by redirecting to the edit page in a new window.
   *
   * @param {Document} document - The document to open.
   */
  const handleOpen = (document: Document): void => {
    const url = `/document/edit/${document.id}`;
    window.open(url, '_blank');
  };

  const handleCloseModal = () => {
    setIsUploadModalOpen(false);
  };

  /**
   * Upload file to API (memoized)
   */
  const uploadFile = useUploadFile({
    // ✅ tracks the upload %
    setProgress: (percent) => {
      setProgress(percent);
    },
    // ✅ sets the document object
    onSuccess: () => {
      refreshDocuments(); // Call the refresh method after new document is created
    },
    // ✅ handles upload errors
    onError: (error) => {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error?.message || 'Failed to upload file.',
      });
    },
  });

  /**
   * Handles file selection and upload (memoized)
   */
  const handleFilesSelected = useMemo(() => {
    return async (files: File[]) => {
      const file = files?.[0];

      if (!file) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Error while uploading file: No file selected.',
        });
        return;
      }

      setIsUploadModalOpen(false);
      setIsUploading(true);
      setProgress(0);

      try {
        await uploadFile(file);
      } catch (error) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to upload file or missing documentId.',
        });
      } finally {
        setIsUploading(false);
      }
    };
  }, [uploadFile, setIsUploadModalOpen, setIsUploading, setProgress]);

  /**
   * Returns the appropriate file icon based on the document type.
   *
   * @param {Document['type']} type - The type of the document (e.g., 'pdf', 'doc', 'xls').
   * @returns {string} - A string representing the file icon. Defaults to 'pdf' if the type is not recognized.
   *
   * @example
   * getFileIcon('pdf'); // returns 'pdf'
   */
  const getFileIcon = (type: Document['type']): string => {
    switch (type) {
      case 'pdf':
        return 'pdf';
      case 'doc':
        return 'doc';
      case 'xls':
        return 'xls';
      default:
        return 'pdf';
    }
  };

  return (
    <div className={cn('flex flex-col items-center pt-10', 'desktop:pt-5', 'final:pt-5')}>
      <div
        className={cn(
          'mb-8 flex w-full max-w-[1060px] items-center justify-between',
          'desktop:mb-6 desktop:w-full desktop:flex-col desktop:items-start desktop:gap-4 desktop:px-4',
          'final:mb-6 final:w-full final:flex-col final:items-start final:gap-4 final:px-4',
        )}
      >
        <h1
          className={cn(
            'font-onest text-[46px] font-medium leading-[50px] tracking-[-0.05px] text-[#1C1C1C]',
            'desktop:text-[26px] desktop:leading-[30px]',
            'final:text-[26px] final:leading-[30px]',
          )}
        >
          {t('title')}
        </h1>
        <div className={cn('flex items-center gap-10', 'desktop:w-full', 'final:w-full')}>
          <div className={cn('relative', 'desktop:w-full', 'final:w-full')}>
            <Image
              src="/images/my-documents/Search.svg"
              alt="Search Icon"
              width={20}
              height={20}
              className="absolute left-4 top-1/2 -translate-y-1/2 object-contain"
            />
            <Input
              type="text"
              placeholder={t('searchPlaceholder')}
              className={cn(
                'font-onest text-[16px] font-normal leading-5 text-[#585858]',
                'h-[50px] w-[449px] rounded-[10px] border border-[#E7E7E7] py-[15px] pl-11',
                'desktop:h-12 desktop:w-full desktop:py-[14px]',
                'final:h-12 final:w-full final:py-[14px]',
              )}
              value={searchQuery}
              onChange={handleSearch}
            />
          </div>
          <Button
            onClick={() => setIsUploadModalOpen(true)}
            className={cn(
              'flex h-[50px] w-[157px] items-center gap-1.5 rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-medium leading-5 text-white hover:bg-red-600',
              'desktop:hidden',
              'final:hidden',
            )}
          >
            <Image src="/images/my-documents/Plus.svg" alt="Plus" width={20} height={20} className="object-contain" />
            <span>{t('addNewButton')}</span>
          </Button>
        </div>
      </div>

      <Tabs
        defaultValue={activeTab}
        className={cn('flex w-full justify-center border-b border-b-[#E7E7E7]')}
        onValueChange={(v) => setActiveTab(v as 'all' | 'recent')}
      >
        <TabsList
          className={cn(
            'flex h-fit w-full max-w-[1060px] justify-start gap-7 bg-transparent p-0',
            'desktop:gap-0 final:gap-0',
          )}
        >
          {tabs.map((tab, index) => (
            <Link href={tab.href} key={index} className="desktop:flex-1 final:flex-1">
              <TabsTrigger
                value={tab.value}
                className={cn('relative p-0 pb-[15px]', 'desktop:w-full desktop:pb-3', 'final:w-full final:pb-3')}
              >
                <span
                  className={cn(
                    'font-onest text-[16px] font-medium leading-5',
                    'desktop:text-sm desktop:leading-[18px]',
                    'final:text-sm final:leading-[18px]',
                    activeTab === tab.value ? 'text-[#1C1C1C]' : 'text-[#585858]',
                  )}
                >
                  {t(`tabs.${tab.name}`)}
                </span>
                {activeTab === tab.value && (
                  <div
                    className={cn('absolute bottom-0 left-0 h-1 w-full rounded-t-[10px] bg-[#F0401D]', 'final:h-0.5')}
                  ></div>
                )}
              </TabsTrigger>
            </Link>
          ))}
        </TabsList>
      </Tabs>

      {loading ? (
        <div className={cn('ml-[-100%] mr-[-100%] bg-[#F9F9F9] pl-[100%] pr-[100%]')}>
          <div className="flex h-80 items-center gap-3">
            <Spinner size="medium" />
            <h1>{t('loading')}</h1>
          </div>
        </div>
      ) : filteredDocuments.length === 0 ? (
        <div className={cn('ml-[-100%] mr-[-100%] bg-[#F9F9F9] pl-[100%] pr-[100%]')}>
          <FileUploader onFilesSelected={handleFilesSelected} maxFileSize={50} onError={handleError} />
        </div>
      ) : (
        <div className={cn('ml-[-100%] mr-[-100%] bg-[#F9F9F9] pl-[100%] pr-[100%]')}>
          <Button
            className={cn(
              'mx-4 mt-5 flex h-12 w-[calc(100vw_-_2rem)] items-center justify-center gap-1.5 rounded-[10px] bg-[#F0401D] hover:bg-red-600',
              'hidden',
              'final:flex',
            )}
            onClick={() => {}}
          >
            <Image
              src={`/images/my-documents/Plus.svg`}
              alt="plus"
              width={20}
              height={20}
              className={cn('cursor-pointer object-contain')}
            />
            <span className="font-onest text-[16px] font-medium leading-[20px] tracking-normal text-white">
              {t('addNewButton')}
            </span>
          </Button>

          <div
            className={cn(
              'my-8 flex w-screen max-w-[1060px] flex-col gap-3',
              'desktop:mx-4 desktop:my-6 desktop:w-[calc(100vw_-_2rem)]',
              'final:mx-4 final:my-6 final:w-[calc(100vw_-_2rem)]',
            )}
          >
            <div
              className={cn(
                'flex items-center px-5 text-[16px] font-normal leading-5 text-[#585858]',
                'desktop:hidden',
                'final:hidden',
              )}
            >
              <div className="ml-0">{t('table.name')}</div>
              <div className="ml-[387px]">{t('table.size')}</div>
              <div className="ml-[220px]">{t('table.date')}</div>
              <div className="ml-[244px]">{t('table.actions')}</div>
            </div>

            <div className="flex flex-col gap-2">
              {filteredDocuments.map((document) => (
                <div
                  key={document.id}
                  className={cn(
                    'h-16 rounded-[10px] bg-white shadow-[2px_8px_14px_0px_#6881B114]',
                    'flex items-center p-5 hover:bg-gray-50',
                    'desktop:h-[46px] desktop:justify-between desktop:p-3',
                    'final:h-[46px] final:justify-between final:p-3',
                  )}
                >
                  <div className={cn('w-[431px]', 'flex items-center gap-3', 'desktop:w-auto', 'final:w-auto')}>
                    <Image
                      src={`/images/my-documents/${getFileIcon(document.type)}.svg`}
                      alt="pdf icon"
                      width={24}
                      height={24}
                      className={cn(
                        'object-contain',
                        'desktop:h-[22px] desktop:w-[22px]',
                        'final:h-[22px] final:w-[22px]',
                      )}
                    />
                    <span
                      className={cn(
                        'font-onest text-[16px] font-medium leading-5 text-[#1C1C1C]',
                        'desktop:text-sm desktop:leading-[18px]',
                        'final:text-sm final:leading-[18px]',
                      )}
                    >
                      {document.title}
                    </span>
                  </div>

                  <div
                    className={cn(
                      'w-[252px]',
                      'font-onest text-[16px] font-normal leading-5 text-[#585858]',
                      'desktop:hidden',
                      'final:hidden',
                    )}
                  >
                    {formatFileSize(document.size)}
                  </div>

                  <div
                    className={cn(
                      'w-[299px]',
                      'font-onest text-[16px] font-normal leading-5 text-[#585858]',
                      'desktop:hidden',
                      'final:hidden',
                    )}
                  >
                    {formatDate(document.last_modified, true)}
                  </div>

                  <div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Image
                          src={`/images/my-documents/Actions.svg`}
                          alt="more actions"
                          width={20}
                          height={20}
                          className={cn('cursor-pointer object-contain')}
                        />
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align="end"
                        className={cn(
                          'border-[0.5px] border-[#E7E7E7] bg-white p-2 shadow-[2px_8px_20px_0px_#4852641F]',
                          '-mt-5 mr-7',
                        )}
                      >
                        <DropdownMenuItem
                          onClick={() => handleOpen(document)}
                          className="flex items-center gap-2 px-2 py-1.5"
                        >
                          <Image
                            src={`/images/my-documents/Open.svg`}
                            alt="open"
                            width={18}
                            height={18}
                            className={cn('object-contain')}
                          />
                          <span className="font-onest text-[16px] font-medium leading-5 text-[#1C1C1C]">
                            {t('open')}
                          </span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDownload(document)}
                          className="flex items-center gap-2 px-2 py-1.5"
                        >
                          <Image
                            src={`/images/my-documents/Download.svg`}
                            alt="open"
                            width={18}
                            height={18}
                            className={cn('object-contain')}
                          />
                          <span className="font-onest text-[16px] font-medium leading-5 text-[#1C1C1C]">
                            {t('download')}
                          </span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(document)}
                          className="flex items-center gap-2 px-2 py-1.5"
                        >
                          <Image
                            src={`/images/my-documents/Delete.svg`}
                            alt="open"
                            width={18}
                            height={18}
                            className={cn('object-contain')}
                          />
                          <span className="font-onest text-[16px] font-medium leading-5 text-[#1C1C1C]">
                            {t('delete')}
                          </span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <UploadModal isOpen={isUploadModalOpen} onClose={handleCloseModal} onUpload={handleFilesSelected} />
      {isUploading && <ProgressModal isOpen={isUploading} progress={progress} onClose={() => setIsUploading(false)} />}
    </div>
  );
}
