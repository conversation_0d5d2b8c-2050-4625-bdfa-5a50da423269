import { z } from 'zod';

/**
 * Schema for validating SendGrid configuration environment variables.
 */
export const SendgridSchema = z.object({
  /**
   * The SendGrid API key used for authentication.
   * Must not be empty.
   */
  apiKey: z
    .string({ required_error: 'Please provide SMTP_PASS', description: 'The SendGrid Api Key' })
    .trim()
    .min(1, { message: 'The SendGrid Api Key should not be empty' }),

  /**
   * The email address used as the sender in outgoing emails.
   * Must be a valid email address.
   */
  senderEmail: z
    .string({
      required_error: 'Please provide SMTP_FROM_EMAIL',
      description: 'The SendGrid sender email address',
    })
    .trim()
    .toLowerCase()
    .email('Invalid email address'),

  /**
   * The email address that will receive support messages from the contact form.
   * Must be a valid email address.
   */
  supportReceiverEmail: z
    .string({
      required_error: 'Please provide SENDGRID_SUPPORT_RECEIVER_EMAIL',
      description: 'The SendGrid receiver email address',
    })
    .trim()
    .toLowerCase()
    .email('Invalid email address'),

  /**
   * The SendGrid template ID used for the contact form emails.
   * Must not be empty.
   */
  contactFormTemplateId: z
    .string({
      required_error: 'Please provide SENDGRID_CONTACT_TEMPLATE_ID',
      description: 'The SendGrid template ID',
    })
    .trim()
    .min(1, { message: 'The SendGrid template ID should not be empty' }),
});

export type TSendgridSchema = z.infer<typeof SendgridSchema>;
