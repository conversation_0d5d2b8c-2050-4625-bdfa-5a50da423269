import type { User } from '@supabase/supabase-js';
import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { useSupabase } from './use-supabase';

const queryKey = ['supabase:user'];

/**
 * @function useUser
 * @description Custom React hook to fetch the current user using Supabase.
 *
 * @param {User | null} [initialData] - Optional initial data for the user.
 * @returns {UseQueryResult<User | undefined, Error>} - Result object from `useQuery`, including user data or error.
 *
 * @example
 * const { data: user, isLoading, isError } = useUser();
 */
export function useUser(initialData?: User | null): UseQueryResult<User | undefined, Error> {
  const supabase = useSupabase();

  const queryFn = async (): Promise<User | undefined> => {
    const response = await supabase.auth.getUser();

    // Check for authentication error
    if (response.error) {
      return undefined;
    }

    // Check if user data exists in the response
    if (response.data?.user) {
      return response.data.user;
    }

    // Handle unexpected result format
    return Promise.reject(new Error('Unexpected result format'));
  };

  return useQuery({
    queryFn,
    queryKey,
    initialData,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
}
