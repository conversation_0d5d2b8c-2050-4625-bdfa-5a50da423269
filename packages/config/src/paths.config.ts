import { z } from "zod";
import PathsConfigSchema from "./schemas/paths-config.schema";

export type PathsConfig = z.infer<typeof PathsConfigSchema>;

// Parse and validate the configuration at startup.
const pathsConfig: PathsConfig = PathsConfigSchema.parse({
  auth: {
    signIn: '/auth/sign-in',
    signUp: '/auth/sign-up',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/reset-password',
    callback: '/auth/callback',
  },
  app: {
    home: '/',
    profileSettings: '/settings',
  }
});

export default pathsConfig;
