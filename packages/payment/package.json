{"name": "@pdfily/payment", "private": true, "version": "0.1.0", "description": "Payment integration package for the PDFily monorepo, supporting Stripe and Solidgate.", "type": "module", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "dependencies": {"@next/third-parties": "^15.4.1", "@pdfily/supabase": "workspace:*", "@pdfily/shared": "workspace:*", "@pdfily/ui": "workspace:*", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@supabase/supabase-js": "^2.52.0", "axios": "^1.10.0", "next": "^15.3.3", "react": "^19.1.0", "stripe": "^18.2.1"}, "devDependencies": {"@pdfily/eslint-config": "workspace:*", "@pdfily/prettier-config": "workspace:*", "@pdfily/typescript-config": "workspace:*", "@types/node": "^22.16.4", "@types/react": "^19.1.8", "typescript": "5.8.3"}, "exports": {".": "./src/index.ts", "./hooks/*": "./src/hooks/*.tsx", "./provider": "./src/lib/providers/*.ts", "./service": "./src/lib/payment-service.ts", "./factory": "./src/lib/payment-provider-factory.ts", "./types": "./src/types/*.ts", "./ui": "./src/ui/*.tsx"}, "turbo": {"pipeline": {"build": {"outputs": []}}}, "typesVersions": {"*": {"*": ["src/*"]}}, "prettier": "@pdfily/prettier-config"}