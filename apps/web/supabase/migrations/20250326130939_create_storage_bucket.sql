-- Ensure the storage schema exists
create schema if not exists storage;

-- Create a new bucket by directly inserting into the storage.buckets table
insert into storage.buckets (id, name, created_at, updated_at, public)
select
  'uploads',  -- Bucket ID and name
  'uploads',  -- Bucket name
  now(),      -- Current timestamp for created_at
  now(),      -- Current timestamp for updated_at
  false       -- Set public access to false (private bucket)
where not exists (
  select 1 from storage.buckets where name = 'uploads'
);