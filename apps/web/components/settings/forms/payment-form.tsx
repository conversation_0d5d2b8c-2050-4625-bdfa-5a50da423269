'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { cn } from '@pdfily/ui/utils';
import { Button } from '@pdfily/ui/button';
import { usePayment } from '@pdfily/payment';

interface AccountFormProps {
  user?: User | null;
}

const cards = [
  { name: 'Visa', image: '/images/checkout/Visa2.png' },
  { name: 'Master', image: '/images/checkout/Master2.png' },
  { name: 'AmEx', image: '/images/checkout/AmEx2.png' },
  { name: 'Discover', image: '/images/checkout/Discover2.png' },
  { name: 'JCB', image: '/images/checkout/JCB.png' },
];

// Card type detection and formatting
const CARD_TYPES = [
  {
    name: 'Visa',
    pattern: /^4/,
  },
  {
    name: 'Master',
    pattern: /^5[1-5]/,
  },
  {
    name: 'AmEx',
    pattern: /^3[47]/,
  },
  {
    name: 'Discover',
    pattern: /^6(?:011|5)/,
  },
];

export default function PaymentForm({ user }: AccountFormProps) {
  const t = useTranslations('Settings.payment');
  const { service } = usePayment();

  // Retrive the update payment method UI compoments of the current provider
  const uiComponents = service?.getUIComponents();
  const UpdatePaymentMethodForm = uiComponents?.UpdatePaymentMethodForm;

  const [isEditPaymentModalOpen, setIsEditPaymentModalOpen] = useState(false);
  const [defaultPaymentMethod, setDefaultPaymentMethod] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!service) return;
    const initPaymentMethods = async () => {
      try {
        setIsLoading(true);
        const paymentMethods = await service.getCustomerPaymentMethods(user);
        setDefaultPaymentMethod(paymentMethods.defaultPaymentMethod);
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        console.log({ error });
      } finally {
        setIsLoading(false);
      }
    };
    if (user) {
      initPaymentMethods();
    }
  }, [user, service]);

  // Handle close
  const handleClose = () => {
    setIsEditPaymentModalOpen(false);
  };

  // Handle error
  const handleError = () => {
    handleClose();
  };

  return (
    <div
      className={cn(
        'flex w-full max-w-[1060px] flex-col rounded-3xl bg-white p-8 shadow-[2px_8px_14px_0px_#6881B114]',
        'desktop:mx-4 desktop:p-4',
        'final:p-4',
      )}
    >
      <h3
        className={cn(
          'mb-3 font-onest text-[32px] font-medium leading-9 tracking-[-0.05px] text-[#1C1C1C]',
          'desktop:mb-2 desktop:text-xl desktop:leading-6',
          'final:mb-2 final:text-xl final:leading-6',
        )}
      >
        {t('title')}
      </h3>
      <p
        className={cn(
          'mb-6 font-onest text-lg font-normal leading-[22px] text-[#585858]',
          'desktop:mb-5 desktop:text-sm desktop:leading-[19px]',
          'final:mb-5 final:text-sm final:leading-[19px]',
        )}
      >
        {t('description.saveTimeWithCard')}
      </p>

      <div className={cn('mb-7 w-[308px]', 'desktop:mb-6 desktop:w-full', 'final:mb-6 final:w-full')}>
        <label htmlFor="card" className="mb-2 font-onest text-sm font-medium leading-5 text-[#1C1C1C]">
          {t('card.label')}
        </label>

        <Button
          variant={'outline'}
          className={cn(
            'flex h-[50px] w-full items-center justify-between rounded-[10px] border border-[#E7E7E7] px-4',
            'desktop:h-12',
            'final:h-12',
          )}
          onClick={() => setIsEditPaymentModalOpen(true)}
        >
          <Image src="/images/checkout/Visa2.png" alt="Visa" width={38} height={24} className="object-contain" />
          <span className="ml-3 flex flex-1 items-center gap-x-3 text-left font-onest text-[16px] font-normal leading-5 text-[#1C1C1C]">
            ****{' '}
            {isLoading && (
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-l-0 border-t-0 border-red-500"></div>
            )}
            {defaultPaymentMethod?.card?.last4}
          </span>
          <Image src="/images/settings/Next.svg" alt="Edit" width={20} height={20} className="object-contain" />
        </Button>
      </div>

      <Button
        type="submit"
        disabled={isSubmitting}
        className={cn(
          'flex h-[50px] w-[98px] items-center justify-center rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-medium leading-5 text-white hover:bg-[#d83e15]',
          'desktop:h-12 desktop:w-full',
          'final:h-12 final:w-full',
        )}
      >
        {isSubmitting ? t('savingButton') : t('saveButton')}
      </Button>

      <div
        className={cn(
          'fixed inset-0 z-50 bg-black/80 transition-opacity duration-300',
          isEditPaymentModalOpen ? 'opacity-100' : 'pointer-events-none opacity-0',
        )}
        onClick={handleClose}
      >
        <div
          className={cn(
            'absolute left-1/2 top-1/2 flex h-[388px] min-h-max w-[392px] -translate-x-1/2 -translate-y-1/2 flex-col rounded-[10px] bg-white p-[26px]',
            'desktop:h-[342px] desktop:w-[343px] desktop:p-4',
            'final:h-[342px] final:w-[343px] final:p-4',
          )}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <h3
            className={cn(
              'mb-3 font-onest text-2xl font-medium leading-7 tracking-[-0.05px]',
              'desktop:mb-2.5 desktop:text-xl desktop:leading-6',
              'final:mb-2.5 final:text-xl final:leading-6',
            )}
          >
            {t('modal.title')}
          </h3>

          <div className={cn('mb-6 flex items-center gap-3', 'desktop:mb-5', 'final:mb-5')}>
            <span className={cn('font-onest text-[16px] font-normal leading-5')}>{t('modal.acceptedCards')}</span>
            <div className="flex gap-1">
              {cards.map((item, index) => (
                <div key={index} className={cn('flex h-[18px] w-[28.5px] items-center justify-center')}>
                  <Image src={item.image} alt={item.name} width={28.5} height={18} className={'object-contain'} />
                </div>
              ))}
            </div>
          </div>
          {service && UpdatePaymentMethodForm && (
            <UpdatePaymentMethodForm
              onSuccess={handleClose}
              onError={handleError}
              user={user}
              amount={0}
              currency="usd"
              isSubscription
            />
          )}
        </div>
      </div>
    </div>
  );
}
