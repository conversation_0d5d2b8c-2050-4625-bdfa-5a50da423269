import { GoogleTagManager } from '@next/third-parties/google';
import googleAnalyticsConfig from '@pdfily/config/google-analytics.config';

export default async function GoogleAnalyticsTracker() {
  const { isTrackingEnabled, gtmId } = googleAnalyticsConfig;

  return (
    <>
      {isTrackingEnabled && gtmId && (
        <>
          <GoogleTagManager gtmId={gtmId} />
        </>
      )}
    </>
  );
}
