'use client';

import Image from 'next/image';
import { ComponentProps, useState } from 'react';
import { cn } from '@pdfily/ui/utils';
import authConfig from '@pdfily/config/auth.config';
import { signInWithProviderPopupAction } from '@pdfily/auth/signin';

import { Button } from '../ui/button';

/**
 * Props interface for the GoogleAuthButton component.
 *
 * @interface GoogleAuthButtonProps
 * @extends {React.ButtonHTMLAttributes<HTMLButtonElement>}
 */
type GoogleAuthButtonProps = ComponentProps<typeof Button> & {
  /**
   * Additional CSS classes to apply to the button.
   * @type {string}
   * @default ''
   */
  className?: string;

  path: string;
};

/**
 * GoogleAuthButton component that renders a Google authentication button with visual separator.
 *
 * The component only renders when Google authentication is enabled in the auth configuration.
 * When disabled, it returns an empty fragment to maintain layout consistency.
 */
export default function GoogleAuthButton({ path, className = '', ...rest }: GoogleAuthButtonProps) {
  const { googleAuthEnabled } = authConfig;
  const [isLoading, setIsLoading] = useState(false);

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    try {
      await signInWithProviderPopupAction('google', path);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {googleAuthEnabled ? (
        <div>
          <button
            type="button"
            onClick={handleGoogleSignIn}
            disabled={isLoading}
            className={cn(
              'flex gap-x-2 justify-center items-center p-3 border w-full border-gray-200 hover:bg-gray-50 hover:shadow-md hover:border-white transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed',
              className,
            )}
            {...rest}
          >
            <Image src="/images/auth/google.svg" alt="google" width={500} height={28} className="w-7 h-7" />
            <p>{isLoading ? 'processing' : 'Continue with Google'}</p>
          </button>
          <div className="flex items-center my-4">
            <div className="flex-grow h-px bg-gray-300" />
            <span className="px-4 text-sm text-gray-500">OR</span>
            <div className="flex-grow h-px bg-gray-300" />
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );
}
