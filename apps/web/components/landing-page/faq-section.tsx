import { useTranslations } from 'next-intl';
import { cn } from '@pdfily/ui/utils';
import { Accordion, AccordionItem } from './faq-accordion';

const items = ['isFree', 'isSignable', 'isEditable', 'isProtectable'];

export default function FaqSection({ showTitle = true }: { showTitle?: boolean }) {
  const t = useTranslations('HomePage.faqSection');
  return (
    <section
      className={cn(
        'flex w-full items-start justify-between',
        'tablet:flex-col tablet:justify-center tablet:gap-[26px]',
        'final:flex-col final:justify-center final:gap-[26px]',
      )}
    >
      <div
        className={cn(
          'flex w-[413px] flex-col gap-6',
          'laptop:w-[300px]',
          'tablet:w-full tablet:gap-3',
          'final:w-full final:gap-3',
        )}
      >
        {showTitle ? (
          <p
            className={cn(
              '-mb-3 font-onest text-lg font-medium leading-[22px] tracking-normal text-[#F0401D]',
              'final:hidden',
            )}
          >
            {t('title')}
          </p>
        ) : null}
        <h2
          className={cn(
            'font-onest text-[46px] font-medium leading-[50px] tracking-[-0.05px] text-[#1C1C1C]',
            'laptop:text-[30px] laptop:leading-9',
            'tablet:text-[30px] tablet:leading-9',
            'final:text-[30px] final:leading-9',
          )}
        >
          {t('title')}
        </h2>
        <p
          className={cn(
            'font-onest text-lg font-normal leading-[26px] tracking-normal text-[#585858]',
            'laptop:text-[16px] laptop:leading-6',
            'tablet:text-[16px] tablet:leading-6',
            'final:text-[16px] final:leading-6',
          )}
        >
          {t.rich('description', {
            supportTag: (chunks) => <span className="underline">{chunks}</span>,
          })}
        </p>
      </div>
      <Accordion
        className={cn(
          'flex w-[738px] flex-col gap-6',
          'desktop:w-[560px]',
          'laptop:w-[420px]',
          'tablet:w-full tablet:gap-4',
          'final:w-full final:gap-4',
        )}
      >
        {items.map((item, index) => (
          <AccordionItem key={index} title={t(`questions.${item}.question`)} defaultOpen={index === 0}>
            {t(`questions.${item}.answer`)}
          </AccordionItem>
        ))}
      </Accordion>
    </section>
  );
}
