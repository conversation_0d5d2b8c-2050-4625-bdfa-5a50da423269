'use client';

import { createContext, useContext, ReactNode } from 'react';

import { useCaptchaWrapperContext } from '@pdfily/auth/captcha/client';
import { useSupabase } from '@pdfily/supabase/hooks/use-supabase';

/**
 * Represents the context for anonymous authentication.
 *
 * @interface AnonymousContextType
 */
interface AnonymousContextType {
  signInAnonymously: () => Promise<void>;
}

/**
 * Creates a React context for anonymous authentication.
 *
 * @constant {React.Context<AnonymousContextType | undefined>} AnonymousContext
 */
const AnonymousContext = createContext<AnonymousContextType | undefined>(undefined);

/**
 * AnonymousProvider - A React context provider for handling anonymous user authentication.
 *
 * @param {ReactNode} param0.children - The child components to be wrapped by the provider.
 */
export const AnonymousProvider = ({ children }: { children: ReactNode }) => {
  const supabase = useSupabase();
  const { captchaToken, resetCaptchaToken } = useCaptchaWrapperContext();

  /**
   * Asynchronously signs in the user anonymously using Supabase.
   * Persists the session in local storage.
   */
  const signInAnonymously = async (): Promise<void> => {
    try {
      const { data, error } = await supabase.auth.signInAnonymously({
        ...(captchaToken && {
          options: { captchaToken },
        }),
      });

      if (error) {
        throw new Error(`Error while signing in anonymously: ${error.message}`);
      }

      if (!data?.user) {
        throw new Error('Anonymous sign-in failed due to no user returned');
      }
    } finally {
      resetCaptchaToken();
    }
  };

  return <AnonymousContext.Provider value={{ signInAnonymously }}>{children}</AnonymousContext.Provider>;
};

/**
 * Custom React hook that provides access to the anonymous user context.
 * This hook should be used within an `AnonymousProvider`.
 *
 * @function useAnonymous
 * @returns {Object} The context object containing user information, loading state,
 * and functions related to anonymous authentication.
 */
export const useAnonymous = () => {
  const context = useContext(AnonymousContext);

  if (!context) {
    throw new Error('useAnonymous must be used within an AnonymousProvider');
  }

  return context;
};
