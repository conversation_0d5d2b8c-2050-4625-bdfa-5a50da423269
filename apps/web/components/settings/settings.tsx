"use client";

import { User } from "@supabase/supabase-js";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { cn } from "@pdfily/ui/utils";
import SettingsTabs from "./settings-tabs";
import SettingsContent from "./settings-content";

interface SiteHeaderProps {
  user?: User | null;
}

/**
 * Settings - The main settings component with tabs and content.
 */
export default function Settings({ user }: SiteHeaderProps) {
  const t = useTranslations("Settings");
  const [activeTab, setActiveTab] = useState<string>("account");

  return (
    <div className={cn("flex w-full flex-col items-stretch")}>
      <div className={cn("flex flex-col items-center gap-8 pt-10", "final:gap-6 final:pt-5")}>
        <h1
          className={cn(
            "w-full max-w-[1060px]",
            "font-onest text-[46px] font-medium leading-[50px] tracking-[-0.05px] text-[#1C1C1C]",
            "desktop:px-4 desktop:text-[26px] desktop:leading-[30px]",
            "final:text-[26px] final:leading-[30px]"
          )}
        >
          {t("title")}
        </h1>
        <SettingsTabs activeTab={activeTab} onTabChange={setActiveTab} />
      </div>
      <SettingsContent activeTab={activeTab} user={user} />
    </div>
  );
}
