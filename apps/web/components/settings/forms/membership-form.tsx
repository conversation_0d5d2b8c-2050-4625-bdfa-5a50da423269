'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { User } from '@supabase/supabase-js';
import { cn } from '@pdfily/ui/utils';
import { Button } from '@pdfily/ui/button';
import { DISCOUNT_PRICES, usePayment } from '@pdfily/payment';
import { useRouter } from '@/lib/i18n/navigation';
import { useAuth } from '@/components/session-provider';
import { useSupabase } from '@pdfily/supabase/hooks/use-supabase';

dayjs.extend(advancedFormat);

interface AccountFormProps {
  user?: User | null;
}

const features = [
  {
    icon: (
      <Image
        src={'/images/checkout/EditAndSaveYourPdfs_mobile.svg'}
        alt={'feature icon'}
        width={24}
        height={24}
        className={cn('object-contain', 'desktop:h-[22px] desktop:w-[22px]', 'final:h-[22px] final:w-[22px]')}
      />
    ),
    name: (
      <>
        Fill in, edit <br className="hidden desktop:hidden final:block" />& save your PDFs
      </>
    ),
    description: (
      <>
        Edit and customize your PDFs effortlessly with our <br className="desktop:hidden final:hidden" />
        powerful tools. Enjoy seamless updates and secure saving
      </>
    ),
  },
  {
    icon: (
      <Image
        src={'/images/checkout/CommentHighlight_mobile.svg'}
        alt={'feature icon'}
        width={26}
        height={26}
        className={cn('object-contain', 'desktop:h-6 desktop:w-6', 'final:h-6 final:w-6')}
      />
    ),
    name: (
      <>
        Comment, highlight <br className="hidden desktop:hidden final:block" />& underline the text
      </>
    ),
    description: (
      <>
        Make information memorable and <br className="hidden desktop:hidden final:block" />
        accessible: comment, highlight, <br className="hidden desktop:hidden final:block" />
        and underline to capture your insights
      </>
    ),
  },
  {
    icon: (
      <Image
        src={'/images/checkout/AccessFromAnyDevice_mobile.svg'}
        alt={'feature icon'}
        width={26}
        height={26}
        className={cn('object-contain', 'desktop:h-6 desktop:w-6', 'final:h-6 final:w-6')}
      />
    ),
    name: (
      <>
        Access your docs <br className="hidden desktop:hidden final:block" />
        from anywhere
      </>
    ),
    description: (
      <>Access your PDFs from any device with internet access. Work seamlessly across platforms and locations</>
    ),
  },
  {
    icon: (
      <Image
        src={'/images/checkout/SignYourDocsOnline_mobile.svg'}
        alt={'feature icon'}
        width={24}
        height={24}
        className={cn('object-contain', 'desktop:h-[22px] desktop:w-[22px]', 'final:h-[22px] final:w-[22px]')}
      />
    ),
    name: (
      <>
        Sign your <br className="hidden desktop:hidden final:block" />
        docs online
      </>
    ),
    description: (
      <>
        Sign PDFs digitally and effortlessly—save time, <br className="desktop:hidden final:hidden" />
        effort, and resources with seamless online signing
      </>
    ),
  },
  {
    icon: (
      <Image
        src={'/images/checkout/UseFormTemplates_mobile.svg'}
        alt={'feature icon'}
        width={26}
        height={26}
        className={cn('object-contain', 'desktop:h-6 desktop:w-6', 'final:h-6 final:w-6')}
      />
    ),
    name: (
      <>
        Use various <br className="hidden desktop:hidden final:block" />
        form templates
      </>
    ),
    description: (
      <>
        Effortlessly create polished forms in <br className="hidden desktop:hidden final:block" />
        minutes <br className="desktop:hidden final:hidden" />
        with a library of ready-to-use templates
      </>
    ),
  },
  {
    icon: (
      <Image
        src={'/images/checkout/CreateYourOwnForms_mobile.svg'}
        alt={'feature icon'}
        width={24}
        height={24}
        className={cn('object-contain', 'desktop:h-[22px] desktop:w-[22px]', 'final:h-[22px] final:w-[22px]')}
      />
    ),
    name: (
      <>
        Create your own
        <br className="hidden desktop:hidden final:block" /> form templates
      </>
    ),
    description: (
      <>
        Create personalized form templates with flexible tools. Customize layouts, add branding, and define unique
        fields
      </>
    ),
  },
];
const plans = [
  { name: 'Unlimited edits', image: '/images/checkout/UnlimitedEdits_mobile.svg' },
  { name: 'Unlimited downloads', image: '/images/checkout/UnlimitedDownloads_mobile.svg' },
  { name: 'Sign your docs online', image: '/images/checkout/SignYourDocsOnline_mobile.svg' },
  { name: 'Create your own forms', image: '/images/checkout/CreateYourOwnForms_mobile.svg' },
  { name: 'Convert to any format', image: '/images/checkout/ConvertToAnyFormat_mobile.svg' },
  { name: 'Access from any device', image: '/images/checkout/AccessFromAnyDevice_mobile.svg' },
];

export default function MembershipForm({ user }: AccountFormProps) {
  const supabase = useSupabase();
  const t = useTranslations('Settings.membership');
  const router = useRouter();
  const { service } = usePayment();
  const [subscription, setSubscription] = useState<any>();
  const [userSubscription, setUserSubscription] = useState<any>();
  const [isLoading, setIsLoading] = useState({
    cancelSubscrption: false,
    reactivateSubscription: false,
    createSubscription: false,
    switchToAnnualPlan: false,
  });
  const { setUser } = useAuth();

  useEffect(() => {
    if (!service) return;
    const fetchUserSubscription = async () => {
      try {
        const response = await fetch('/api/subscription');
        const data = await response.json();
        if (response.ok) {
          setUserSubscription(data);
          const currentSubscription = await service.getCurrentSubscription(data.subscription_id);
          setSubscription(currentSubscription);
        }
      } catch (error) {
        console.log(error);
      }
    };

    if (user) {
      fetchUserSubscription();
    }
  }, []);

  const handleCancelSubscription = async () => {
    try {
      if (!service) return;
      setIsLoading({ ...isLoading, cancelSubscrption: true });
      await service.cancelSubscription(subscription?.subscription.id);
      const updatedUser = await supabase.auth.getUser();
      setUser(updatedUser.data.user);
      setStatus('unsubscribed');
      setIsLoading({ ...isLoading, cancelSubscrption: false });
      router.refresh();
    } catch (error) {
      setIsLoading({ ...isLoading, cancelSubscrption: false });
    }
  };

  const handleReactivateSubscription = async () => {
    try {
      if (!service) return;
      setIsLoading({ ...isLoading, reactivateSubscription: true });
      await service.reactivateSubscription(subscription?.subscription.id);
      const updatedUser = await supabase.auth.getUser();
      setUser(updatedUser.data.user);
      setStatus('subscribed');
      setIsLoading({ ...isLoading, reactivateSubscription: false });
      router.refresh();
    } catch (error) {
      setIsLoading({ ...isLoading, reactivateSubscription: false });
    }
  };

  const handleCreateSubscription = useCallback(async () => {
    try {
      setIsLoading({ ...isLoading, createSubscription: true });
      await service?.createSubscription({
        customerId: subscription?.subscription.customer,
        priceId: subscription?.subscription?.items.data[0]?.price.id,
        paymentMethodId: subscription?.subscription?.default_payment_method,
        enforceNoTrial: true,
        metadata: {
          uid: user?.id,
          email: user?.email,
        },
      });
      setIsLoading({ ...isLoading, createSubscription: false });
    } catch (error) {
      setIsLoading({ ...isLoading, createSubscription: false });
      console.log(error);
    }
  }, [service, subscription, user]);

  const handleSubscribe = async () => {
    try {
      if (!!userSubscription?.has_paid_trial) {
        return await handleCreateSubscription();
      } else {
        router.push('/checkout');
      }
    } catch (error) {}
  };

  const handleSwitchToAnnualPlan = async () => {
    try {
      if (!service) return;
      setIsLoading({ ...isLoading, switchToAnnualPlan: true });
      await service.switchSubscriptionToAnnual(subscription?.subscription.id);
      const currentSubscription = await service.getCurrentSubscription(subscription?.subscription.id);
      setSubscription(currentSubscription);
      const updatedUser = await supabase.auth.getUser();
      setUser(updatedUser.data.user);
      setIsLoading({ ...isLoading, switchToAnnualPlan: false });
      router.refresh();
    } catch (error) {
      setIsLoading({ ...isLoading, switchToAnnualPlan: false });
      console.log(error);
    }
  };

  const [status, setStatus] = useState<
    | 'empty-state'
    | 'subscribed'
    | 'renew-subscribe'
    | 'change-plan'
    | 'cancel-subscribe'
    | 'cancel-subscribe-confirm'
    | 'unsubscribed'
    | 'create-subscription'
  >('empty-state');

  const [isSubmitting, setIsSubmitting] = useState(false);

  return (
    <>
      {!user?.user_metadata.is_subscribed && status !== 'unsubscribed' ? (
        <div
          className={cn(
            'flex w-full max-w-[1060px] flex-col rounded-3xl bg-white p-8 shadow-[2px_8px_14px_0px_#6881B114]',
            'desktop:mx-4 desktop:p-4',
            'final:mx-4 final:p-4',
          )}
        >
          <h3
            className={cn(
              'mb-3 font-onest text-[32px] font-medium leading-9 tracking-[-0.05px] text-[#1C1C1C]',
              'desktop:mb-2 desktop:text-xl desktop:leading-6',
              'final:mb-2 final:text-xl final:leading-6',
            )}
          >
            {t('title')}
          </h3>
          <p
            className={cn(
              'mb-6 font-onest text-lg font-normal leading-[22px] text-[#585858]',
              'desktop:mb-5 desktop:text-sm desktop:leading-[19px]',
              'final:mb-5 final:text-sm final:leading-[19px]',
            )}
          >
            {t('subscription.noSubscriptionTitle')}
          </p>

          <div
            className={cn(
              'w-[611px] rounded-[10px] border border-[#E7E7E7] p-[26px]',
              'desktop:w-full desktop:p-4',
              'final:w-full final:p-4',
            )}
          >
            <h4
              className={cn(
                'mb-2.5 font-onest text-2xl font-medium leading-[30px] tracking-[-0.05px] text-[#1C1C1C]',
                'desktop:mb-2 desktop:text-lg desktop:leading-[22px]',
                'final:mb-2 final:text-lg final:leading-[22px]',
              )}
            >
              {t('unlockAllTools')}
            </h4>
            <p
              className={cn(
                'mb-[26px] font-onest text-[16px] font-normal leading-[22px] text-[#585858]',
                'desktop:mb-6 desktop:text-sm desktop:leading-[18px]',
                'final:mb-6 final:text-sm final:leading-[18px]',
              )}
            >
              {t('ctaDescription')}
            </p>

            <Button
              type="submit"
              disabled={isLoading.createSubscription}
              className={cn(
                'flex h-[50px] w-[137px] items-center justify-center rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-medium leading-5 text-white hover:bg-[#d83e15]',
                'desktop:h-12 desktop:w-full',
                'final:h-12 final:w-full',
              )}
              onClick={handleSubscribe}
            >
              {isLoading.createSubscription ? 'Saving...' : t('subscribeButton')}
            </Button>
          </div>
        </div>
      ) : user?.user_metadata.is_subscribed &&
        status !== 'cancel-subscribe' &&
        status !== 'cancel-subscribe-confirm' ? (
        <>
          {user?.user_metadata.scheduled_cancel_at && user?.user_metadata.scheduled_cancel_at > 0 ? (
            <div className={cn('flex w-full justify-center px-4')}>
              <div
                className={cn(
                  'flex w-[520px] flex-col rounded-2xl bg-white p-6 shadow-[2px_8px_20px_0px_#84899226]',
                  'desktop:w-full desktop:p-4',
                  'final:w-full final:p-4',
                )}
              >
                <div className={cn('mb-8 flex items-center justify-center', 'desktop:mb-5', 'final:mb-5')}>
                  <Image
                    src={'/images/settings/Check.svg'}
                    alt={'check'}
                    width={80}
                    height={80}
                    className={cn('object-contain', 'desktop:h-16 desktop:w-16', 'final:h-16 final:w-16')}
                  />
                </div>

                <h3
                  className={cn(
                    'mb-2 text-center font-onest text-[26px] font-medium leading-8 tracking-[-0.05px] text-[#1C1C1C]',
                    'desktop:text-xl desktop:leading-[26px]',
                    'final:text-xl final:leading-[26px]',
                  )}
                >
                  {t('subscription.canceled')}
                </h3>

                <p
                  className={cn(
                    'mb-9 text-center font-onest text-[16px] font-normal leading-[22px] text-[#585858]',
                    'desktop:mb-6 desktop:text-sm desktop:leading-5',
                    'final:mb-6 final:text-sm final:leading-5',
                  )}
                >
                  Your subscription is active until{' '}
                  {dayjs(user?.user_metadata?.scheduled_cancel_at).format('Do MMMM, YYYY')}.{' '}
                  <br className="final:hidden" />
                  Thanks for using our product!
                </p>

                <Button
                  disabled={isLoading.reactivateSubscription}
                  className={cn(
                    'flex h-[50px] w-full items-center justify-center rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-medium leading-5 text-white hover:bg-[#d83e15]',
                    'desktop:h-12',
                    'final:h-12',
                  )}
                  onClick={handleReactivateSubscription}
                >
                  {isLoading.reactivateSubscription ? 'Activating Your Subscription' : t('subscription.reactivate')}
                </Button>
              </div>
            </div>
          ) : (
            <div
              className={cn(
                'flex w-full max-w-[1060px] flex-col rounded-3xl bg-white p-8 shadow-[2px_8px_14px_0px_#6881B114]',
                'desktop:mx-4 desktop:w-[calc(100%-2rem)] desktop:p-4',
                'final:mx-4 final:w-[calc(100%-2rem)] final:p-4',
              )}
            >
              <h3
                className={cn(
                  'mb-3 font-onest text-[32px] font-medium leading-9 tracking-[-0.05px] text-[#1C1C1C]',
                  'desktop:mb-2 desktop:text-xl desktop:leading-6',
                  'final:mb-2 final:text-xl final:leading-6',
                )}
              >
                {t('title')}
              </h3>
              <p
                className={cn(
                  'mb-6 font-onest text-lg font-normal leading-[22px] text-[#585858]',
                  'desktop:mb-4 desktop:text-sm desktop:leading-[19px]',
                  'final:mb-4 final:text-sm final:leading-[19px]',
                )}
              >
                {t('subscription.manage')}
              </p>

              <div className={cn('mb-6 h-px w-full bg-[#E7E7E7]', 'desktop:mb-4', 'final:mb-4')}></div>

              <h4
                className={cn(
                  'mb-5 font-onest text-xl font-medium leading-6 tracking-[-0.05px] text-[#1C1C1C]',
                  'desktop:text-lg desktop:leading-[22px]',
                  'final:text-lg final:leading-[22px]',
                )}
              >
                {t.rich('subscription.planIncludes', {
                  br: () => <br className="hidden desktop:block final:block" />,
                })}
              </h4>

              <div
                className={cn(
                  'mb-8 grid grid-cols-2 gap-x-4 gap-y-4',
                  'desktop:mb-[29px] desktop:grid-cols-1 desktop:gap-y-2.5',
                  'final:mb-[29px] final:grid-cols-1 final:gap-y-2.5',
                )}
              >
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className={cn(
                      'flex items-start gap-3.5 rounded-[10px] border border-[#E7E7E7] p-4',
                      'desktop:flex-col desktop:gap-3 desktop:p-3',
                      'final:flex-col final:gap-3 final:p-3',
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={cn(
                          'flex h-[46px] w-[46px] items-center justify-center rounded-lg border-[0.5px] border-[#F6F6F6] shadow-[0px_2px_6px_0px_#042C5714]',
                          'desktop:h-11 desktop:w-11',
                          'final:h-11 final:w-11',
                        )}
                      >
                        {feature.icon}
                      </div>

                      <p
                        className={cn(
                          'font-onest text-[16px] font-medium leading-[22px] text-[#1C1C1C]',
                          'hidden desktop:block final:block',
                        )}
                      >
                        {feature.name}
                      </p>
                    </div>
                    <div className="flex flex-1 flex-col gap-1">
                      <p
                        className={cn(
                          'font-onest text-[16px] font-medium leading-[22px] text-[#1C1C1C]',
                          'desktop:hidden final:hidden',
                        )}
                      >
                        {feature.name}
                      </p>
                      <p className="font-onest text-sm font-normal leading-5 text-[#585858]">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>

              <h3
                className={cn(
                  'mb-3 font-onest text-2xl font-medium leading-7 tracking-[-0.05px] text-[#1C1C1C]',
                  'desktop:mb-2 desktop:text-lg desktop:leading-[22px]',
                  'final:mb-2 final:text-lg final:leading-[22px]',
                )}
              >
                {t('subscription.planBillingSettings')}
              </h3>
              <p
                className={cn(
                  'mb-5 font-onest text-[16px] font-normal leading-5 text-[#585858]',
                  'desktop:mb-4 desktop:text-sm desktop:leading-[18px]',
                  'final:mb-4 final:text-sm final:leading-[18px]',
                )}
              >
                {t('privateInfoNote')}
              </p>

              <div
                className={cn(
                  'mb-5 flex w-[490px] flex-col gap-3 rounded-[10px] border border-[#E7E7E7] p-5',
                  'font-onest text-sm font-normal leading-[18px] text-[#1C1C1C]',
                  'desktop:mb-4 desktop:w-full desktop:p-3',
                  'final:mb-4 final:w-full final:p-3',
                )}
              >
                <p>
                  Your current plan: <br className="hidden desktop:block final:block" />
                  <span className="font-semibold">
                    {subscription?.planName} membership for <span className="font-medium">${subscription?.amount}</span>
                  </span>
                </p>
                <p>
                  {user?.user_metadata?.scheduled_cancel_at ? (
                    <span>
                      Your subsription will be cancelled on{' '}
                      <span className="font-medium">
                        {dayjs(user?.user_metadata?.scheduled_cancel_at).format('DD/MM/YYYY')}
                      </span>
                    </span>
                  ) : (
                    <span>
                      Your next payment will be on{' '}
                      <span className="font-medium">
                        {dayjs(user?.user_metadata.current_period_end).format('DD/MM/YYYY')}
                      </span>
                    </span>
                  )}
                </p>
              </div>

              {user?.user_metadata.is_subscribed ? (
                <>
                  <div
                    className={cn(
                      'flex items-center gap-6',
                      'font-onest text-sm font-medium leading-[18px] tracking-[-0.05px] text-[#1C1C1C] underline',
                      'desktop:mb-5',
                      'final:mb-5',
                    )}
                  >
                    {false && (
                      <p onClick={() => setStatus('change-plan')} className="cursor-pointer">
                        {t('subscription.change')}
                      </p>
                    )}
                    {!user?.user_metadata?.scheduled_cancel_at && (
                      <p onClick={() => setStatus('cancel-subscribe')} className="cursor-pointer">
                        {t('subscription.cancel')}
                      </p>
                    )}
                    {subscription?.interval === 'week' && (
                      <p onClick={handleSwitchToAnnualPlan} className="cursor-pointer desktop:hidden final:hidden">
                        {isLoading.switchToAnnualPlan ? 'Switching...' : t('subscription.switchToAnnual')}
                      </p>
                    )}
                  </div>
                  {subscription?.interval === 'week' && (
                    <p
                      onClick={handleSwitchToAnnualPlan}
                      className="hidden cursor-pointer font-onest text-sm font-medium leading-[18px] tracking-[-0.05px] text-[#1C1C1C] underline desktop:block final:block"
                    >
                      {isLoading.switchToAnnualPlan ? 'Switching...' : t('subscription.switchToAnnual')}
                    </p>
                  )}
                </>
              ) : (
                <p
                  onClick={() => setStatus('renew-subscribe')}
                  className="hidden cursor-pointer font-onest text-sm font-medium leading-[18px] tracking-[-0.05px] text-[#F0401D] underline desktop:block final:block"
                >
                  {t('subscription.renew')}
                </p>
              )}
            </div>
          )}
        </>
      ) : status === 'cancel-subscribe' ? (
        <div className={cn('flex flex-col gap-10', 'desktop:gap-[26px]', 'final:gap-[26px]')}>
          <h3
            className={cn(
              'leading-11 text-center font-onest text-[40px] font-medium tracking-[-0.05px] text-[#1C1C1C]',
              'desktop:text-[26px] desktop:leading-8',
              'final:text-[26px] final:leading-8',
            )}
          >
            {t('subscription.switchOrCancel')}
          </h3>

          <div
            className={cn(
              'flex w-full max-w-[1060px] items-start gap-5',
              'desktop:flex-col-reverse',
              'final:flex-col-reverse',
            )}
          >
            <div
              className={cn(
                'flex w-[430px] flex-col rounded-2xl bg-white p-6 shadow-[2px_8px_20px_0px_#84899226]',
                'desktop:mx-4 desktop:w-[calc(100%-2rem)] desktop:p-4',
                'final:mx-4 final:w-[calc(100%-2rem)] final:p-4',
              )}
            >
              <h3
                className={cn(
                  'mb-5 text-center font-onest text-[26px] font-medium leading-8 tracking-[-0.05px] text-[#1C1C1C]',
                  'desktop:mb-3 desktop:text-xl desktop:leading-[26px]',
                  'final:mb-3 final:text-xl final:leading-[26px]',
                )}
              >
                {t('subscription.confirmCancel')}
              </h3>

              <p
                className={cn(
                  'mb-8 text-center font-onest text-[16px] font-normal leading-[22px] text-[#585858]',
                  'desktop:mb-6 desktop:text-sm desktop:leading-5',
                  'final:mb-6 final:text-sm final:leading-5',
                )}
              >
                After canceling your subscription, you will have access to premium features until the end of your
                billing preriod on{' '}
                <span className="font-medium text-[#1C1C1C]">
                  {dayjs(user?.user_metadata.current_period_end).format('DD MMMM YYYY')}
                </span>
                .
              </p>

              <Button
                className={cn(
                  'mb-1.5 flex h-[50px] w-full items-center justify-center rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-medium leading-5 text-white hover:bg-[#d83e15]',
                  'desktop:h-12',
                  'final:h-12',
                )}
                onClick={() => setStatus('subscribed')}
              >
                {t('subscription.keep')}
              </Button>

              <Button
                variant={'outline'}
                className={cn(
                  'flex h-[50px] w-full items-center justify-center rounded-[10px] border-none font-onest text-[16px] font-medium leading-5 text-[#1C1C1C]',
                  'desktop:h-12',
                  'final:h-12',
                )}
                onClick={() => setStatus('cancel-subscribe-confirm')}
              >
                {t('subscription.cancel')}
              </Button>
            </div>

            <div
              className={cn(
                'flex w-[610px] flex-col rounded-2xl bg-white p-6 shadow-[2px_8px_20px_0px_#84899226]',
                'desktop:mx-4 desktop:w-[calc(100%-2rem)] desktop:p-4',
                'final:mx-4 final:w-[calc(100%-2rem)] final:p-4',
              )}
            >
              <h3
                className={cn(
                  'mb-6 font-onest text-[26px] font-medium leading-[30px] tracking-[-1px] text-[#1C1C1C]',
                  'desktop:mb-5 desktop:text-xl desktop:leading-[26px] desktop:tracking-normal',
                  'final:mb-5 final:text-xl final:leading-[26px] final:tracking-normal',
                )}
              >
                Subscription includes <br className="hidden desktop:block final:block" />
                all these tools and more:
              </h3>

              <div
                className={cn(
                  'grid grid-cols-2 gap-x-3 gap-y-3',
                  'desktop:grid-cols-1 desktop:gap-y-2',
                  'final:grid-cols-1 final:gap-y-2',
                )}
              >
                {plans.map((plan, index) => (
                  <div
                    key={index}
                    className={cn(
                      'flex items-center gap-3.5 rounded-[10px] border border-[#E7E7E7] p-4',
                      'desktop:gap-3 desktop:p-3',
                      'final:gap-3 final:p-3',
                    )}
                  >
                    <div
                      className={cn(
                        'flex h-10 w-10 items-center justify-center rounded-lg border-[0.5px] border-[#F6F6F6] shadow-[0px_2px_6px_0px_#042C5714]',
                        'desktop:h-[34px] desktop:w-[34px]',
                        'final:h-[34px] final:w-[34px]',
                      )}
                    >
                      <Image
                        src={plan.image}
                        alt={'plan icon'}
                        width={20}
                        height={20}
                        className={cn(
                          'object-contain',
                          'desktop:h-[18px] desktop:w-[18px]',
                          'final:h-[18px] final:w-[18px]',
                        )}
                      />
                    </div>
                    <p className={cn('font-onest text-[16px] font-medium leading-[22px] text-[#1C1C1C]')}>
                      {plan.name}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : status === 'cancel-subscribe-confirm' ? (
        <div className={cn('flex w-full flex-col items-center px-4')}>
          <h3
            className={cn(
              'leading-11 mb-4 text-center font-onest text-[40px] font-medium tracking-[-0.05px] text-[#1C1C1C]',
              'desktop:mb-3 desktop:text-[26px] desktop:leading-8',
              'final:mb-3 final:text-[26px] final:leading-8',
            )}
          >
            {t('offer.specialOffer')}
          </h3>

          <p
            className={cn(
              'mb-10 text-center font-onest text-xl font-normal leading-6 text-[#585858]',
              'desktop:mb-[26px] desktop:text-[16px] desktop:leading-[22px]',
              'final:mb-[26px] final:text-[16px] final:leading-[22px]',
            )}
          >
            {t('offer.stayWithUs')}
          </p>

          <div
            className={cn(
              'flex w-[520px] flex-col rounded-2xl bg-white p-6 shadow-[2px_8px_20px_0px_#84899226]',
              'desktop:w-full desktop:p-4',
              'final:w-full final:p-4',
            )}
          >
            <h3
              className={cn(
                'mb-2 text-center font-onest text-[26px] font-medium leading-8 tracking-[-0.05px] text-[#1C1C1C]',
                'desktop:text-xl desktop:leading-[26px]',
                'final:text-xl final:leading-[26px]',
              )}
            >
              {t('offer.exclusiveDeal')}
            </h3>

            <p
              className={cn(
                'mb-6 text-center font-onest text-[16px] font-normal leading-[22px] text-[#585858]',
                'desktop:mb-[19px] desktop:text-sm desktop:leading-5',
                'final:mb-[19px] final:text-sm final:leading-5',
              )}
            >
              {t('offer.unlockDiscount')}
            </p>

            <div
              className={cn(
                'mb-8 flex items-center gap-4',
                'desktop:mb-6 desktop:flex-col',
                'final:mb-6 final:flex-col',
              )}
            >
              <div
                className={cn(
                  'flex w-[167px] flex-col items-center gap-2.5 rounded-[10px] border border-[#1FC7941A] bg-[#1FC7941A] px-4 py-3 shadow-[0px_4px_16px_0px_#282C3C1A]',
                  'desktop:w-full desktop:items-center',
                  'final:w-full final:items-center',
                )}
              >
                <p className="font-onest text-sm font-medium leading-[18px] text-[#1FC794]">Your price now!</p>
                <p
                  className={cn(
                    'flex items-baseline gap-1.5 font-onest text-[24px] font-medium leading-10 text-[#1FC794]',
                    'desktop:text-4xl',
                    'final:text-4xl',
                  )}
                >
                  {subscription?.interval === 'week'
                    ? DISCOUNT_PRICES.us?.fourWeekly?.formatted
                    : DISCOUNT_PRICES.us?.annual?.formatted}
                  <span
                    className={cn(
                      'relative inline-block',
                      "after:absolute after:left-0 after:right-0 after:top-1/2 after:mt-px after:h-[0.5px] after:-translate-y-1/2 after:bg-[#585858] after:content-['']",
                      'text-lg leading-6 text-[#585858]',
                      'desktop:text-lg',
                      'final:text-lg',
                    )}
                  >
                    ${subscription?.amount}{' '}
                  </span>
                </p>
              </div>
              <ul className="flex flex-1 flex-col gap-3">
                {[
                  'Enjoy endless downloads & editing',
                  'Sign and secure your documents',
                  'Create and share your own forms',
                ].map((feature, idx) => (
                  <li key={idx} className="flex items-start gap-2">
                    <Image
                      src="/images/checkout/CircleCheck_outline.svg"
                      alt="Check"
                      width={20}
                      height={20}
                      className={cn(
                        'object-contain',
                        'desktop:h-[18px] desktop:w-[18px]',
                        'final:h-[18px] final:w-[18px]',
                      )}
                    />
                    <span
                      className={cn(
                        'font-onest text-[16px] font-normal leading-5 text-[#585858]',
                        'desktop:text-sm desktop:leading-[18px]',
                        'final:text-sm final:leading-[18px]',
                      )}
                    >
                      {feature}
                    </span>
                  </li>
                ))}
              </ul>
            </div>

            <Button
              className={cn(
                'mb-1.5 flex h-[50px] w-full items-center justify-center rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-medium leading-5 text-white hover:bg-[#d83e15]',
                'desktop:h-12',
                'final:h-12',
              )}
              onClick={() => setStatus('subscribed')}
            >
              {t('offer.claimButton')}
            </Button>

            <Button
              variant={'outline'}
              disabled={isLoading.cancelSubscrption}
              className={cn(
                'flex h-[50px] w-full items-center justify-center rounded-[10px] border-none font-onest text-[16px] font-medium leading-5 text-[#1C1C1C]',
                'desktop:h-12',
                'final:h-12',
              )}
              onClick={handleCancelSubscription}
            >
              {isLoading.cancelSubscrption ? 'Cancelling' : t('subscription.cancelAnywayButton')}
            </Button>
          </div>
        </div>
      ) : status === 'unsubscribed' ? (
        <div></div>
      ) : null}
    </>
  );
}
