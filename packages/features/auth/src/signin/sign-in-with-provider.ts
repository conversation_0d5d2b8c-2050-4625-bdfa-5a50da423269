import Swal from 'sweetalert2';
import { ProviderType } from '@pdfily/supabase/types';

/**
 * Initiates sign-in with the specified OAuth provider by sending a POST request to the server.
 *
 * This function sends the selected provider and redirect path to the API route `/api/auth/sign-in-with-provider`,
 * then redirects the user to the provider's authentication page if successful.
 *
 * If the request fails or the response does not include a redirect URL, it displays an error using SweetAlert.
 *
 * @param {ProviderType} provider - The name of the OAuth provider (e.g., 'google', 'github')
 * @param {string} path - The path to redirect to after successful authentication
 */
export const signInWithProviderAction = async (provider: ProviderType, path: string) => {
  try {
    const response = await fetch('/api/auth/sign-in-with-provider', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ provider, path }),
    });

    const data = await response.json();

    if (data.url) {
      window.location.href = data.url;
    } else {
      throw new Error(data.error || 'Authentication failed');
    }
  } catch (error) {
    console.error('Google sign-in error:', error);
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: `Failed to sign in with ${provider}. Please try again.`,
    });
  }
};

/**
 * Initiates sign-in with the specified OAuth provider using a popup window.
 *
 * This function opens a popup window for OAuth authentication, listens for the authentication
 * result via BroadcastChannel, and handles the redirect after successful authentication.
 *
 * @param {ProviderType} provider - The name of the OAuth provider (e.g., 'google', 'github')
 * @param {string} path - The path to redirect to after successful authentication
 * @returns {Promise<boolean>} - Returns true if authentication was successful, false otherwise
 */
export const signInWithProviderPopupAction = async (provider: ProviderType, path: string): Promise<boolean> => {
  try {
    // Get the OAuth URL from the server
    const response = await fetch('/api/auth/sign-in-with-provider-popup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ provider, path }),
    });

    const data = await response.json();

    if (!data.url) {
      throw new Error(data.error || 'Failed to get authentication URL');
    }

    // Open popup window
    const popup = openAuthPopup(data.url);
    if (!popup) {
      throw new Error('Failed to open popup window');
    }

    // Wait for authentication result
    const authResult = await waitForAuthResult(popup);

    if (authResult.success && authResult.code) {
      // Exchange code for session
      const exchangeResponse = await fetch(`/api/auth/popup-callback?code=${authResult.code}`);
      const exchangeData = await exchangeResponse.json();

      if (exchangeData.success) {
        // Redirect to the specified path
        window.location.href = path;
        return true;
      } else {
        throw new Error(exchangeData.error || 'Failed to exchange code for session');
      }
    } else {
      throw new Error(authResult.error || 'Authentication was cancelled or failed');
    }
  } catch (error) {
    console.error('Popup sign-in error:', error);
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: `Failed to sign in with ${provider}. Please try again.`,
    });
    return false;
  }
};

/**
 * Opens a popup window for OAuth authentication.
 *
 * @param {string} url - The OAuth URL to open in the popup
 * @returns {Window | null} - The popup window reference or null if failed to open
 */
const openAuthPopup = (url: string): Window | null => {
  const width = 500;
  const height = 600;
  const left = window.screen.width / 2 - width / 2;
  const top = window.screen.height / 2 - height / 2;

  const windowFeatures = `scrollbars=no,resizable=no,copyhistory=no,width=${width},height=${height},top=${top},left=${left}`;
  return window.open(url, 'oauth-popup', windowFeatures);
};

/**
 * Waits for authentication result from the popup window using BroadcastChannel.
 *
 * @param {Window} popup - The popup window reference
 * @returns {Promise<{success: boolean, code?: string, error?: string}>} - Authentication result
 */
const waitForAuthResult = (popup: Window): Promise<{ success: boolean; code?: string; error?: string }> => {
  return new Promise((resolve) => {
    const channel = new BroadcastChannel('oauth-popup-channel');

    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== window.location.origin) return;

      const { authResultCode, error } = event.data;

      // Clean up
      channel.removeEventListener('message', handleMessage);
      channel.close();

      if (popup && !popup.closed) {
        popup.close();
      }

      if (authResultCode) {
        resolve({ success: true, code: authResultCode });
      } else {
        resolve({ success: false, error: error || 'Authentication failed' });
      }
    };

    // Listen for popup close without authentication
    const checkClosed = setInterval(() => {
      if (popup.closed) {
        clearInterval(checkClosed);
        channel.removeEventListener('message', handleMessage);
        channel.close();
        resolve({ success: false, error: 'Authentication was cancelled' });
      }
    }, 1000);

    channel.addEventListener('message', handleMessage);
  });
};
