# @pdfily/eslint-config

Shared ESLint configurations for the pdfily monorepo.

## Usage

Install in your package:

```sh
pnpm add -D @pdfily/eslint-config
```

### Flat Config Example (base)

```js
// eslint.config.js
import { config as baseConfig } from '@pdfily/eslint-config/base';
export default baseConfig;
```

### Next.js Example

```js
import { nextJsConfig } from '@pdfily/eslint-config/next-js';
export default nextJsConfig;
```

### React (internal) Example

```js
import { config as reactConfig } from '@pdfily/eslint-config/react-internal';
export default reactConfig;
```

## Notes

- Ensure you have all required peer dependencies installed in your project.
- See `package.json` for the list of required plugins and versions.
