#!/bin/bash

# Absolute path to the root of the repo
ROOT="$(git rev-parse --show-toplevel)"

# Load environment variables from .env file located inside apps/web
set -a
[ -f "$ROOT/apps/web/.env" ] && source "$ROOT/apps/web/.env"
set +a

# Default bucket name if not provided in .env
BUCKET_NAME=${S3_BUCKET:-uploads}

# Absolute path to the root of the repo
ROOT="$(git rev-parse --show-toplevel)"

# Migration directory path
MIGRATION_DIR="$ROOT/apps/web/supabase/migrations"

# Create the migration directory if it doesn't exist
mkdir -p "$MIGRATION_DIR"

# Generate a timestamp in the format used by Supabase (YYYYMMDDHHMMSS)
TIMESTAMP=$(date -u +"%Y%m%d%H%M%S")
FILENAME="${TIMESTAMP}_create_storage_bucket.sql"
FILE_PATH="$MIGRATION_DIR/$FILENAME"

# Create or overwrite the migration file with the bucket creation template
cat > "$FILE_PATH" <<EOF
-- Ensure the storage schema exists
create schema if not exists storage;

-- Create a new bucket by directly inserting into the storage.buckets table
insert into storage.buckets (id, name, created_at, updated_at, public)
select
  '{{BUCKET_NAME}}',  -- Bucket ID and name
  '{{BUCKET_NAME}}',  -- Bucket name
  now(),              -- Current timestamp for created_at
  now(),              -- Current timestamp for updated_at
  false               -- Set public access to false (private bucket)
where not exists (
  select 1 from storage.buckets where name = '{{BUCKET_NAME}}'
);

EOF

# Confirmation message
echo "✅ Created migration file: $FILE_PATH"

# Replace the placeholder with the actual bucket name
if [[ "$OSTYPE" == "darwin"* ]]; then
  # macOS (uses BSD sed)
  sed -i '' "s/{{BUCKET_NAME}}/$BUCKET_NAME/g" "$FILE_PATH"
else
  # Linux and others (uses GNU sed)
  sed -i.bak "s/{{BUCKET_NAME}}/$BUCKET_NAME/g" "$FILE_PATH"
  # Remove the .bak file created by sed
  rm -f "$FILE_PATH.bak"
fi

echo "🚀 Bucket name set to: $BUCKET_NAME in migration file"
