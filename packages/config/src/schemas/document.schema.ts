import { z } from "zod";

export const UpdateDocumentPropertiesSchema = z.object({
    description: z.string().nullable().optional(),
    is_public: z.boolean().optional(),
    name: z.string().min(1, 'Name cannot be empty').max(255, 'Name is too long').optional(),
    title: z.string().min(1, 'Title cannot be empty').max(255, 'Title is too long').optional(),
}).strict(); // strict() avoid additional fiels.