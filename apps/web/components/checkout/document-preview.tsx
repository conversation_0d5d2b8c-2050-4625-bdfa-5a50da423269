'use client';

import { useEffect, useState, useCallback } from 'react';
import { Viewer, Worker } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';
import { cn } from '@pdfily/ui/utils';

interface DocumentPreviewProps {
  documentId: string | null;
  className?: string;
  onDocumentLoaded?: () => void;
  onError?: (error: Error) => void;
}

interface PreviewToken {
  token: string;
  expiresAt: number;
}

export default function DocumentPreview({ documentId, className, onDocumentLoaded, onError }: DocumentPreviewProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fileData, setFileData] = useState<Uint8Array | null>(null);

  const generatePreviewToken = useCallback(async (docId: string): Promise<string | null> => {
    try {
      const response = await fetch(`/api/v1/files/${encodeURIComponent(docId)}/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to generate preview token');
      }

      const { token }: PreviewToken = await response.json();
      return token;
    } catch (error) {
      console.error('Token generation error:', error);
      throw error;
    }
  }, []);

  const fetchFileData = useCallback(async () => {
    if (!documentId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Step 1: Generate the secure token
      const token = await generatePreviewToken(documentId);
      if (!token) {
        throw new Error('Unable to generate preview token');
      }

      // Step 2: Use the token to fetch the document
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 seconds timeout

      const response = await fetch(`/api/v1/files/${encodeURIComponent(documentId)}/preview`, {
        method: 'GET',
        headers: {
          Accept: 'application/pdf',
          Authorization: `Bearer ${token}`,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        // Handle specific errors
        const errorData = await response.json().catch(() => ({}));
        let errorMessage = 'File retrieving failed';

        switch (response.status) {
          case 401:
            errorMessage = 'You must be signed in to view this document';
            break;
          case 403:
            errorMessage = 'Access denied. Invalid or expired token';
            break;
          case 404:
            errorMessage = 'Document not found or you do not have access to it';
            break;
          case 500:
            errorMessage = 'Server error. Please try again later';
            break;
          default:
            errorMessage = errorData.message || errorMessage;
        }

        setError(errorMessage);
      }

      const arrayBuffer = await response.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      setFileData(uint8Array);
      onDocumentLoaded?.();
    } catch (error) {
      let errorMessage = 'An error occurred while loading the preview';

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Request timeout. Please try again';
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);
      onError?.(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [documentId, generatePreviewToken, onDocumentLoaded, onError]);

  const handleRetry = useCallback(() => {
    setError(null);
    setFileData(null);
    fetchFileData();
  }, [fetchFileData]);

  useEffect(() => {
    fetchFileData();
  }, [fetchFileData]);

  if (error) {
    return (
      <div className={cn('flex flex-1 items-center justify-center p-4', className)}>
        <div className="text-center max-w-md">
          <div className="text-red-500 font-onest text-[16px] mb-2">
            An error occurred while trying to load the preview
          </div>
          <div className="text-[#585858] font-onest text-[14px] mb-4 break-words">{error}</div>
          <button
            onClick={handleRetry}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors font-onest"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={cn('flex flex-1 items-center justify-center p-4', className)}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500 mx-auto mb-4"></div>
          <span className="font-onest text-[16px] text-[#585858]">Generating preview...</span>
        </div>
      </div>
    );
  }

  if (!documentId || !fileData) {
    return (
      <div className={cn('flex flex-1 items-center justify-center p-4', className)}>
        <div className="text-center">
          <span className="font-onest text-[16px] text-[#585858]">No document selected</span>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-1 flex-col w-full h-full', className)}>
      <Worker workerUrl="https://unpkg.com/pdfjs-dist@2.12.313/build/pdf.worker.min.js">
        <div className="rpv-core__viewer" style={{ width: '100%', height: '100%' }}>
          <Viewer
            fileUrl={fileData}
            onDocumentLoad={onDocumentLoaded}
            renderError={(error) => (
              <div className="flex items-center justify-center p-4 h-full">
                <div className="text-center">
                  <div className="text-red-500 font-onest text-[16px] mb-2">PDF rendering error</div>
                  <div className="text-[#585858] font-onest text-[14px] mb-4">
                    The PDF file appears to be corrupted or in an unsupported format
                  </div>
                  <button
                    onClick={handleRetry}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors font-onest"
                  >
                    Reload document
                  </button>
                </div>
              </div>
            )}
          />
        </div>
      </Worker>
    </div>
  );
}
