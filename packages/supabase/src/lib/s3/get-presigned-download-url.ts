import { GetObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { RequestPresigningArguments } from "@smithy/types";
import { getSupabaseS3Client } from "../../clients/s3-client";

/**
 * Generates a signed URL for downloading a file from a Supabase S3-compatible bucket.
 *
 * This URL allows temporary, secure access to a private file using HTTP GET.
 *
 * @param {string} bucket - The name of the bucket containing the file.
 * @param {string} fileKey - The path of the file inside the bucket.
 * @param {RequestPresigningArguments} [options] - Optional configuration for expiration (defaults to 3600s).
 * @returns {Promise<string | null>} - The generated signed URL, or `null` if an error occurs.
 *
 * @throws {Error} - If the bucket name or file key is missing.
 *
 * @example
 * const url = await getPresignedDownloadUrl("uploads", "documents/abc123/sample.pdf");
 * if (url) window.open(url, '_blank');
 */
export async function getPresignedDownloadUrl(
  bucket: string,
  fileKey: string,
  options: RequestPresigningArguments = { expiresIn: 3600 }
): Promise<string | null> {
  if (!bucket || !fileKey) {
    console.error("❌ Bucket and file key are required for generating a signed download URL.");
    throw new Error("Bucket and file key are required.");
  }

  try {
    const s3Client: S3Client = getSupabaseS3Client();
    const command = new GetObjectCommand({ Bucket: bucket, Key: fileKey });
    const signedUrl = await getSignedUrl(s3Client, command, options);
    return signedUrl;
  } catch (error) {
    console.error(`❌ Failed to generate signed download URL for "${fileKey}":`, error);
    return null;
  }
}

