'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

import { cn } from '@pdfily/ui/utils';
import { Button } from '@pdfily/ui/button';
import { Link } from '@/lib/i18n/navigation';

const items = [
  {
    category: 'edit',
    items: [
      {
        name: 'protect',
        title: 'Protect PDF',
        icon: '/images/landing-page/tools-section/ProtectPDF.svg',
      },
      {
        name: 'watermark',
        title: 'Watermark PDF',
        icon: '/images/landing-page/tools-section/AddWatermark.svg',
      },
      {
        name: 'split',
        title: 'Split PDF',
        icon: '/images/landing-page/tools-section/SplitPDFs.svg',
      },
      {
        name: 'merge',
        title: 'Merge PDF',
        icon: '/images/landing-page/tools-section/MergePDFs.svg',
      },
      {
        name: 'rotate',
        title: 'Rotate PDF',
        icon: '/images/landing-page/tools-section/RotatePDF.svg',
      },
      {
        name: 'sign',
        title: 'Sign PDF',
        icon: '/images/landing-page/tools-section/SignPDF.svg',
      },
      {
        name: 'compress',
        title: 'Compress PDF',
        icon: '/images/landing-page/tools-section/CompressPDF.svg',
      },
    ],
  },
  {
    category: 'convertTo',
    items: [
      {
        name: 'wordToPdf',
        title: 'Word to PDF',
        icon: '/images/landing-page/tools-section/Word2PDF.svg',
      },
      {
        name: 'powerPointToPdf',
        title: 'PowerPoint to PDF',
        icon: '/images/landing-page/tools-section/PowerPoint2PDF.svg',
      },
      {
        name: 'excelToPdf',
        title: 'Excel to PDF',
        icon: '/images/landing-page/tools-section/Excel2PDF.svg',
      },
      {
        name: 'jpgToPdf',
        title: 'JPG to PDF',
        icon: '/images/landing-page/tools-section/JPG2PDF.svg',
      },
    ],
  },
  {
    category: 'convertFrom',
    items: [
      {
        name: 'pdfToWord',
        title: 'PDF to Word',
        icon: '/images/landing-page/tools-section/PDF2Word.svg',
      },
      {
        name: 'pdfToPowerPoint',
        title: 'PDF to PowerPoint',
        icon: '/images/landing-page/tools-section/PDF2PowerPoint.svg',
      },
      {
        name: 'pdfToExcel',
        title: 'PDF to Excel',
        icon: '/images/landing-page/tools-section/PDF2Excel.svg',
      },
      {
        name: 'pdfToJpg',
        title: 'PDF to JPG',
        icon: '/images/landing-page/tools-section/PDF2JPG.svg',
      },
    ],
  },
  // {
  //   category: 'languages',
  //   items: [
  //     {
  //       name: 'english',
  //       title: 'English',
  //       icon: '/images/landing-page/tools-2-section/English.png',
  //     },
  //     {
  //       name: 'spanish',
  //       title: 'Español',
  //       icon: '/images/landing-page/tools-2-section/Spanish.png',
  //     },
  //     {
  //       name: 'italian',
  //       title: 'Italiano',
  //       icon: '/images/landing-page/tools-2-section/Italian.png',
  //     },
  //     {
  //       name: 'french',
  //       title: 'Français',
  //       icon: '/images/landing-page/tools-2-section/French.png',
  //     },
  //     {
  //       name: 'portuguese',
  //       title: 'Português',
  //       icon: '/images/landing-page/tools-2-section/Portuguese.png',
  //     },
  //   ],
  // },
];

export default function Tools2Section() {
  const [expandedCategories, setExpandedCategories] = useState<number[]>([0]);
  const t = useTranslations('HomePage.tools2Section');

  const toggleCategory = (index: number) => {
    setExpandedCategories((prev) => (prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]));
  };

  return (
    <section className={cn()}>
      <div
        className={cn(
          'ml-[-100%] mr-[-100%] flex flex-col gap-8 bg-[#F9F9F9] py-20 pl-[100%] pr-[100%]',
          'desktop:hidden laptop:hidden tablet:hidden final:hidden',
        )}
      >
        {items.map((item, index) => (
          <div key={index} className="flex w-full flex-col items-stretch gap-8">
            <div className="flex items-center">
              <h3 className="flex w-[250px] items-center justify-start font-onest text-[22px] font-medium leading-[26px] tracking-normal text-[#1C1C1C]">
                {t(`${item.category}.title`)}
              </h3>
              <div className="grid flex-1 grid-cols-5 gap-y-6">
                {item.items.map((tool, index) => (
                  <Link
                    href="#"
                    key={index}
                    className="flex w-[200px] items-center justify-start gap-3 font-onest text-lg font-normal leading-6 tracking-normal text-[#585858] hover:text-gray-900"
                  >
                    <Image src={tool.icon} alt="Tool Icon" width={24} height={24} className="object-contain" />
                    {t(`${item.category}.tools.${tool.name}`)}
                  </Link>
                ))}
              </div>
            </div>
            {index < items.length - 1 ? <div className="h-px bg-[#E7E7E7]"></div> : null}
          </div>
        ))}
      </div>

      <div
        className={cn('flex flex-col gap-3', 'tablet:-mt-10', 'hidden desktop:flex laptop:flex tablet:flex final:flex')}
      >
        {items.map((item, index) => (
          <div key={index} className="flex flex-col gap-6 rounded-[10px] bg-[#F9F9F9] p-5">
            <Button
              onClick={() => toggleCategory(index)}
              className="flex h-6 items-center justify-between bg-transparent p-0 font-onest text-xl font-medium leading-6 text-[#1C1C1C] hover:bg-transparent"
            >
              <span>{t(`${item.category}.title`)}</span>

              <div className="transition-transform duration-300 ease-in-out">
                {expandedCategories.includes(index) ? (
                  <ChevronUp className="h-5 w-5 text-[#1C1C1C]" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-[#1C1C1C]" />
                )}
              </div>
            </Button>

            {expandedCategories.includes(index) ? (
              <div className="flex flex-col gap-4">
                {item.items.map((tool, index) => (
                  <Link
                    href="#"
                    key={index}
                    className="flex items-center justify-start gap-3 font-onest text-[16px] font-normal leading-[22px] tracking-normal text-[#585858] hover:text-gray-900"
                  >
                    <Image src={tool.icon} alt="Tool Icon" width={22} height={22} className="object-contain" />
                    {t(`${item.category}.tools.${tool.name}`)}
                  </Link>
                ))}
              </div>
            ) : null}
          </div>
        ))}
      </div>
    </section>
  );
}
