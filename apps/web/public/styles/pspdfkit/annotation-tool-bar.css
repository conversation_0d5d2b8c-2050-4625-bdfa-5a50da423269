/* common */
.PSPDFKit-Annotation-Toolbar,
.PSPDFKit-Annotation-Toolbar > .BaselineUI-Toolbar {
  min-height: 36px;
  height: 36px;
  margin-bottom: 18px;
  overflow: hidden;
  background: #f4f4f4;
  box-shadow: 0px 1px 0px 0px #e7e7e7;
}

.PSPDFKit-Annotation-Toolbar .BaselineUI-Separator {
  min-height: 36px !important;
  background: #dadada;
}

/* color */
div[data-block-id='color'],
div[data-block-id='text-color'],
div[data-block-id='background-color'],
div[data-block-id='fill-color'],
div[data-block-id='stroke-color'] {
  margin: 0px;
  padding: 0px;
  gap: 16px;
}

div[data-block-id='color'] > svg,
div[data-block-id='text-color'] > svg,
div[data-block-id='background-color'] > svg,
div[data-block-id='fill-color'] > svg,
div[data-block-id='stroke-color'] > svg {
  display: none;
}

div[data-block-id='color']::before,
div[data-block-id='text-color']::before,
div[data-block-id='background-color']::before,
div[data-block-id='fill-color']::before,
div[data-block-id='stroke-color']::before {
  content: '';
  width: 26px;
  height: 26px;

  /* background-image: url('../../images/pspdfkit/*.svg'); */
  background-size: contain;
  background-repeat: no-repeat;
}

div[data-block-id='color']::before,
div[data-block-id='text-color']::before {
  background-image: url('../../images/pspdfkit/annotation-color.svg');
}

div[data-block-id='background-color']::before,
div[data-block-id='fill-color']::before {
  background-image: url('../../images/pspdfkit/annotation-bg-color.svg');
}

div[data-block-id='stroke-color']::before {
  background-image: url('../../images/pspdfkit/annotation-stroke-color.svg');
}

div[data-block-id='color'] button,
div[data-block-id='text-color'] button,
div[data-block-id='background-color'] button,
div[data-block-id='fill-color'] button,
div[data-block-id='stroke-color'] button {
  width: 44px;
  height: 28px;
  padding: 6px;

  display: flex;
  align-items: center;
  gap: 4px;

  border-radius: 4px;
  border: 0.5px solid #e7e7e7;
  background: #ffffff;
}

/* div[data-block-id="color"] button[aria-expanded="true"],
div[data-block-id="text-color"] button[aria-expanded="true"],
div[data-block-id="background-color"] button[aria-expanded="true"],
div[data-block-id="fill-color"] button[aria-expanded="true"],
div[data-block-id="stroke-color"] button[aria-expanded="true"] {
    box-shadow: none;
} */

div[data-block-id='color'] button::after,
div[data-block-id='text-color'] button::after,
div[data-block-id='background-color'] button::after,
div[data-block-id='fill-color'] button::after,
div[data-block-id='stroke-color'] button::after {
  content: '';
  width: 12px;
  height: 12px;
  background-image: url('../../images/pspdfkit/dropdown-arrow.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* opacity */
div[data-block-id='opacity'] {
  margin: 0px;
  padding: 0px;
  gap: 16px;
}

div[data-block-id='opacity'] > svg {
  display: none;
}

div[data-block-id='opacity']::before {
  content: '';
  width: 26px;
  height: 26px;
  background-image: url('../../images/pspdfkit/annotation-opacity.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

div[data-block-id='opacity'] button {
  width: 59px;
  min-width: 59px !important;
  height: 28px;
  min-height: 28px;
  padding: 6px;

  display: flex;
  align-items: center;
  gap: 4px;

  border-radius: 4px;
  border: 0.5px solid #e7e7e7;
  background: #ffffff;

  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  color: #020202;
}

/* div[data-block-id="opacity"] button[aria-expanded="true"] {
    box-shadow: none;
} */

div[data-block-id='opacity'] button > svg {
  display: none;
}

div[data-block-id='opacity'] button::after {
  content: '';
  width: 12px;
  height: 12px;
  background-image: url('../../images/pspdfkit/dropdown-arrow.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* font-family */
div[data-block-id='font-family'] {
  margin: 0px;
  padding: 0px;
  gap: 16px;
}

div[data-block-id='font-family'] > svg {
  display: none;
}

div[data-block-id='font-family']::before {
  content: '';
  width: 26px;
  height: 26px;
  background-image: url('../../images/pspdfkit/annotation-font-family.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

div[data-block-id='font-family'] > .BaselineUI-Select {
  width: 124px;
}

div[data-block-id='font-family'] button {
  width: 124px;
  min-width: 59px !important;
  height: 28px;
  padding: 6px;

  display: flex;
  align-items: center;
  gap: 4px;

  border-radius: 4px;
  border: 0.5px solid #e7e7e7;
  background: #ffffff;
}

div[data-block-id='font-family'] button > span:first-child > span {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  color: #020202;
}

div[data-block-id='font-family'] button > span:last-child {
  display: none;
}

div[data-block-id='font-family'] button::after {
  content: '';
  width: 12px;
  height: 12px;
  background-image: url('../../images/pspdfkit/dropdown-arrow.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* font-size */
div[data-block-id='font-size'] {
  margin: 0px;
  padding: 0px;
}

div[data-block-id='font-size'] > div {
  width: 50px;
  height: 28px;
  padding: 6px;

  display: flex;
  align-items: center;
  gap: 4px;

  border-radius: 4px;
  border: 0.5px solid #e7e7e7;
  background: #ffffff;
}

div[data-block-id='font-size'] > div > input {
  border: none;
  padding: 0px;

  min-width: 22px !important;
  width: 22px;
  height: 16px;

  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  color: #020202;
}

div[data-block-id='font-size'] > div > button {
  padding: 0px;
  min-height: 28px;
}

/* div[data-block-id="font-size"]:hover>div>button {
    background: none;
    border: none;
}

div[data-block-id="font-size"]>div>button:hover {
    background: none;
    border: none;
    outline: none;
} */

div[data-block-id='font-size'] > div > button > svg {
  display: none;
}

div[data-block-id='font-size'] > div > button::after {
  content: '';
  width: 12px;
  height: 12px;
  background-image: url('../../images/pspdfkit/dropdown-arrow.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* font-alignment */
div[data-block-id='font-horizontal-alignment'],
div[data-block-id='font-vertical-alignment'] {
  margin: 0px;
  padding: 0px;
  gap: 1px;
}

div[data-block-id='font-horizontal-alignment'] button,
div[data-block-id='font-vertical-alignment'] button {
  width: 28px;
  height: 28px;
  background: #ffffff;
}

div[data-block-id='font-horizontal-alignment'] button > svg,
div[data-block-id='font-vertical-alignment'] button > svg {
  display: none;
}

div[data-block-id='font-horizontal-alignment'] button::before,
div[data-block-id='font-vertical-alignment'] button::before {
  content: '';
  width: 18px;
  height: 18px;
  /* background-image: url('../../images/pspdfkit/*.svg'); */
  background-size: contain;
  background-repeat: no-repeat;
}

div[data-block-id='font-horizontal-alignment'] button[data-hovered='true'],
div[data-block-id='font-vertical-alignment'] button[data-hovered='true'] {
  background: #5858580e;
}

div[data-block-id='font-horizontal-alignment'] button[aria-checked='true'],
div[data-block-id='font-vertical-alignment'] button[aria-checked='true'] {
  background: #58585834;
}

div[data-block-id='font-horizontal-alignment'] button:nth-child(1),
div[data-block-id='font-vertical-alignment'] button:nth-child(1) {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

div[data-block-id='font-horizontal-alignment'] button:nth-child(1)::before {
  background-image: url('../../images/pspdfkit/annotation-font-left.svg');
}

div[data-block-id='font-vertical-alignment'] button:nth-child(1)::before {
  background-image: url('../../images/pspdfkit/annotation-font-top.svg');
}

div[data-block-id='font-horizontal-alignment'] button:nth-child(2)::before {
  background-image: url('../../images/pspdfkit/annotation-font-center.svg');
}

div[data-block-id='font-vertical-alignment'] button:nth-child(2)::before {
  background-image: url('../../images/pspdfkit/annotation-font-middle.svg');
}

div[data-block-id='font-horizontal-alignment'] button:nth-child(3),
div[data-block-id='font-vertical-alignment'] button:nth-child(3) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

div[data-block-id='font-horizontal-alignment'] button:nth-child(3)::before {
  background-image: url('../../images/pspdfkit/annotation-font-right.svg');
}

div[data-block-id='font-vertical-alignment'] button:nth-child(3)::before {
  background-image: url('../../images/pspdfkit/annotation-font-bottom.svg');
}

/* add-text-box */
button[data-block-id='add-text-box'] {
  margin: 0px;
  padding: 0px;
  min-width: 26px;
  min-height: 26px;
  width: 26px;
  height: 26px;

  background-color: #585858;

  mask-image: url('../../images/pspdfkit/annotation-add-text.svg');
  mask-size: contain;
  mask-repeat: no-repeat;
}

button[data-block-id='add-text-box'][aria-pressed='true'] {
  background-color: #f0401d;
}

button[data-block-id='add-text-box'] > svg {
  display: none;
}

/* image-rotation */
button[data-block-id='counterclockwise-rotation'],
button[data-block-id='clockwise-rotation'] {
  margin: 0px;
  padding: 5px;
  width: 28px;
  height: 28px;
  min-width: 28px;
  min-height: 28px;
  border-radius: 4px;
  background: #ffffff;
}

button[data-block-id='counterclockwise-rotation'][data-hovered='true'],
button[data-block-id='clockwise-rotation'][data-hovered='true'] {
  background: #5858580e;
}

button[data-block-id='counterclockwise-rotation'] > svg,
button[data-block-id='clockwise-rotation'] > svg {
  display: none;
}

button[data-block-id='counterclockwise-rotation']::before,
button[data-block-id='clockwise-rotation']::before {
  content: '';
  width: 18px;
  height: 18px;
  /* background-image: url('../../images/pspdfkit/*.svg'); */
  background-size: contain;
  background-repeat: no-repeat;
}

button[data-block-id='counterclockwise-rotation']::before {
  background-image: url('../../images/pspdfkit/annotation-rotate.svg');
}

button[data-block-id='clockwise-rotation']::before {
  background-image: url('../../images/pspdfkit/annotation-wise-rotate.svg');
}

/* note-icon */
div[data-block-id='note-icon'] {
  margin: 0px;
  padding: 0px;
  gap: 16px;
}

div[data-block-id='note-icon'] > svg {
  display: none;
}

div[data-block-id='note-icon']::before {
  content: '';
  width: 26px;
  height: 26px;
  background-image: url('../../images/pspdfkit/annotation-note-icon.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

div[data-block-id='note-icon'] > div {
  gap: 0px;
}

div[data-block-id='note-icon'] > div > button {
  width: 44px;
  height: 36px;
}

div[data-block-id='note-icon'] > div > button[aria-checked='true'],
div[data-block-id='note-icon'] > div > button[data-hovered='true'] {
  background: #e4e4e4;
}

div[data-block-id='note-icon'] > div > button > svg {
  display: none;
}

div[data-block-id='note-icon'] > div > button::before {
  content: '';
  width: 24px;
  height: 24px;
  /* background-image: url('../../images/pspdfkit/*.svg'); */
  background-size: contain;
  background-repeat: no-repeat;
}

.PSPDFKit-Note-Annotation-Icon-Comment::before {
  background-image: url('../../images/pspdfkit/annotation-icon-comment.svg');
}

.PSPDFKit-Note-Annotation-Icon-RightPointer::before {
  background-image: url('../../images/pspdfkit/annotation-icon-right-pointer.svg');
}

.PSPDFKit-Note-Annotation-Icon-RightArrow::before {
  background-image: url('../../images/pspdfkit/annotation-icon-right-arrow.svg');
}

.PSPDFKit-Note-Annotation-Icon-Check::before {
  background-image: url('../../images/pspdfkit/annotation-icon-check.svg');
}

.PSPDFKit-Note-Annotation-Icon-Circle::before {
  background-image: url('../../images/pspdfkit/annotation-icon-circle.svg');
}

.PSPDFKit-Note-Annotation-Icon-Cross::before {
  background-image: url('../../images/pspdfkit/annotation-icon-cross.svg');
}

.PSPDFKit-Note-Annotation-Icon-Insert::before {
  background-image: url('../../images/pspdfkit/annotation-icon-insert.svg');
}

.PSPDFKit-Note-Annotation-Icon-NewParagraph::before {
  background-image: url('../../images/pspdfkit/annotation-icon-new-paragraph.svg');
}

.PSPDFKit-Note-Annotation-Icon-Note::before {
  background-image: url('../../images/pspdfkit/annotation-icon-note.svg');
}

.PSPDFKit-Note-Annotation-Icon-Paragraph::before {
  background-image: url('../../images/pspdfkit/annotation-icon-paragraph.svg');
}

.PSPDFKit-Note-Annotation-Icon-Help::before {
  background-image: url('../../images/pspdfkit/annotation-icon-help.svg');
}

.PSPDFKit-Note-Annotation-Icon-Star::before {
  background-image: url('../../images/pspdfkit/annotation-icon-star.svg');
}

.PSPDFKit-Note-Annotation-Icon-Key::before {
  background-image: url('../../images/pspdfkit/annotation-icon-key.svg');
}

/* font-formatting */
div[data-block-id='font-formatting'] {
  margin: 0px;
  padding: 0px;
  gap: 6px;
}

div[data-block-id='font-formatting'] button {
  width: 28px;
  height: 28px;
  border-radius: 4px;
}

div[data-block-id='font-formatting'] button:not([data-disabled]) {
  background: #ffffff;
}

div[data-block-id='font-formatting'] button[data-hovered='true'] {
  background: #5858580e;
}

div[data-block-id='font-formatting'] button[aria-checked='true'] {
  background: #58585834;
}

div[data-block-id='font-formatting'] button > svg {
  display: none;
}

div[data-block-id='font-formatting'] button::before {
  content: '';
  width: 18px;
  height: 18px;
  /* background-image: url('../../images/pspdfkit/*.svg'); */
  background-size: contain;
  background-repeat: no-repeat;
}

div[data-block-id='font-formatting'] button:nth-child(1)::before {
  background-image: url('../../images/pspdfkit/annotation-font-bold.svg');
}

div[data-block-id='font-formatting'] button:nth-child(2)::before {
  background-image: url('../../images/pspdfkit/annotation-font-italic.svg');
}

/* line-width */
div[data-block-id='line-width'] {
  margin: 0px;
  padding: 0px;
  gap: 16px;
}

div[data-block-id='line-width'] > svg {
  display: none;
}

div[data-block-id='line-width']::before {
  content: '';
  width: 26px;
  height: 26px;
  background-image: url('../../images/pspdfkit/annotation-line-width.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

div[data-block-id='line-width'] button {
  width: 59px;
  min-width: 59px !important;
  height: 28px;
  min-height: 28px;
  padding: 6px;

  display: flex;
  align-items: center;
  gap: 4px;

  border-radius: 4px;
  border: 0.5px solid #e7e7e7;
  background: #ffffff;

  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  color: #020202;
}

/* div[data-block-id="line-width"] button[aria-expanded="true"] {
    box-shadow: none;
} */

div[data-block-id='line-width'] button > svg {
  display: none;
}

div[data-block-id='line-width'] button::after {
  content: '';
  width: 12px;
  height: 12px;
  background-image: url('../../images/pspdfkit/dropdown-arrow.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* line-caps */
.PSPDFKit-Toolbox-LineCaps-DashArray {
  margin: 0px;
  padding: 0px;
  gap: 6px;
}

.PSPDFKit-Toolbox-LineCaps-DashArray > div:first-child {
  margin: 0px;
  padding: 0px;
  margin-right: 10px;
  width: 26px;
}

.PSPDFKit-Toolbox-LineCaps-DashArray > div:first-child > svg {
  display: none;
}

.PSPDFKit-Toolbox-LineCaps-DashArray > div:first-child::before {
  content: '';
  width: 26px;
  height: 26px;
  background-image: url('../../images/pspdfkit/annotation-line-caps.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

.PSPDFKit-Toolbox-LineCaps-DashArray .PSPDFKit-Toolbar-DropdownGroup {
  height: 28px;
}

.PSPDFKit-Toolbox-LineCaps-DashArray .PSPDFKit-Toolbar-DropdownGroup button {
  padding: 6px;
  gap: 4px;
  border-radius: 4px;
  border-width: 0.5px;
  background: #ffffff;
  border: 0.5px solid #e7e7e7;
}

/* .PSPDFKit-Toolbox-LineCaps-DashArray .PSPDFKit-Toolbar-DropdownGroup button[aria-expanded="true"] {} */

.PSPDFKit-Annotation-Toolbar-StartLineCap,
.PSPDFKit-Annotation-Toolbar-StartLineCap button,
.PSPDFKit-Annotation-Toolbar-EndLineCap,
.PSPDFKit-Annotation-Toolbar-EndLineCap button {
  width: 64px;
}

.PSPDFKit-Annotation-Toolbar-StartLineCap button > span:first-child,
.PSPDFKit-Annotation-Toolbar-EndLineCap button > span:first-child {
  width: 36px;
  height: 16px;
}

.PSPDFKit-Annotation-Toolbar-StartLineCap button > span:last-child > svg,
.PSPDFKit-Annotation-Toolbar-EndLineCap button > span:last-child > svg {
  display: none;
}

.PSPDFKit-Annotation-Toolbar-StartLineCap button > span:last-child::after,
.PSPDFKit-Annotation-Toolbar-EndLineCap button > span:last-child::after {
  display: block;
  content: '';
  width: 12px;
  height: 12px;
  background-image: url('../../images/pspdfkit/dropdown-arrow.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

.PSPDFKit-Annotation-Toolbar-DashArray,
.PSPDFKit-Annotation-Toolbar-DashArray button {
  width: 88px;
}

.PSPDFKit-Annotation-Toolbar-DashArray button > span:first-child {
  width: 60px;
  height: 16px;
}

.PSPDFKit-Annotation-Toolbar-DashArray button > span:last-child > svg {
  display: none;
}

.PSPDFKit-Annotation-Toolbar-DashArray button > span:last-child::after {
  display: block;
  content: '';
  width: 12px;
  height: 12px;
  background-image: url('../../images/pspdfkit/dropdown-arrow.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* line-style */
div[data-block-id='line-style'] {
  margin: 0px;
  padding: 0px;
  gap: 16px;
}

div[data-block-id='line-style'] > svg {
  display: none;
}

div[data-block-id='line-style']::before {
  content: '';
  width: 26px;
  height: 26px;
  background-image: url('../../images/pspdfkit/annotation-line-style.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

div[data-block-id='line-style'] > .BaselineUI-Select {
  width: 88px;
}

div[data-block-id='line-style'] button {
  width: 88px;
  min-width: 88px !important;
  height: 28px;
  min-height: 28px;
  padding: 6px;

  display: flex;
  align-items: center;
  gap: 4px;

  border-radius: 4px;
  border: 0.5px solid #e7e7e7;
  background: #ffffff;

  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  color: #020202;
}

/* div[data-block-id="line-style"] button[aria-expanded="true"] {
    box-shadow: none;
} */

div[data-block-id='line-style'] button > span:first-child {
  width: 60px;
  height: 16px;
}

div[data-block-id='line-style'] button > span:last-child > svg {
  display: none;
}

div[data-block-id='line-style'] button > span:last-child::after {
  display: block;
  content: '';
  width: 12px;
  height: 12px;
  background-image: url('../../images/pspdfkit/dropdown-arrow.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* blend-mode */
div[data-block-id='blend-mode'] {
  margin: 0px;
  padding: 0px;
  gap: 16px;
}

div[data-block-id='blend-mode'] > svg {
  display: none;
}

div[data-block-id='blend-mode']::before {
  content: '';
  width: 26px;
  height: 26px;
  background-image: url('../../images/pspdfkit/annotation-blend-mode.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

div[data-block-id='blend-mode'] > .BaselineUI-Select {
  width: 88px;
}

div[data-block-id='blend-mode'] button {
  width: 88px;
  min-width: 88px !important;
  height: 28px;
  min-height: 28px;
  padding: 6px;

  display: flex;
  align-items: center;
  gap: 4px;

  border-radius: 4px;
  border: 0.5px solid #e7e7e7;
  background: #ffffff;

  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  color: #020202;
}

/* div[data-block-id="blend-mode"] button[aria-expanded="true"] {
    box-shadow: none;
} */

div[data-block-id='blend-mode'] button > span:first-child {
  width: 60px;
  height: 16px;
}

div[data-block-id='blend-mode'] button > span:last-child > svg {
  display: none;
}

div[data-block-id='blend-mode'] button > span:last-child::after {
  display: block;
  content: '';
  width: 12px;
  height: 12px;
  background-image: url('../../images/pspdfkit/dropdown-arrow.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* annotation-note */
button[data-block-id='annotation-note'] {
  margin: 0px;
  padding: 0px;
  min-width: 26px;
  min-height: 26px;
  width: 26px;
  height: 26px;
}

button[data-block-id='annotation-note'] > svg {
  display: none;
}

button[data-block-id='annotation-note']::before {
  content: '';
  width: 26px;
  height: 26px;
  background-image: url('../../images/pspdfkit/annotation-note.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* delete */
button[data-block-id='delete'] {
  margin: 0px;
  padding: 0px;
  min-width: 26px;
  min-height: 26px;
  width: 26px;
  height: 26px;
}

button[data-block-id='delete'] > svg {
  display: none;
}

button[data-block-id='delete']::before {
  content: '';
  width: 26px;
  height: 26px;
  background-image: url('../../images/pspdfkit/annotation-delete.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

/* text-annotation */
.PSPDFKit-Text-Annotation-Toolbar .PSPDFKit-Toolbox-TextColor {
  margin: 0px 20px 0px 6px;
}

.PSPDFKit-Text-Annotation-Toolbar .PSPDFKit-Toolbox-BackgroundColor {
  margin: 0px 20px;
}

.PSPDFKit-Text-Annotation-Toolbar .PSPDFKit-Toolbox-Opacity {
  margin: 0px 20px 0px 32px;
}

.PSPDFKit-Text-Annotation-Toolbar-Font-Family {
  margin: 0px 0px 0px 20px !important;
}

.PSPDFKit-Text-Annotation-Toolbar-Font-Size {
  margin: 0px 6px !important;
}

.PSPDFKit-Text-Annotation-Toolbar-Alignment {
  margin: 0px 6px 0px 4px !important;
}

.PSPDFKit-Text-Annotation-Toolbar-Font-Family + .BaselineUI-Separator,
.PSPDFKit-Text-Annotation-Toolbar-Font-Size + .BaselineUI-Separator,
.PSPDFKit-Text-Annotation-Toolbar-Alignment + .BaselineUI-Separator,
.PSPDFKit-Text-Annotation-Toolbar-Vertical-Alignment ~ .BaselineUI-Separator {
  display: none;
}

.PSPDFKit-Text-Annotation-Toolbar-Button-Delete {
  margin-right: 16px !important;
}

/* content-editing */
.PSPDFKit-Content-Editing-Toolbar .PSPDFKit-Toolbox-Add-Text {
  margin: 0px 20px 0px 6px;
}

.PSPDFKit-Content-Editing-Toolbar .PSPDFKit-Toolbox-TextColor {
  margin: 0px 20px;
}

.PSPDFKit-Content-Editing-Toolbar-Font-Family {
  margin: 0px 0px 0px 20px !important;
}

.PSPDFKit-Content-Editing-Toolbar-Font-Size {
  margin: 0px 10px 0px 6px !important;
}

.PSPDFKit-Content-Editing-Toolbar-Font-Family + .BaselineUI-Separator,
.PSPDFKit-Content-Editing-Toolbar-Font-Size + .BaselineUI-Separator,
.PSPDFKit-Content-Editing-Toolbar-Inline-Style ~ .BaselineUI-Separator {
  display: none;
}

.PSPDFKit-Content-Editing-Button-Delete {
  margin-right: 18px !important;
}

.PSPDFKit-Content-Editing-Toolbar > div:last-child {
  margin: 0px 6px;
  padding: 0px;
  gap: 12px;
}

.PSPDFKit-Content-Editing-Toolbar > div:last-child > button {
  display: flex;
  justify-content: center;
  align-items: center;

  font-family: Onest;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;

  box-shadow: none;
}

.PSPDFKit-Content-Editing-Toolbar > div:last-child > button[data-disabled='true'] {
  opacity: 0.5;
}

.PSPDFKit-Content-Editing-Toolbar > div:last-child > button:first-child {
  width: 63px;
  height: 28px;

  border-radius: 4px;
  border-width: 0.5px;
  border: 0.5px solid #e7e7e7;

  background: #ffffff03;
  color: #020202;
}

.PSPDFKit-Content-Editing-Toolbar > div:last-child > button:last-child {
  width: 96px;
  height: 28px;

  border-radius: 4px;

  background: #f0401d;
  color: #ffffff;
}

/* shape-annotation */
.PSPDFKit-Shape-Annotation-Toolbar .PSPDFKit-Toolbox-StrokeColor {
  margin: 0px 20px 0px 6px;
}

.PSPDFKit-Shape-Annotation-Toolbar .PSPDFKit-Toolbox-FillColor {
  margin: 0px 20px;
}

.PSPDFKit-Shape-Annotation-Toolbar .PSPDFKit-Toolbox-Opacity {
  margin: 0px 20px 0px 32px;
}

.PSPDFKit-Shape-Annotation-Toolbar .PSPDFKit-Toolbox-LineWidth {
  margin: 0px 20px;
}

.PSPDFKit-Shape-Annotation-Toolbar .PSPDFKit-Toolbox-LineCaps-DashArray {
  margin-left: 20px;
}

.PSPDFKit-Shape-Annotation-Toolbar .PSPDFKit-Toolbox-LineStyle {
  margin-left: 20px;
}

.PSPDFKit-Shape-Annotation-Toolbar div[data-spacer='true'] + .BaselineUI-Separator,
.PSPDFKit-Shape-Annotation-Toolbar .PSPDFKit-Toolbox-LineStyle + .BaselineUI-Separator,
.PSPDFKit-Shape-Annotation-Toolbar .PSPDFKit-Toolbox-LineCaps-DashArray + .BaselineUI-Separator {
  display: none;
}

.PSPDFKit-Shape-Annotation-Toolbar-Annotation-Note {
  margin-right: 20px !important;
}

.PSPDFKit-Shape-Annotation-Toolbar-Button-Delete {
  margin: 0px 6px 0px 20px !important;
}

/* ink-annotation */
.PSPDFKit-Ink-Annotation-Toolbar .PSPDFKit-Toolbox-StrokeColor {
  margin: 0px 20px 0px 6px;
}

.PSPDFKit-Ink-Annotation-Toolbar .PSPDFKit-Toolbox-FillColor {
  margin: 0px 20px;
}

.PSPDFKit-Ink-Annotation-Toolbar .PSPDFKit-Toolbox-Opacity {
  margin: 0px 20px;
}

.PSPDFKit-Ink-Annotation-Toolbar .PSPDFKit-Toolbox-LineWidth {
  margin: 0px 20px;
}

.PSPDFKit-Ink-Annotation-Toolbar .PSPDFKit-Toolbox-BlendMode {
  margin-left: 20px;
}

.PSPDFKit-Ink-Annotation-Toolbar div[data-spacer='true'] + .BaselineUI-Separator,
.PSPDFKit-Ink-Annotation-Toolbar .PSPDFKit-Toolbox-BlendMode + .BaselineUI-Separator {
  display: none;
}

.PSPDFKit-Ink-Annotation-Toolbar-Annotation-Note {
  margin-right: 20px !important;
}

.PSPDFKit-Ink-Annotation-Toolbar-Button-Delete {
  margin: 0px 6px 0px 20px !important;
}

/* image-annotation */
.PSPDFKit-Image-Annotation-Toolbar .PSPDFKit-Toolbox-Opacity {
  margin: 0px 20px 0px 6px;
}

.PSPDFKit-Image-Annotation-Toolbar div[data-spacer='true'] + .BaselineUI-Separator {
  display: none;
}

.PSPDFKit-Image-Annotation-Toolbar-Button-Rotate-Counterclockwise {
  margin: 0px 6px 0px 20px !important;
}

.PSPDFKit-Image-Annotation-Toolbar-Button-Rotate + .BaselineUI-Separator {
  display: none;
}

.PSPDFKit-Image-Annotation-Toolbar-Annotation-Note {
  margin-right: 20px !important;
}

.PSPDFKit-Image-Annotation-Toolbar-Button-Delete {
  margin: 0px 6px 0px 20px !important;
}

/* stamp-annotation */
.PSPDFKit-Stamp-Annotation-Toolbar .PSPDFKit-Toolbox-Opacity {
  margin-left: 6px;
}

.PSPDFKit-Stamp-Annotation-Toolbar .PSPDFKit-Toolbox-Opacity + .BaselineUI-Separator,
.PSPDFKit-Stamp-Annotation-Toolbar div[data-spacer='true'] + .BaselineUI-Separator {
  display: none;
}

.PSPDFKit-Stamp-Annotation-Toolbar-Annotation-Note {
  margin-right: 20px !important;
}

.PSPDFKit-Stamp-Annotation-Toolbar-Button-Delete {
  margin: 0px 6px 0px 20px !important;
}

/* erase */
.PSPDFKit-Ink-Eraser-Toolbar .PSPDFKit-Toolbox-LineWidth {
  margin-left: 6px;
}

/* text-markup annotation */
.PSPDFKit-Text-Markup-Annotation-Toolbar-Color {
  margin: 0px 20px 0px 6px !important;
}

.PSPDFKit-Text-Markup-Annotation-Toolbar-Color::before {
  content: none !important;
}

.PSPDFKit-Text-Markup-Annotation-Toolbar-Color > div > svg {
  display: none;
}

.PSPDFKit-Text-Markup-Annotation-Toolbar-Color > div::before {
  content: '';
  width: 26px;
  height: 26px;

  background-image: url('../../images/pspdfkit/annotation-stroke-color.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

.PSPDFKit-Text-Markup-Annotation-Toolbar-Color > ul {
  gap: 8px;
}

.PSPDFKit-Text-Markup-Annotation-Toolbar-Color > ul > li {
  width: 26px;
  height: 26px;
  padding: 0px;

  display: flex;
  align-items: center;
  justify-content: center;
}

.PSPDFKit-Text-Markup-Annotation-Toolbar-Color > ul > li > div > div {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  outline-offset: 0px;
}

.PSPDFKit-Text-Markup-Annotation-Toolbar .PSPDFKit-Toolbox-Opacity {
  margin: 0px 20px;
}

.PSPDFKit-Text-Markup-Annotation-Toolbar .PSPDFKit-Toolbox-BlendMode {
  margin-left: 20px;
}

.PSPDFKit-Text-Markup-Annotation-Toolbar .PSPDFKit-Toolbox-BlendMode ~ .BaselineUI-Separator {
  display: none;
}

.PSPDFKit-Text-Markup-Annotation-Toolbar-Button-Delete {
  margin-right: 6px !important;
}

/* note-annotation */
.PSPDFKit-Note-Annotation-Toolbar-Color {
  margin: 0px 20px 0px 6px !important;
}

.PSPDFKit-Note-Annotation-Toolbar-Color::before {
  content: none !important;
}

.PSPDFKit-Note-Annotation-Toolbar-Color > div > svg {
  display: none;
}

.PSPDFKit-Note-Annotation-Toolbar-Color > div::before {
  content: '';
  width: 26px;
  height: 26px;

  background-image: url('../../images/pspdfkit/annotation-stroke-color.svg');
  background-size: contain;
  background-repeat: no-repeat;
}

.PSPDFKit-Note-Annotation-Toolbar-Color > ul {
  gap: 8px;
}

.PSPDFKit-Note-Annotation-Toolbar-Color > ul > li {
  width: 26px;
  height: 26px;
  padding: 0px;

  display: flex;
  align-items: center;
  justify-content: center;
}

.PSPDFKit-Note-Annotation-Toolbar-Color > ul > li > div > div {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  outline-offset: 0px;
}

.PSPDFKit-Note-Annotation-Toolbar .PSPDFKit-Toolbox-NoteIcon {
  margin-left: 20px;
}

.PSPDFKit-Note-Annotation-Toolbar .PSPDFKit-Toolbox-NoteIcon ~ .BaselineUI-Separator {
  display: none;
}

.PSPDFKit-Note-Annotation-Toolbar-Button-Delete {
  margin-right: 6px !important;
}

/* link-annotation */
.PSPDFKit-Link-Annotation-Toolbar .PSPDFKit-Toolbox-BorderColor {
  margin: 0px 20px 0px 6px;
}

.PSPDFKit-Link-Annotation-Toolbar .PSPDFKit-Toolbox-Opacity {
  margin: 0px 20px;
}

.PSPDFKit-Link-Annotation-Toolbar .PSPDFKit-Toolbox-LineWidth {
  margin-left: 20px;
}

.PSPDFKit-Link-Annotation-Toolbar .PSPDFKit-Toolbox-LineWidth ~ .BaselineUI-Separator {
  display: none;
}

.PSPDFKit-Link-Annotation-Toolbar-Button-Delete {
  margin-right: 6px !important;
}

/* color presets */
.BaselineUI-ColorInput-Presets > ul {
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.BaselineUI-ColorInput-Presets > ul > li {
  width: 20px;
  height: 20px;
  padding: 0px;
}

.BaselineUI-ColorInput-Presets .BaselineUI-ColorSwatch {
  width: 20px;
  height: 20px;
}

.BaselineUI-ColorInput-Presets .BaselineUI-ColorSwatch::before {
  border: 1px solid #585858;
}
