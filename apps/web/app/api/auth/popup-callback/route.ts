import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';
import { createAuthCallbackService } from '@pdfily/supabase/auth';
import pathsConfig from '@pdfily/config/paths.config';

/**
 * Handles the OAuth callback for popup authentication.
 *
 * This endpoint:
 * 1. Receives the authorization code from the popup callback
 * 2. Exchanges the code for a session using Supabase
 * 3. Handles document ownership transfer (anonymous to authenticated user)
 * 4. Returns success/error status
 *
 * @param {NextRequest} request - The incoming GET request with code parameter
 * @returns {Promise<NextResponse>} A JSON response indicating success or failure
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const client = getSupabaseServerClient();
    const service = createAuthCallbackService(client);

    // Use the existing auth callback service to handle the code exchange
    const { success, error } = await service.exchangeCodeForSession(request, {
      redirectPath: pathsConfig.app.home,
    });

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Authentication successful',
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: error || 'Authentication failed',
        },
        { status: 400 },
      );
    }
  } catch (error) {
    console.error('Popup callback error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 },
    );
  }
}
