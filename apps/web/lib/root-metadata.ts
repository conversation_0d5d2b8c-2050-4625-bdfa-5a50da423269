import { Metada<PERSON> } from "next"
import appConfig from "@pdfily/config/app.config"

/**
 * Generates the root metadata for the application.
 *
 * @returns {Metadata} The metadata object containing the title.
 */
export const generateRootMetadata = (): Metadata => {
  return {
    title: appConfig.title,
    description: appConfig.description,
    metadataBase: new URL(appConfig.url),
    icons: {
      icon: "/branding/favicon.svg",
    },
  }
}