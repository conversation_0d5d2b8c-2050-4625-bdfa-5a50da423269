'use client';

import type { User } from '@supabase/supabase-js';
import { useEffect, useState } from 'react';
import usePSPDFKit from '../hooks/use-pspdfkit';
import { UseViewerKitProps } from '../types/viewer-kit.types';
import PdfNutrientWebViewerHeader from './pdf-nutrient-web-viewer-header';
import PdfNutrientMobileViewerHeader from './pdf-nutrient-mobile-viewer-header';

interface NutrientWebViewerProps extends UseViewerKitProps {
  user: User;
}

/**
 * NutrientWebViewer is a wrapper component that loads the Nutrient Viewer
 * into a full-width, full-height container using the useNutrientKit hook.
 *
 * @param {UseNutrientKitProps} props - The props for loading the Nutrient document.
 * @param {string} props.source - The URL or ArrayBuffer of the document to load.
 * @param {string} [props.documentId] - Optional ID to track/view the document uniquely.
 * @param {string} [props.locale] - Optional language/locale setting.
 */
export default function NutrientWebViewer({ source, documentId, locale, user }: NutrientWebViewerProps) {
  const [isUndoRedo, setIsUndoRedo] = useState<boolean>(false);

  const { viewer, instance } = usePSPDFKit({ source, locale, isUndoRedo, setIsUndoRedo });

  useEffect(() => {
    (async () => {
      if (!instance) return;
    })();
  }, [instance]);

  return (
    <div className="h-full">
      <div className="small:hidden block">
        <PdfNutrientWebViewerHeader instance={instance} documentId={documentId} user={user} />
      </div>
      <div className="small:block hidden">
        <PdfNutrientMobileViewerHeader
          instance={instance}
          documentId={documentId}
          user={user}
          isUndoRedo={isUndoRedo}
        />
      </div>
      <div ref={viewer} style={{ height: 'calc(100dvh - 62px)' }} />
    </div>
  );
}
