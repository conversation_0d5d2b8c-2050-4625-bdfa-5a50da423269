'use client';

import { ReactNode } from 'react';

import { useAuthChangeListener } from '@pdfily/supabase/hooks/use-auth-change-listener';
import pathsConfig from '@pdfily/config/paths.config';

/**
 * @name AuthProvider
 *
 * @description Provides authentication change listener and renders children.
 * @returns {ReactNode} - Renders children after setting up auth change listener.
 */
export function AuthProvider(props: React.PropsWithChildren): ReactNode {
  useAuthChangeListener({
    appHomePath: pathsConfig.app.home,
    onEvent: (event, session) => {},
  });

  return props.children;
}
