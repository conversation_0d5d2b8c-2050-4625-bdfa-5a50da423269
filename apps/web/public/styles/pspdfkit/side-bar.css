/* common */
.PSPDFKit-Sidebar {
  background: #fbfbfb;
}

.PSPDFKit-Sidebar .BaselineUI-Separator {
  display: block;
}

/* Dragger */
.PSPDFKit-Sidebar-Dragger {
  width: 6px !important;
  border-left: 1px solid #dadada;
  background: #e0e0e0;
}

.PSPDFKit-Sidebar-DraggerHandle {
  width: 12px;
  height: 36px;
  margin-left: -1px;
  border-radius: 10px;
  border: 0.5px solid #acacac;
}

.PSPDFKit-Sidebar-DraggerHandle::before,
.PSPDFKit-Sidebar-DraggerHandle::after {
  height: 22px;
  background: #acacac;
}

/* Thumbnails */
.PSPDFKit-Sidebar-Thumbnails {
  padding-top: 56px;
  padding-bottom: 56px;
}

.BaselineUI-ListBox[aria-label='Thumbnails'] {
  gap: 80px;
  justify-content: center;
}

.BaselineUI-ListBox[aria-label='Thumbnails'] > li {
  gap: 20px;
}

.BaselineUI-ListBox[aria-label='Thumbnails'] > li > div > div {
  box-shadow: 1px 2px 4px 1px #9b9ea31a;
}

.BaselineUI-ListBox[aria-label='Thumbnails'] > li[aria-selected='true'] > div > div {
  outline: none;
  box-shadow: none;
  border: 2px solid #f0401d;
  border-radius: 6px;
}

.BaselineUI-ListBox[aria-label='Thumbnails'] > li > span {
  width: 40px;
  height: 20px;
  padding: 2px 5px;
  border-radius: 6px;

  font-family: Onest;
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;

  background: #dadada;
  color: #585858;
}

.BaselineUI-ListBox[aria-label='Thumbnails'] > li[aria-selected='true'] > span {
  background: #f0401d;
  color: white;
}

/* Annotations */
.PSPDFKit-Sidebar-Annotations {
  padding: 4px 0px;
}

.PSPDFKit-Sidebar-Annotations .PSPDFKit-Sidebar-Header {
  padding: 16px;
}

.PSPDFKit-Sidebar-Annotations-Annotation-Counter {
  font-family: Onest;
  font-weight: 500;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0px;
  color: #020202;

  margin: 0px;
}

.PSPDFKit-Sidebar-Annotations-Page-Number {
  height: 36px;
  padding: 8px 16px;
  background: #f2f2f2;
  text-align: right;
}

.PSPDFKit-Sidebar-Annotations-Page-Number > span {
  font-family: Onest;
  font-weight: 500;
  font-size: 18px;
  line-height: 20px;
  letter-spacing: 0px;
  color: #020202;
}

.PSPDFKit-Sidebar-Annotations-Container > ul {
  padding: 10px 8px;
  display: flex;
  flex-direction: column;
}

.PSPDFKit-Sidebar-Annotations-Annotation {
  height: 58px;
  padding: 10px 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.PSPDFKit-Sidebar-Annotations-Annotation > svg {
  display: none;
}

.PSPDFKit-Sidebar-Annotations-Annotation > div {
  height: 38px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.PSPDFKit-Sidebar-Annotations-Annotation > div > span:first-child {
  font-family: Onest;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  letter-spacing: 0%;
  color: #020202;

  text-transform: lowercase;
}

.PSPDFKit-Sidebar-Annotations-Annotation > div > span:first-child::first-letter {
  text-transform: uppercase;
}

.PSPDFKit-Sidebar-Annotations-Annotation > div > span:last-child {
  font-family: Onest;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0%;
  color: #585858;
}

.PSPDFKit-Sidebar-Annotations-Annotation::before {
  content: '';
  display: inline-block;
  width: 32px;
  height: 32px;
  background-color: #f0401d;

  /* mask-image: url('.../*.svg'); */
  mask-size: contain;
  mask-repeat: no-repeat;
}

.PSPDFKit-Sidebar-Annotations-Annotation-Text::before {
  mask-image: url('../../images/pspdfkit/text.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Line::before {
  mask-image: url('../../images/pspdfkit/line.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Arrow::before {
  mask-image: url('../../images/pspdfkit/arrow.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Rectangle::before {
  mask-image: url('../../images/pspdfkit/rectangle.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Ellipse::before {
  mask-image: url('../../images/pspdfkit/ellipse.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Polygon::before {
  mask-image: url('../../images/pspdfkit/polygon.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Ink::before {
  mask-image: url('../../images/pspdfkit/ink.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Highlighter::before {
  mask-image: url('../../images/pspdfkit/highlighter.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Highlight::before {
  mask-image: url('../../images/pspdfkit/text-highlighter.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Image::before {
  mask-image: url('../../images/pspdfkit/image.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Stamp::before {
  mask-image: url('../../images/pspdfkit/stamp.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation-Note::before {
  mask-image: url('../../images/pspdfkit/note.svg');
}

.PSPDFKit-Sidebar-Annotations-Annotation > button {
  cursor: pointer;

  width: 20px;
  height: 20px;
  background-image: url('../../images/pspdfkit/annotation-delete.svg');
  background-size: contain;
  background-repeat: no-repeat;

  border: none;
  background-color: transparent;
}

.PSPDFKit-Sidebar-Annotations-Delete {
  display: none;
}
