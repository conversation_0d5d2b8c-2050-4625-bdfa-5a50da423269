'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { sendGTMEvent } from '@next/third-parties/google';
import { cn } from '@pdfily/ui/utils';
import { SubscriptionType } from '@pdfily/payment';
import { isGTMInitialized, LocalStorageManager } from '@pdfily/shared/utils';
import { usePayment } from '@pdfily/payment/hooks/use-payment';
import { useAuth } from '../session-provider';

interface PaymentDetailsProps {
  onPaymentComplete: () => void;
  selectedPlan: { id: number; name: string; price: string; total?: string; period: string } | null;
}

const cards2 = [
  { name: 'Visa', image: '/images/checkout/Visa.png' },
  { name: 'Master', image: '/images/checkout/Master.png' },
  { name: 'AmEx', image: '/images/checkout/AmEx.png' },
  { name: 'Discover', image: '/images/checkout/Discover.png' },
  { name: 'Paypal', image: '/images/checkout/Paypal2.png' },
];
const cards3 = [
  { name: 'Visa', image: '/images/checkout/Visa3.png' },
  { name: 'Master', image: '/images/checkout/Master3.png' },
  { name: 'AmEx', image: '/images/checkout/AmEx3.png' },
  { name: 'Discover', image: '/images/checkout/Discover3.png' },
  { name: 'Paypal', image: '/images/checkout/Paypal3.png' },
];
const plans = [
  { name: 'unlimitedEdits', image: '/images/checkout/UnlimitedEdits.svg' },
  { name: 'unlimitedDownloads', image: '/images/checkout/UnlimitedDownloads.svg' },
  { name: 'signDocsOnline', image: '/images/checkout/SignYourDocsOnline.svg' },
  { name: 'createOwnForms', image: '/images/checkout/CreateYourOwnForms.svg' },
  { name: 'convertFormats', image: '/images/checkout/ConvertToAnyFormat.svg' },
  { name: 'multiDeviceAccess', image: '/images/checkout/AccessFromAnyDevice.svg' },
];
const badgeKeys = ['premiumSupport', 'sevenDayRefund', 'satisfactionGuarantee'];

export default function PaymentDetails({ onPaymentComplete, selectedPlan }: PaymentDetailsProps) {
  const { service } = usePayment();
  const { user } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [clientSecret, setClientSecret] = useState<string | null>(null);

  // Retrive the payment UI compoments of the current provider
  const uiComponents = service?.getUIComponents();
  const PaymentForm = uiComponents?.PaymentForm;

  useEffect(() => {
    const initializePayment = async () => {
      if (!service) return;

      try {
        if (selectedPlan?.id === 3) {
          await service.getCustomerId(user);
          const setupIntent = await service.createSetupIntent(user);
          setClientSecret(setupIntent.client_secret!);

          // Store the plan key used
          LocalStorageManager.setItem('payment-type', SubscriptionType.ANNUAL);
        } else {
          const paymentIntent = await service.createPaymentIntent({
            amount: 1000,
            currency: 'usd',
            metadata: {
              name: user?.user_metadata?.name,
              email: user?.email,
              userId: user?.id,
              planName: selectedPlan?.name,
              period: selectedPlan?.period,
              planId: selectedPlan?.id.toString(),
              isTrial: true,
            },
          });

          // Store the plan key used
          LocalStorageManager.setItem('payment-type', SubscriptionType.WEEKLY);
          // Store the client secret
          LocalStorageManager.setItem('payment-id', paymentIntent.id!);

          setClientSecret(paymentIntent.clientSecret!);
        }
      } catch (error) {
        console.error('Erreur lors de la création du PaymentIntent:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializePayment();

    if (!isGTMInitialized()) {
      console.warn('GTM not initialized on the payment details page: Event not sent');
      return;
    }

    sendGTMEvent({ event: 'checkout_form_reached', plan: selectedPlan });

    // eslint-disable-next-line
  }, [service, selectedPlan]);

  const handleSuccess = async (paymentId: string) => {
    if (selectedPlan?.id === 3) {
      const customerId = (await service?.getCustomerId(user)) ?? '';
      const subscription = await service?.createSubscription({
        customerId: customerId!,
        priceId: process.env.NEXT_PUBLIC_STRIPE_SUBSCRIPTION_PRICE_ID!,
        paymentMethodId: paymentId,
        trialPeriodDays: 0,
        metadata: {
          uid: user?.id,
          email: user?.email,
          planId: selectedPlan?.id.toString(),
        },
      });

      LocalStorageManager.setItem('subscription-status', subscription?.status!);
    }

    onPaymentComplete();
  };

  const handleError = (error?: Error) => {
    console.error('Payment Error', error);
    // Redirect to the error page
  };

  const t = useTranslations('Checkout.planDetails');
  const locale = useLocale();

  if (isLoading || !clientSecret) {
    return <div>Payment form loading...</div>;
  }

  if (!service || !PaymentForm) {
    return <div>Payment Service unavailable</div>;
  }

  return (
    <div
      className={cn(
        'my-[48.5px] flex w-full items-start justify-between',
        'desktop:mb-0 desktop:mt-[26px] desktop:flex-col desktop:items-center desktop:justify-start desktop:gap-8',
        'final:mb-0 final:mt-[26px] final:flex-col final:justify-start final:gap-8',
      )}
    >
      <div
        className={cn(
          'flex w-[620px] flex-col rounded-2xl p-6 shadow-[2px_8px_20px_0px_#84899226]',
          'small:w-full small:p-4',
          'final:w-full final:p-4',
        )}
      >
        <div className={cn('flex items-center justify-between', 'desktop:mb-5', 'final:mb-5')}>
          <h3
            className={cn(
              'font-onest text-[26px] font-medium leading-[34px] text-[#1C1C1C]',
              'desktop:text-xl desktop:leading-[30px]',
              'final:text-xl final:leading-[30px]',
            )}
          >
            {t('title')}
          </h3>
          <div
            className={cn(
              'flex items-center gap-2 rounded-lg bg-[#FAFAFA] px-3 py-[10px]',
              'desktop:px-[10px] desktop:py-2',
              'final:px-[10px] final:py-2',
            )}
          >
            <Image
              src="/images/checkout/Guard.svg"
              alt="Guard"
              width={20}
              height={20}
              className={cn('object-contain', 'desktop:h-4 desktop:w-4', 'final:h-4 final:w-4')}
            />
            <span
              className={cn(
                'font-onest text-[16px] font-medium leading-5 text-[#1FC794]',
                'desktop:text-sm desktop:leading-5',
                'final:text-sm final:leading-5',
              )}
            >
              {t('securityTitle')}
            </span>
          </div>
        </div>

        <div className={cn('mb-8 flex flex-col', 'desktop:mb-6', 'final:mb-6')}>
          <PaymentForm
            onSuccess={handleSuccess}
            onError={handleError}
            amount={1000}
            currency="eur"
            clientSecret={clientSecret}
            isSubscription={selectedPlan?.id === 3}
          />
        </div>

        <div className={cn('mb-4 flex items-center justify-between', 'desktop:hidden', 'final:hidden')}>
          <div className="flex items-center gap-2">
            <Image src="/images/checkout/Guard.svg" alt="Guard" width={20} height={20} className="object-contain" />
            <span className="font-onest text-sm font-medium leading-5 text-[#585858]">{t('secureEncryption')}</span>
          </div>
          <Image src="/images/checkout/Nortion.png" alt="Norton" width={69} height={28} />
        </div>

        <div className={cn('mb-4 h-px w-full bg-[#E7E7E7]/50', 'desktop:hidden', 'final:hidden')}></div>

        <p className="font-onest text-xs font-normal leading-[18px] text-[#828181]">
          {t.rich(`${selectedPlan?.id === 3 ? 'termsAgreementAnnual' : 'termsAgreement'}`, {
            price: selectedPlan?.price ?? '',
            subscriptionPrice: 44.95,
            terms: (chunks) => (
              <a
                href={`/${locale}/terms-conditions`}
                target="_blank"
                rel="noopener noreferrer"
                className="font-semibold text-[#585858] underline"
              >
                {chunks}
              </a>
            ),
            privacy: (chunks) => (
              <a
                href={`/${locale}/privacy-policy`}
                target="_blank"
                rel="noopener noreferrer"
                className="font-semibold text-[#585858] underline"
              >
                {chunks}
              </a>
            ),
            email: (chunks) => (
              <a href="mailto:<EMAIL>" className="font-semibold text-[#585858] underline">
                {chunks}
              </a>
            ),
          })}
        </p>
      </div>

      <div
        className={cn(
          'w-[620px] rounded-2xl p-6 shadow-[2px_8px_20px_0px_#84899226]',
          'small:w-full small:p-4',
          'final:w-full final:p-4',
        )}
      >
        <div className={cn('mb-6 flex items-center justify-between', 'desktop:mb-5', 'final:mb-5')}>
          <h3
            className={cn(
              'font-onest text-[26px] font-medium leading-[34px] text-[#1C1C1C]',
              'desktop:text-xl desktop:leading-[30px]',
              'final:text-xl final:leading-[30px]',
            )}
          >
            {t('orderDetails')}
          </h3>
          <div className="flex items-center rounded-lg bg-[#FAFAFA] px-3 py-[10px]">
            <span
              className={cn(
                'font-onest text-[16px] font-medium leading-5 text-[#1C1C1C]',
                'desktop:text-sm desktop:leading-5',
                'final:text-sm final:leading-5',
              )}
            >
              {t('fullAccess7Days')}
            </span>
          </div>
        </div>

        <div
          className={cn(
            'mb-5 grid grid-cols-2 gap-x-3 gap-y-3',
            'desktop:mb-3 desktop:grid-cols-1 desktop:gap-y-2',
            'final:mb-3 final:grid-cols-1 final:gap-y-2',
          )}
        >
          {plans.map((plan, index) => (
            <div
              key={index}
              className={cn(
                'flex h-[76px] w-[280px] items-center gap-4 rounded-[10px] border border-[#E7E7E7] py-[15px] pl-4',
                'desktop:h-[58px] desktop:w-full desktop:gap-3 desktop:py-[10px] desktop:pl-3',
                'final:h-[58px] final:w-full final:gap-3 final:py-[10px] final:pl-3',
              )}
            >
              <Image
                src={plan.image}
                alt="Plan"
                width={46}
                height={46}
                className={cn('object-contain', 'desktop:h-[38px] desktop:w-[38px]', 'final:h-[38px] final:w-[38px]')}
              />
              <span className="font-onest text-[16px] font-medium leading-[22px] text-[#1C1C1C]">
                {t(`planFeatures.${plan.name}`)}
              </span>
            </div>
          ))}
        </div>

        <div
          className={cn(
            'mb-8 flex items-center justify-between rounded-[10px] bg-[#F9F9F9] px-4 py-5 font-onest text-xl font-medium leading-[26px]',
            'desktop:mb-5 desktop:p-3 desktop:text-[16px] desktop:leading-5',
            'final:mb-5 final:p-3 final:text-[16px] final:leading-5',
          )}
        >
          <span>{t('total')}</span>
          <span className="font-bold">{selectedPlan?.total ?? selectedPlan?.price}</span>
        </div>

        <div className={cn('mb-6 flex justify-center gap-2', 'desktop:mb-4', 'final:mb-4')}>
          {cards2.map((item, index) => (
            <div
              key={index}
              className={cn('flex h-8 w-[52px] items-center justify-center', 'desktop:hidden final:hidden')}
            >
              <Image src={item.image} alt={item.name} width={52} height={32} className={cn('object-contain')} />
            </div>
          ))}
          {cards3.map((item, index) => (
            <div
              key={index}
              className={cn('flex h-7 w-[45.5px] items-center justify-center', 'hidden desktop:block final:block')}
            >
              <Image src={item.image} alt={item.name} width={45.5} height={28} className={cn('object-contain')} />
            </div>
          ))}
        </div>

        <div className={cn('mb-6 h-px w-full bg-[#E7E7E7]/50', 'desktop:mb-4', 'final:mb-4')}></div>

        <div
          className={cn(
            'flex items-center justify-between',
            'desktop:flex-col desktop:items-stretch desktop:gap-2',
            'final:flex-col final:items-stretch final:gap-2',
          )}
        >
          {badgeKeys.map((item, index) => (
            <div
              key={index}
              className={cn(
                'flex items-center gap-2 rounded-[10px] bg-[#E9F9F4] px-[10px] py-2',
                'desktop:px-3 desktop:py-[11px]',
                'final:px-3 final:py-[11px]',
              )}
            >
              <Image
                src={'/images/checkout/CircleCheck_filled.svg'}
                alt={'Check'}
                width={20}
                height={20}
                className={cn('object-contain', 'desktop:h-4 desktop:w-4', 'final:h-4 final:w-4')}
              />
              <span
                className={cn(
                  'font-onest text-[16px] font-normal leading-5 text-[#1C1C1C]',
                  'desktop:text-sm desktop:leading-[18px]',
                  'final:text-sm final:leading-[18px]',
                )}
              >
                {t(`badges.${item}`)}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
