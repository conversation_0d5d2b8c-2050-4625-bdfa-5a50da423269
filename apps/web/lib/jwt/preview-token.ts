import jwt from 'jsonwebtoken';

interface PreviewTokenPayload {
  documentId: string;
  userId: string;
  timestamp: number;
  iat?: number;
  exp?: number;
}

/**
 * Generate a 30s validity jwt for document preview
 * @param {string} documentId - The document ID
 * @param {string} userId - The user ID
 * @return {*}  {string} - A JWT that can provide access to preview documents
 */
export function generatePreviewToken(documentId: string, userId: string): string {
  const payload: PreviewTokenPayload = {
    documentId,
    userId,
    timestamp: Date.now(),
  };

  return jwt.sign(payload, process.env.FILE_PREVIEW_JWT_SECRET!, {
    algorithm: 'HS256',
    expiresIn: '30s', // Token expires in 30 seconds
  });
}

/**
 * Validates a JWT for Documents previews
 * @param {string} token - The token to be validates
 * @return {*}  {(PreviewTokenPayload | null)}
 */
export function validatePreviewToken(token: string): PreviewTokenPayload | null {
  try {
    const decoded = jwt.verify(token, process.env.FILE_PREVIEW_JWT_SECRET!) as PreviewTokenPayload;

    // Double check of du for more security
    const now = Date.now();
    const tokenAge = now - decoded.timestamp;

    if (tokenAge > 30000) {
      throw new Error('Token expired by timestamp');
    }

    return decoded;
  } catch (error) {
    console.error('Token validation error:', error);
    return null;
  }
}
