import { getTranslations } from 'next-intl/server';

import pathsConfig from '@pdfily/config/paths.config';
import { cn } from '@pdfily/ui/utils';

import { FormMessage, Message } from '@/components/form-message';
import SignUpForm from '@/components/auth/sign-up-form';
import { Link } from '@/lib/i18n/navigation';

export default async function Signup(props: { searchParams: Promise<Message> }) {
  const t = await getTranslations('Auth.signUp');
  const searchParams = await props.searchParams;

  if ('message' in searchParams || 'success' in searchParams) {
    return (
      <div className="sm:max-w-md flex h-screen w-full flex-1 items-center justify-center gap-2 p-4">
        <FormMessage message={searchParams} />
      </div>
    );
  }

  return (
    <>
      <h1
        className={cn(
          'mb-5 text-center font-onest text-[32px] font-medium leading-9 tracking-[-1px] text-[#1C1C1C]',
          'final:mb-4 final:text-[26px] final:leading-[30px]',
        )}
      >
        {t('title')}
      </h1>
      <p
        className={cn(
          'mb-8 text-center font-onest text-[16px] font-normal leading-[22px] text-[#585858]',
          'final:mb-6',
        )}
      >
        {t.rich('goToSignin', {
          Link: (chunks) => (
            <Link className="font-medium text-[#F0401D] hover:underline" href={pathsConfig.auth.signIn}>
              {chunks}{' '}
            </Link>
          ),
        })}
      </p>
      <SignUpForm searchParams={searchParams} />
    </>
  );
}
