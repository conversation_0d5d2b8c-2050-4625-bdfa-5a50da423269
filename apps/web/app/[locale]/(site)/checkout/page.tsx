'use client';

import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslations } from 'next-intl';
import { sendGTMEvent } from '@next/third-parties/google';
import { cn } from '@pdfily/ui/utils';
import PlanSelection from '@/components/checkout/plan-selection';
import PaymentDetails from '@/components/checkout/payment-details';
import SuccessPage from '@/components/checkout/success-page';
import ProgressSteps from '@/components/checkout/progress-steps';
import { useRouter } from '@/lib/i18n/navigation';

export interface Plan {
  id: number;
  name: string;
  price: string;
  period: string;
}
export type Step = 'plan' | 'payment' | 'success';

/**
 * Checkout Steps Component
 * Handles the checkout flow including Plan Selection, Payment, and Success steps.
 */
export default function CheckoutPage() {
  const t = useTranslations('Checkout.steps');
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null); // Selected plan state

  // State for the current step (initialized from the URL step)
  const [currentStep, setCurrentStep] = useState<Step>('plan');
  const currentStepNumber = useMemo(() => {
    const stepMap: Record<Step, number> = {
      plan: 2,
      payment: 3,
      success: 4,
    };

    return stepMap[currentStep] ?? 2; // fallback to 2 if step is missing
  }, [currentStep]);

  /**
   * Navigate to the selected step by updating the URL.
   */
  const navigateStep = useCallback(
    (step: Step) => {
      const url = new URLSearchParams(searchParams);
      url.set('step', step);
      router.push(`/checkout?${url.toString()}`);
    },
    [searchParams, router],
  );

  // Get step from search params
  const step = useMemo(() => searchParams.get('step') ?? 'plan', [searchParams]);
  useEffect(() => {
    const validSteps: Set<Step> = new Set(['plan', 'payment', 'success']);
    setCurrentStep(validSteps.has(step as Step) ? (step as Step) : 'plan');
  }, [step]);

  // Handle plan selection
  const handlePlanSelect = (plan: Plan) => {
    setSelectedPlan(plan);
    sendGTMEvent({ event: 'selected_plan', plan });
    navigateStep('payment');
  };

  // Handle payment completion
  const handlePaymentComplete = () => {
    navigateStep('success');
  };

  // Get page title based on current step (fallback to default)
  const getPageTitle = () => t(currentStep || 'default');

  return (
    <div className={cn('flex w-full max-w-7xl flex-col')}>
      <div className={cn('mt-8 flex flex-col items-center', 'desktop:mt-5 desktop:px-4', 'final:mt-5 final:px-4')}>
        <p
          className={cn(
            'mb-5 text-center font-onest text-[40px] font-medium leading-[44px] tracking-[-0.05px] text-[#1C1C1C]',
            'desktop:mb-0 desktop:text-[26px] desktop:leading-[30px]',
            'final:mb-0 final:text-[26px] final:leading-[30px]',
          )}
        >
          {getPageTitle()}
        </p>

        <ProgressSteps currentStep={currentStepNumber} />

        {currentStep === 'plan' && <PlanSelection onSelectPlan={handlePlanSelect} />}
        {currentStep === 'payment' && (
          <PaymentDetails onPaymentComplete={handlePaymentComplete} selectedPlan={selectedPlan} />
        )}
        {currentStep === 'success' && <SuccessPage />}
      </div>
    </div>
  );
}
