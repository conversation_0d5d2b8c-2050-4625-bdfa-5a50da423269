import { z } from 'zod';

/**
 * Defines the contact form input and validates them
 */
export const ContactFormSchema = z.object({
  name: z
    .string({ required_error: 'Name is required' })
    .trim()
    .min(2, 'Name must be at least 2 characters long')
    .max(100, 'Name must be less than 100 characters long'),

  email: z.string({ required_error: 'Email address is required' }).trim().toLowerCase().email('Invalid email address'),

  message: z
    .string({ required_error: 'Message is required' })
    .trim()
    .min(10, 'Message must be at least 10 characters long')
    .max(1000, 'Message must be less than 1000 characters long'),
});

/**
 * Automatically derived TypeScript type
 */
export type TContactForm = z.infer<typeof ContactFormSchema>;
