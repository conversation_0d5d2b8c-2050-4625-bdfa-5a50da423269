'use client';

import { useTranslations } from 'next-intl';
import { cn } from '@pdfily/ui/utils';
import { Tabs, TabsList, TabsTrigger } from '@pdfily/ui/tabs';

interface SettingsTabsProps {
  activeTab: string
  onTabChange: (value: string) => void
}

const tabs = [
  { value: 'account', name: 'account' },
  { value: 'payment', name: 'payment' },
  { value: 'membership', name: 'membership' },
];

/**
 * SettingsTabs - Renders the settings navigation tabs.
 *
 * @param {SettingsTabsProps} props - The component props.
 * @param {string} props.activeTab - The currently active tab.
 * @param {(value: string) => void} props.onTabChange - The callback for tab change.
 */
export default function SettingsTabs({ activeTab, onTabChange }: SettingsTabsProps) {
  const t = useTranslations('Settings.tabs');

  return (
    <Tabs
      defaultValue="account"
      className={cn('flex w-full max-w-[1440px] justify-center border-b border-b-[#E7E7E7]')}
      onValueChange={onTabChange}
    >
      <TabsList
        className={cn(
          'flex h-fit w-full max-w-[1060px] justify-start gap-7 bg-transparent p-0',
          'desktop:gap-0 final:gap-0',
        )}
      >
        {tabs.map((tab, index) => (
          <TabsTrigger
            key={index}
            value={tab.value}
            className={cn('relative p-0 pb-[15px]', 'desktop:flex-1 desktop:pb-3', 'final:flex-1 final:pb-3')}
          >
            <span
              className={cn(
                'font-onest text-[16px] font-medium leading-5',
                'desktop:text-sm desktop:leading-[18px]',
                'final:text-sm final:leading-[18px]',
                activeTab === tab.value ? 'text-[#1C1C1C]' : 'text-[#585858]',
              )}
            >
              {t(tab.name)}
            </span>
            {activeTab === tab.value && (
              <div
                className={cn('absolute bottom-0 left-0 h-1 w-full rounded-t-[10px] bg-[#F0401D]', 'final:h-0.5')}
              ></div>
            )}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  )
}