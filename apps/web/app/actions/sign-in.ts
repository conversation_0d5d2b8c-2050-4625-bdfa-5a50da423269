'use server';

import { redirect } from 'next/navigation';

import authConfig from '@pdfily/config/auth.config';
import pathsConfig from '@pdfily/config/paths.config';
import { encodedRedirect } from '@pdfily/shared/utils';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';
import { getSupabaseServerAdminClient } from '@pdfily/supabase/server-admin-client'; // Adjust import path if needed

/**
 * @description Handles user sign-in with email and password using Supabase.
 * Handles conflict resolution by reassigning documents from an anonymous user to the authenticated user.
 *
 * @param {FormData} formData - The form data containing user credentials (email and password).
 */
export const signInAction = async (formData: FormData) => {
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const captchaToken = formData.get('captchaToken') as string;

  const supabase = getSupabaseServerClient();

  // 1. Sign in anonymously (assuming the user is already signed in anonymously)
  const { data: anonData } = await supabase.auth.getSession();

  // 2. Sign in to the existing account
  const { data, error: signInError } = await supabase.auth.signInWithPassword({
    email,
    password,
    ...(authConfig.captchaEnabled && {
      options: { captchaToken },
    }),
  });

  const existingUser = data?.user;

  if (signInError) {
    return encodedRedirect('error', pathsConfig.auth.signIn, signInError.message);
  }

  if (existingUser) {
    const anonymousUserId = anonData?.session?.user?.id ?? '';
    const existingUserId = existingUser?.id ?? '';

    try {
      // 3. Reassign documents tied to the anonymous user to the existing user
      if (anonymousUserId && existingUserId) {
        const adminSupabase = getSupabaseServerAdminClient();
        // Update the user_id of all documents owned by the anonymous user to the existing user
        await adminSupabase.from('documents').update({ user_id: existingUserId }).eq('user_id', anonymousUserId);
      }
    } catch (error) {
      return encodedRedirect('error', pathsConfig.auth.signIn, 'Unexpected error during ownership transfer');
    }
  }

  return redirect(pathsConfig.app.home);
};
