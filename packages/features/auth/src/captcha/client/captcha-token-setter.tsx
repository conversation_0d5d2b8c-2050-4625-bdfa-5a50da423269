'use client';

import { useContext, useState } from 'react';
import { Turnstile, TurnstileProps } from '@marsidev/react-turnstile';

import { CaptchaWrapperContext } from './captcha-wrapper-provider';

/**
 * @name CaptchaTokenSetter
 * A React component that renders a Turnstile captcha widget and sets the captcha token
 * upon successful completion. This component uses the Turnstile library for captcha
 * rendering and verification.
 *
 * @param props
 * @returns
 */
export function CaptchaTokenSetter(props: {
  siteKey: string | undefined;
  options?: TurnstileProps;
  lang?: string;
}) {
  const { setToken, setInstance } = useContext(CaptchaWrapperContext);
  const [error, setError] = useState<string | null>(null);

  if (!props.siteKey) {
    console.warn('CaptchaTokenSetter: No site key provided');
    return null;
  }

  if (error) {
    console.error('Turnstile error:', error);
    // Return null to hide the widget if there's an error
    return null;
  }

  const options = props.options ?? {
    options: {
      size: 'invisible',
    },
  };

  return (
    <Turnstile
      ref={(instance) => {
        if (instance) {
          setInstance(instance);
        }
      }}
      lang={props.lang}
      siteKey={props.siteKey}
      onSuccess={(token) => {
        setToken(token);
        setError(null); // Clear any previous errors
      }}
      onError={(error) => {
        console.error('Turnstile error:', error);
        setError(error);
      }}
      onExpire={() => {
        console.warn('Turnstile token expired');
        setToken(''); // Clear the token
      }}
      {...options}
    />
  );
}
