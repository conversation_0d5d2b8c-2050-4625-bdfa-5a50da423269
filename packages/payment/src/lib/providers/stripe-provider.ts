import Stripe from 'stripe';
import React from 'react';
import { User } from '@supabase/supabase-js';
import axios from 'axios';
import {
  PaymentProviderInterface,
  PaymentIntent,
  PaymentVerificationResult,
  WebhookResult,
  CreatePaymentParams,
  ClientConfig,
  PaymentProviderConfig,
  CreateCustomerParams,
  CustomerResult,
  CreateSubscriptionParams,
  SubscriptionInfo,
  SubscriptionStatus,
  UpdateSubscriptionParams,
  UIComponents,
  CardBrandIcon,
  PaymentFormProps,
  SetupIntent,
} from '../../types/payment-types';
import StripeElementsWrapper from '../../ui/stripe/stripe-elements';
import UpdatePaymentMethodWrapper from '../../ui/stripe/update-payment-method';
import { PRICES } from '../utils/prices';

// Import dynamically in actual implementation
// For this file, we'll define placeholders
const stripeCardBrands: CardBrandIcon[] = [
  {
    name: 'visa',
    lightIcon: '/assets/payment/stripe/visa-light.svg',
    darkIcon: '/assets/payment/stripe/visa-dark.svg',
    width: 40,
    height: 25,
  },
  {
    name: 'mastercard',
    lightIcon: '/assets/payment/stripe/mastercard-light.svg',
    darkIcon: '/assets/payment/stripe/mastercard-dark.svg',
    width: 40,
    height: 25,
  },
  {
    name: 'amex',
    lightIcon: '/assets/payment/stripe/amex-light.svg',
    darkIcon: '/assets/payment/stripe/amex-dark.svg',
    width: 40,
    height: 25,
  },
  {
    name: 'discover',
    lightIcon: '/assets/payment/stripe/discover-light.svg',
    darkIcon: '/assets/payment/stripe/discover-dark.svg',
    width: 40,
    height: 25,
  },
];

// Mock translations - would be actual translations in real implementation
const stripeTranslations = {
  en: {
    cardNumber: 'Card number',
    cardExpiry: 'Expiration date',
    cardCvc: 'CVC',
    submit: 'Pay now',
    processingPayment: 'Processing payment...',
    paymentSuccessful: 'Payment successful',
    paymentFailed: 'Payment failed',
  },
  fr: {
    cardNumber: 'Numéro de carte',
    cardExpiry: "Date d'expiration",
    cardCvc: 'CVC',
    submit: 'Payer maintenant',
    processingPayment: 'Traitement du paiement...',
    paymentSuccessful: 'Paiement réussi',
    paymentFailed: 'Échec du paiement',
  },
};

export class StripeProvider implements PaymentProviderInterface {
  private stripe: Stripe;
  private webhookSecret: string;
  private publishableKey: string;

  constructor(config: PaymentProviderConfig) {
    this.stripe = new Stripe(config.apiKey, {
      apiVersion: '2025-04-30.basil' as Stripe.LatestApiVersion,
    });
    this.webhookSecret = config.webhookSecret!;
    this.publishableKey = config.options?.publishableKey || '';
  }

  hasCustomerId(user: User | null): boolean {
    return !!user?.user_metadata.stripe_customer_id;
  }

  // Private method to update user metadata via the API
  private async updateUserMetadata(userId: string, metadata: Record<string, any>): Promise<boolean> {
    try {
      const response = await axios.post(
        '/api/user/update-metadata',
        { userId, metadata },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      return response.data.success === true;
    } catch (error) {
      console.error('Error updating user metadata via API:', error);
      return false;
    }
  }

  async getCustomerId(user: User | null): Promise<string | null> {
    if (this.hasCustomerId(user)) {
      return user!.user_metadata.stripe_customer_id;
    } else {
      const customer = await this.createCustomer({
        email: user?.email!,
        name: user?.user_metadata.name,
        metadata: { userId: user?.id },
      });

      if (user) {
        try {
          // Update the metadata via the API
          await this.updateUserMetadata(user.id, {
            stripe_customer_id: customer.id,
          });
        } catch (error) {
          console.error('Failed to update stripe_customer_id:', error);
        }
      }

      return customer.id;
    }
  }

  async getCustomerPaymentMethods(user: User | null): Promise<any> {
    try {
      // Retrieve the customer and expand its payment methods
      const customerId = user?.user_metadata.stripe_customer_id;
      const customer = (await this.stripe.customers.retrieve(customerId, {
        expand: ['invoice_settings.default_payment_method'],
      })) as any;

      // Retrieve other payment methods
      const paymentMethods = await this.stripe.customers.listPaymentMethods(customerId);

      const formattedPaymentMethods = paymentMethods.data.map((pm: Stripe.PaymentMethod) => ({
        id: pm.id,
        brand: pm.card?.brand,
        last4: pm.card?.last4,
        expMonth: pm.card?.exp_month,
        expYear: pm.card?.exp_year,
      }));

      return {
        customerId,
        paymentMethods: formattedPaymentMethods,
        defaultPaymentMethod: customer.invoice_settings.default_payment_method,
      };
    } catch (error) {
      console.log('Get Customer payment method error', { error });
      return {};
    }
  }

  async updateCustomerPaymentMethod(paymentMethodId: string, user?: User | null): Promise<any> {
    try {
      const customerId = user?.user_metadata.stripe_customer_id;

      // Retrieve the Stripe customer
      const customer = await this.stripe.customers.retrieve(customerId);
      if (customer.deleted) {
        return { error: 'Customer not found' };
      }

      // Attach the payment method to the customer
      const paymentMethod = await this.stripe.paymentMethods.attach(paymentMethodId, { customer: customerId });

      // Update the customer's default payment method
      await this.stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });

      // Update all the user's subscriptions to use the new Payment Method
      const subscriptions = await this.stripe.subscriptions.list({
        customer: customerId,
        status: 'all',
      });

      for (const subscription of subscriptions.data) {
        await this.stripe.subscriptions.update(subscription.id, {
          default_payment_method: paymentMethodId,
        });
      }

      return paymentMethod;
    } catch (error) {
      console.log('Get Customer payment method error', { error });
      return {};
    }
  }

  async createSetupIntent(user: User | null): Promise<SetupIntent> {
    const setupIntent = await this.stripe.setupIntents.create({
      customer: user?.user_metadata.stripe_customer_id,
      payment_method_types: ['card'],
    });

    return { ...setupIntent, clientSecret: setupIntent.client_secret! };
  }

  async createPaymentIntent(params: CreatePaymentParams): Promise<PaymentIntent> {
    try {
      const { amount, currency, metadata, customerId, productId } = params;

      const stripeParams: Stripe.PaymentIntentCreateParams = {
        amount: metadata?.planId === '3' ? 0 : Math.round((PRICES.us?.oneTime?.amount || amount) * 100), // Stripe expects amount in cents
        currency: PRICES.us?.currency || currency,
        setup_future_usage: 'off_session',
        automatic_payment_methods: { enabled: true },
        metadata,
      };

      // Add the productId to the metadata if it is defined
      if (productId) {
        stripeParams.metadata!.productId = productId;
      }

      // Add the customerId only if it is defined
      if (customerId) {
        stripeParams.customer = customerId;
      }

      const paymentIntent = await this.stripe.paymentIntents.create(stripeParams);

      return {
        id: paymentIntent.id,
        amount: paymentIntent.amount / 100, // Convert back to decimal
        currency: paymentIntent.currency,
        status: paymentIntent.status,
        clientSecret: paymentIntent.client_secret || '',
        customerId: (paymentIntent.customer as string) || undefined,
      };
    } catch (error) {
      console.error('Stripe createPaymentIntent error:', error);
      throw error;
    }
  }

  async confirmPayment(paymentId: string, paymentMethodId: string): Promise<PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.confirm(paymentId, {
        payment_method: paymentMethodId,
      });

      return {
        id: paymentIntent.id,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency,
        status: paymentIntent.status,
        clientSecret: paymentIntent.client_secret || '',
        customerId: (paymentIntent.customer as string) || undefined,
      };
    } catch (error) {
      console.error('Stripe confirmPayment error:', error);
      throw error;
    }
  }

  async verifyPayment(paymentId: string): Promise<PaymentVerificationResult> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentId);

      return {
        isValid: paymentIntent.status === 'succeeded',
        paymentId: paymentIntent.id,
        status: paymentIntent.status,
        details: {
          amount: paymentIntent.amount / 100,
          currency: paymentIntent.currency,
          metadata: paymentIntent.metadata,
          customerId: paymentIntent.customer,
        },
      };
    } catch (error) {
      console.error('Stripe verifyPayment error:', error);
      throw error;
    }
  }

  async createCustomer(params: CreateCustomerParams): Promise<CustomerResult> {
    try {
      const { email, name, metadata } = params;

      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata,
      });

      return {
        id: customer.id,
        email: customer.email || email,
        name: customer.name || undefined,
        metadata: customer.metadata,
      };
    } catch (error) {
      console.error('Stripe createCustomer error:', error);
      throw error;
    }
  }

  async createSubscription(params: CreateSubscriptionParams): Promise<SubscriptionInfo> {
    try {
      const {
        customerId,
        paymentMethodId,
        priceId,
        description,
        trialPeriodDays,
        metadata,
        enforceNoTrial = false,
        subscriptionType,
      } = params;

      let subscription_price_id: string;

      if (priceId) {
        subscription_price_id = priceId; // Prioritize the provided priceId
      } else if (enforceNoTrial) {
        // Select the priceId according to subscriptionType for the cases without the trial period
        subscription_price_id =
          subscriptionType === '4weeks'
            ? process.env.NEXT_PUBLIC_STRIPE_4_WEEKS_SUBSCRIPTOIN_PRICE_ID!
            : process.env.NEXT_PUBLIC_STRIPE_SUBSCRIPTION_PRICE_ID!;
      } else {
        // Trial prices cases
        subscription_price_id =
          trialPeriodDays && trialPeriodDays > 0
            ? process.env.NEXT_PUBLIC_STRIPE_4_WEEKS_SUBSCRIPTOIN_PRICE_ID!
            : process.env.NEXT_PUBLIC_STRIPE_SUBSCRIPTION_PRICE_ID!;
      }

      if (!subscription_price_id) {
        throw new Error('Price ID is required');
      }

      // If a paymentMethodId is provided, we need to first attach it to the client
      if (paymentMethodId) {
        await this.stripe.paymentMethods.attach(paymentMethodId, {
          customer: customerId,
        });

        // Set this payment method as the default payment method
        await this.stripe.customers.update(customerId, {
          invoice_settings: {
            default_payment_method: paymentMethodId,
          },
        });
      }

      // Create the subscription
      const subscriptionParams: Stripe.SubscriptionCreateParams = {
        customer: customerId,
        description: description || 'Annual subscription created',
        items: [{ price: subscription_price_id }],
        default_payment_method: paymentMethodId,
        expand: ['latest_invoice'],
        metadata,
        collection_method: 'charge_automatically',
      };

      // For subscriptions without trial period
      if (enforceNoTrial || trialPeriodDays === 0) {
        // Options to charge immediately
        (subscriptionParams.off_session = true),
          (subscriptionParams.payment_settings = {
            save_default_payment_method: 'on_subscription',
          });
      } else {
        subscriptionParams.trial_period_days = trialPeriodDays;
      }

      const subscription = await this.stripe.subscriptions.create(subscriptionParams);

      // Get the payment_intent_id if available
      let paymentIntentId: string | undefined;
      if (subscription.latest_invoice && typeof subscription.latest_invoice !== 'string') {
        // Use a type assertion with additional check
        const invoice = subscription.latest_invoice as any;
        if (invoice && invoice.payment_intent && typeof invoice.payment_intent !== 'string') {
          paymentIntentId = invoice.payment_intent.id;
        }
      }

      return {
        id: subscription.id,
        customerId: subscription.customer as string,
        status: this.mapSubscriptionStatus(subscription.status),
        currentPeriodEnd: subscription.items.data[0]?.current_period_end,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        cancelAt: subscription.cancel_at || null,
        trialEnd: subscription.trial_end || null,
        priceId: subscription.items.data[0]?.price?.id || subscription_price_id!,
        paymentIntentId,
      };
    } catch (error) {
      console.error('Stripe createSubscription error:', error);
      throw error;
    }
  }

  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<SubscriptionInfo> {
    try {
      let subscription: Stripe.Subscription;

      if (cancelAtPeriodEnd) {
        // Cancel at the end of the current period
        subscription = await this.stripe.subscriptions.update(subscriptionId, {
          cancel_at_period_end: true,
        });
      } else {
        // Cancel immediately
        subscription = await this.stripe.subscriptions.cancel(subscriptionId);
      }

      return {
        id: subscription.id,
        customerId: subscription.customer as string,
        status: this.mapSubscriptionStatus(subscription.status),
        currentPeriodEnd: subscription.items.data[0]?.current_period_end,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        cancelAt: subscription.cancel_at || null,
        trialEnd: subscription.trial_end || null,
        priceId: subscription.items.data[0]?.price?.id || '',
      };
    } catch (error) {
      console.error('Stripe cancelSubscription error:', error);
      throw error;
    }
  }

  async reactivateSubscription(subscriptionId: string): Promise<SubscriptionInfo> {
    try {
      // Check the current subscription status
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);

      // Check if the subscription can be re-activated
      if (subscription.status === 'canceled') {
        throw new Error('Cannot reactivate a canceled subscription. Please create a new subscription.');
      }

      if (!subscription.cancel_at_period_end) {
        throw new Error('Subscription is not scheduled for cancellation, no need to reactivate.');
      }

      // Re-activate the subscription by deleting the cancelling at the end of period
      const updatedSubscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false,
      });

      return {
        id: updatedSubscription.id,
        customerId: updatedSubscription.customer as string,
        status: this.mapSubscriptionStatus(updatedSubscription.status),
        currentPeriodEnd: updatedSubscription.items.data[0]?.current_period_end,
        cancelAtPeriodEnd: updatedSubscription.cancel_at_period_end,
        cancelAt: updatedSubscription.cancel_at || null,
        trialEnd: updatedSubscription.trial_end || null,
        priceId: updatedSubscription.items.data[0]?.price?.id || '',
      };
    } catch (error) {
      console.error('Stripe reactivateSubscription error:', error);
      throw error;
    }
  }

  async updateSubscription(params: UpdateSubscriptionParams): Promise<SubscriptionInfo> {
    try {
      const { subscriptionId, priceId, cancelAtPeriodEnd, cancelAt, metadata } = params;

      const updateParams: Stripe.SubscriptionUpdateParams = {};

      if (priceId) {
        const existingSubscription = await this.stripe.subscriptions.retrieve(subscriptionId);
        if (existingSubscription.items.data[0]) {
          updateParams.items = [
            {
              id: existingSubscription.items.data[0].id,
              price: priceId,
            },
          ];
        }
      }

      if (cancelAtPeriodEnd !== undefined) {
        updateParams.cancel_at_period_end = cancelAtPeriodEnd;
      }

      if (cancelAt !== undefined) {
        updateParams.cancel_at = cancelAt;
      }

      if (metadata) {
        updateParams.metadata = metadata;
      }

      const subscription = await this.stripe.subscriptions.update(subscriptionId, updateParams);

      return {
        id: subscription.id,
        customerId: subscription.customer as string,
        status: this.mapSubscriptionStatus(subscription.status),
        currentPeriodEnd: subscription.items.data[0]?.current_period_end,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        cancelAt: subscription.cancel_at || null,
        trialEnd: subscription.trial_end || null,
        priceId: subscription.items.data[0]?.price?.id || '',
      };
    } catch (error) {
      console.error('Stripe updateSubscription error:', error);
      throw error;
    }
  }

  async getCurrentSubscription(subscriptionId: string): Promise<any> {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      const productId = subscription.items.data[0]?.price.product as string;
      const product = await this.stripe.products.retrieve(productId);

      const planDetails = {
        planName: product.name,
        amount: subscription.items.data[0]?.price.unit_amount ? subscription.items.data[0]?.price.unit_amount / 100 : 0,
        currency: subscription.items.data[0]?.price.currency,
        interval: subscription.items.data[0]?.price?.recurring?.interval,
        subscription,
        product,
      };

      return planDetails;
    } catch (error) {
      console.error('Stripe get suubscription error:', error);
      throw error;
    }
  }

  async switchSubscriptionToAnnual(subscriptionId: string): Promise<SubscriptionInfo> {
    try {
      // Check the subscription status
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);

      if (subscription.status === 'canceled') {
        throw new Error('Cannot switch plan for a canceled subscription. Please create a new subscription.');
      }

      // Check if the subscription is using the 4 weeks plan
      const currentItem = subscription.items.data[0];
      if (!currentItem || currentItem.price?.id !== process.env.NEXT_PUBLIC_STRIPE_4_WEEKS_SUBSCRIPTOIN_PRICE_ID) {
        throw new Error('Subscription is not on the 4-weeks plan.');
      }

      // Validate the annual plan price
      const newPriceId = process.env.NEXT_PUBLIC_STRIPE_SUBSCRIPTION_PRICE_ID;
      if (!newPriceId) {
        throw new Error('Annual plan price ID is not defined.');
      }

      // Updating subscription params
      const updateParams: Stripe.SubscriptionUpdateParams = {
        items: [
          {
            id: currentItem.id, // Delete the 4-weeks item
            deleted: true,
          },
          {
            price: newPriceId, // Add the annual item
          },
        ],
        proration_behavior: 'create_prorations', // Apply prorations
      };

      // Keep the trial period if the subscription is in the 'trialing' status
      if (subscription.status === 'trialing' && subscription.trial_end) {
        updateParams.trial_end = subscription.trial_end; // Keep the end date
      }

      // Update the subscription
      const updatedSubscription = await this.stripe.subscriptions.update(subscriptionId, updateParams);

      return {
        id: updatedSubscription.id,
        customerId: updatedSubscription.customer as string,
        status: this.mapSubscriptionStatus(updatedSubscription.status),
        currentPeriodEnd: updatedSubscription.items.data[0]?.current_period_end,
        cancelAtPeriodEnd: updatedSubscription.cancel_at_period_end,
        cancelAt: updatedSubscription.cancel_at || null,
        trialEnd: updatedSubscription.trial_end || null,
        priceId: updatedSubscription.items.data[0]?.price?.id || newPriceId,
      };
    } catch (error) {
      console.error('Stripe switchSubscriptionToAnnual error:', error);
      throw error;
    }
  }

  async handleWebhook(payload: any, signature: string): Promise<WebhookResult> {
    try {
      const event = this.stripe.webhooks.constructEvent(payload, signature, this.webhookSecret);

      let eventType: string;
      let eventData: any = {};

      // Map Stripe event types to generic types
      switch (event.type) {
        case 'payment_intent.succeeded':
          eventType = 'payment_succeeded';
          eventData = event.data.object;
          break;
        case 'payment_intent.payment_failed':
          eventType = 'payment_failed';
          eventData = event.data.object;
          break;
        case 'customer.subscription.created':
          eventType = 'subscription_created';
          eventData = event.data.object;
          break;
        case 'customer.subscription.updated':
          eventType = 'subscription_updated';
          eventData = event.data.object;
          break;
        case 'customer.subscription.deleted':
          eventType = 'subscription_cancelled';
          eventData = event.data.object;
          break;
        case 'customer.subscription.trial_will_end':
          eventType = 'subscription_trial_ending';
          eventData = event.data.object;
          break;
        case 'invoice.payment_succeeded':
          eventType = 'subscription_payment_succeeded';
          eventData = event.data.object;
          break;
        case 'invoice.payment_failed':
          eventType = 'subscription_payment_failed';
          eventData = event.data.object;
          break;
        default:
          eventType = event.type;
          eventData = event.data.object;
      }

      return {
        received: true,
        type: eventType,
        id: event.id,
        data: eventData,
      };
    } catch (error) {
      console.error('Stripe webhook handling error:', error);
      throw error;
    }
  }

  async refundPayment(paymentId: string, amount?: number): Promise<any> {
    try {
      const refundParams: Stripe.RefundCreateParams = {
        payment_intent: paymentId,
      };

      if (amount) {
        refundParams.amount = Math.round(amount * 100);
      }

      const refund = await this.stripe.refunds.create(refundParams);

      return {
        id: refund.id,
        amount: refund.amount / 100,
        status: refund.status,
      };
    } catch (error) {
      console.error('Stripe refundPayment error:', error);
      throw error;
    }
  }

  getClientConfig(): ClientConfig {
    return {
      publicKey: this.publishableKey,
      paymentGateway: 'stripe',
    };
  }

  getUIComponents(): UIComponents {
    const self = this;

    // Create a function that will inject the public key into the StripeElements component
    const StripePaymentFormWithConfig = (props: PaymentFormProps) => {
      return React.createElement(StripeElementsWrapper, {
        ...props,
        stripePublicKey: this.publishableKey,
      });
    };

    // Create a function tha will rendre the update payment method form with all the config
    const StripeUpdatePaymentMethodWithConfig = (props: PaymentFormProps) => {
      return React.createElement(UpdatePaymentMethodWrapper, {
        ...props,
        stripePublicKey: this.publishableKey,
        stripeProvider: self,
      });
    };

    return {
      // We use our wrapper function to configure the component with the public key
      PaymentForm: StripePaymentFormWithConfig,
      UpdatePaymentMethodForm: StripeUpdatePaymentMethodWithConfig,

      // Visual elements
      logo: '/assets/payment/stripe/stripe-logo.svg',
      cardBrands: stripeCardBrands,

      // Supported payment methods - Stripe automatically handles Apple Pay and Google Pay buttons
      // if they are enabled in the Stripe dashboard and the browser supports them
      supportedPaymentMethods: ['card'],

      // Translations
      translations: stripeTranslations,
    };
  }

  // Utility function to map Stripe subscription statuses to our own statuses
  private mapSubscriptionStatus(stripeStatus: string): SubscriptionStatus {
    switch (stripeStatus) {
      case 'incomplete':
        return SubscriptionStatus.INCOMPLETE;
      case 'incomplete_expired':
        return SubscriptionStatus.INCOMPLETE_EXPIRED;
      case 'trialing':
        return SubscriptionStatus.TRIALING;
      case 'active':
        return SubscriptionStatus.ACTIVE;
      case 'past_due':
        return SubscriptionStatus.PAST_DUE;
      case 'canceled':
        return SubscriptionStatus.CANCELED;
      case 'unpaid':
        return SubscriptionStatus.UNPAID;
      default:
        return SubscriptionStatus.INCOMPLETE;
    }
  }
}
