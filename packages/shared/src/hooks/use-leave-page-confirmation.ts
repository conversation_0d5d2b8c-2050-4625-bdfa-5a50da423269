'use client';

import { useEffect } from 'react';

/**
 * Hook to show browser's "Leave site?" confirmation when user tries to close/refresh/navigate.
 * @param enabled Boolean flag to enable/disable this behavior.
 */
export const useLeavePageConfirmation = (enabled: boolean = true) => {
  useEffect(() => {
    if (!enabled) return;

    // Handle Before
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      event.preventDefault();
      event.returnValue = '';
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [enabled]);
};
