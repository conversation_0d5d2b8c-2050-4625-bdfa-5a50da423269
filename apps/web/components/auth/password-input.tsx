'use client'

import { useEffect, useState } from 'react'
import { Input } from '@pdfily/ui/input'
import { cn } from '@pdfily/ui/utils'
import Image from 'next/image'

interface PasswordInputProps {
  name: string
  placeholder?: string
  required?: boolean
  className?: string
  onChange: (v: string) => void
}

export function PasswordInput({ name, placeholder = '', required = true, className, onChange }: PasswordInputProps) {
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  useEffect(() => {
    onChange(password)
  }, [password])

  return (
    <div className="relative">
      <Input
        type={showPassword ? 'text' : 'password'}
        name={name}
        placeholder={placeholder}
        required={required}
        className={cn(
          'h-[50px] w-full rounded-[10px] border border-[#E7E7E7] px-4 py-[15px]',
          'focus-visible:ring-0',
          'font-onest text-[16px] font-normal leading-5 text-[#1C1C1C] placeholder:text-[#585858]',
          className,
          'final:h-12',
        )}
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />
      <button
        type="button"
        onClick={togglePasswordVisibility}
        className="absolute inset-y-0 right-[15px] flex items-center"
        aria-label={showPassword ? 'Hide password' : 'Show password'}
      >
        <Image
          src={showPassword ? '/images/auth/Eye.svg' : '/images/auth/EyeOff.svg'}
          alt={showPassword ? 'eye' : 'eyeoff'}
          width={20}
          height={20}
          className={cn('object-contain')}
        />
      </button>
    </div>
  )
}
