'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Swal from 'sweetalert2';

import { cn } from '@pdfily/ui/utils';
import { stringifyObject } from '@pdfily/shared/utils';
import { useRouter } from '@/lib/i18n/navigation';

import { useUploadFile } from '@pdfily/supabase/hooks/use-upload-file';
import { useLastEditFileId } from '@pdfily/documents/hooks/use-last-edit-file-id';
import { useLastEditFile } from '@pdfily/documents/hooks/use-last-edit-file';

import BannerSection from '@/components/landing-page/banner-section';
import HowDoesItWorkSection from '@/components/landing-page/how-does-it-work-section';
import WorkflowSection from '@/components/landing-page/workflow-section';
import HowToConvertSection from '@/components/landing-page/how-to-convert-section';
import TestimonialsSection from '@/components/landing-page/testimonials-section';
import QuickEditSection from '@/components/landing-page/quick-edit-section';
import FaqSection from '@/components/landing-page/faq-section';
import CtaSection from '@/components/landing-page/cta-section';
import ProgressModal from '@/components/progress-modal';

export default function Home() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [progress, setProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  const { setFileId } = useLastEditFileId();
  const { setFile } = useLastEditFile();

  useEffect(() => {
    const editor = searchParams.get('editor');
    if (editor) {
      localStorage.setItem('selectedEditor', editor);
    }
  }, [searchParams]);

  const handleError = useCallback((message: string) => {
    console.error('Upload Error:', message);
    Swal.fire({
      icon: 'error',
      title: 'Error',
      text: message,
    });
  }, []);

  const uploadFile = useUploadFile({
    setProgress,
    setDocumentId: (id) => setFileId(id),
    onSuccess: (doc) => {
      setFile(stringifyObject(doc));
      router.push('/document/create');
    },
    onError: (error) => {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error?.message || 'Failed to upload file.',
      });
    },
  });

  const handleFilesSelected = useMemo(() => {
    return async (files: File[]) => {
      const file = files?.[0];
      if (!file) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'No file selected.',
        });
        return;
      }

      setIsUploading(true);
      setProgress(0);

      try {
        await uploadFile(file);
      } catch (error) {
        Swal.fire({
          icon: 'error',
          title: 'Upload Failed',
          text: 'Something went wrong during upload.',
        });
      } finally {
        setIsUploading(false);
      }
    };
  }, [uploadFile]);

  return (
    <>
      <div
        className={cn(
          'flex w-full flex-col items-stretch',
          'max-w-[1440px] gap-y-[120px] px-20 pt-[60px]',
          'mac:gap-y-10 mac:px-4 mac:pt-5',
          'final:gap-y-10 final:px-4 final:pt-5',
        )}
      >
        <BannerSection />
        <HowDoesItWorkSection onFilesSelected={handleFilesSelected} onError={handleError} maxFileSize={50} />
        <WorkflowSection />
        <HowToConvertSection />
        <TestimonialsSection />
        <QuickEditSection onFilesSelected={handleFilesSelected} onError={handleError} maxFileSize={50} />
        <FaqSection />
        <CtaSection />
      </div>

      {isUploading && <ProgressModal isOpen={isUploading} progress={progress} onClose={() => setIsUploading(false)} />}
    </>
  );
}
