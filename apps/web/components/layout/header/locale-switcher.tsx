'use client';

import { useLocale, useTranslations } from 'next-intl';
import { routing } from '@/lib/i18n/routing';
import LocaleSwitcherSelect from './locale-switcher-select';
import { SelectItem } from '@pdfily/ui/select';

export default function LocaleSwitcher() {
  const t = useTranslations('LocaleSwitcher');
  const locale = useLocale();

  return (
    <div className="flex items-center gap-2">
      <LocaleSwitcherSelect defaultValue={locale} label={t('label')}>
        {routing.locales.map((cur) => (
          <SelectItem
            key={cur}
            value={cur}
            className="text-sm text-gray-900 transition-colors duration-100 hover:bg-gray-100 dark:text-gray-100 dark:hover:bg-gray-700"
          >
            {t('locale', { locale: cur })}
          </SelectItem>
        ))}
      </LocaleSwitcherSelect>
    </div>
  );
}
