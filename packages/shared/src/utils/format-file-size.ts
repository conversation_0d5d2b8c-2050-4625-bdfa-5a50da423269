/**
 * Formats file size from bytes to a human-readable string.
 *
 * @function formatFileSize
 * @param {number | null} size - The file size in bytes. Can be null or undefined.
 * @returns {string} - The formatted file size as "MB", "KB", or "B".
 *
 * @example
 * const sizeInBytes = 1048576;
 * const formattedSize = formatFileSize(sizeInBytes);
 * console.log(formattedSize); // "1.0 MB"
 */
export const formatFileSize = (size: number | null | undefined): string => {
  if (size == null || isNaN(size) || size < 0) return "0 B";

  const units = ["B", "KB", "MB", "GB", "TB"];
  let unitIndex = 0;
  let formattedSize = size;

  while (formattedSize >= 1024 && unitIndex < units.length - 1) {
    formattedSize /= 1024;
    unitIndex++;
  }

  return `${formattedSize.toFixed(1)} ${units[unitIndex]}`;
};
