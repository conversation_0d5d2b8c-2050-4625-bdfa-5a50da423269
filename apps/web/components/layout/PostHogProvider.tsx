'use client';

import { useEffect } from 'react';
import posthog from 'posthog-js';
import { PostHogProvider as PHProvider } from 'posthog-js/react';
import posthogConfig from '@pdfily/config/posthog.config';

interface PostHogProviderProps {
  children: React.ReactNode;
}

export function PostHogProvider({ children }: PostHogProviderProps) {
  const { api<PERSON><PERSON>, host, isTrackingEnabled } = posthogConfig;

  useEffect(() => {
    if (isTrackingEnabled && apiKey && host) {
      posthog.init(apiKey, {
        api_host: host,
        capture_pageview: 'history_change',
        person_profiles: 'always',
        capture_pageleave: true,
      });
    }
    return () => {
      // Cleanup: reset PostHog if needed\
      if (posthog && posthog.reset) {
        posthog.reset();
      }
    };
  }, [isTrackingEnabled, apiKey, host]);

  if (isTrackingEnabled && apiKey) {
    return <PHProvider client={posthog}>{children}</PHProvider>;
  }
  return <>{children}</>;
}
