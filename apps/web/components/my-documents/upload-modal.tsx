'use client';

import { useTranslations } from 'next-intl';
import { useRef, useState } from 'react';
import { Upload } from 'lucide-react';
import { formatFileSize } from '@pdfily/shared/utils';
import { Button } from '@pdfily/ui/button';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@pdfily/ui/dialog';

interface UploadModalProps {
  isOpen: boolean
  onClose: () => void
  onUpload: (file: File[]) => void
}

export default function UploadModal({ isOpen, onClose, onUpload }: UploadModalProps) {
  const t = useTranslations('MyDocuments.uploadModal');
  const [dragActive, setDragActive] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      setSelectedFile(e.dataTransfer.files[0])
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0])
    }
  }

  const handleUpload = () => {
    if (selectedFile) {
      onUpload([selectedFile])
      setSelectedFile(null)
    }
  }

  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }

  const handleClose = () => {
    setSelectedFile(null)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="rounded-2xl">
        <DialogHeader>
          <DialogTitle>{t('title')}</DialogTitle>
        </DialogHeader>
        <div
          className={`mt-4 rounded-lg border-2 border-dashed p-8 text-center ${dragActive ? 'border-primary bg-primary/5' : 'border-gray-300'} `}
          onDragEnter={handleDrag}
          onDragOver={handleDrag}
          onDragLeave={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="mx-auto mb-4 h-10 w-10 text-gray-400" />
          <h3 className="mb-2 text-lg font-medium">
            {selectedFile ? selectedFile.name : t('drop')}
          </h3>
          <p className="mb-4 text-sm text-gray-500">
            {selectedFile ? formatFileSize(selectedFile.size) : t('clickToBrowse')}
          </p>
          <input ref={fileInputRef} type="file" className="hidden" onChange={handleFileChange} />
          {!selectedFile && (
            <Button variant="outline" onClick={handleButtonClick}>
              {t('browseFilesButton')}
            </Button>
          )}
        </div>
        <div className="mt-4 flex justify-end gap-2">
          <Button variant="outline" onClick={handleClose}>
            {t('cancelButton')}
          </Button>
          <Button onClick={handleUpload} disabled={!selectedFile} className="bg-red-500 text-white hover:bg-red-600">
            {t('uploadButton')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
