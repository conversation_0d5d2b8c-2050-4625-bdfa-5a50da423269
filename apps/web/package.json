{"name": "@pdfily/web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS='--inspect' next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "supabase": "supabase", "supabase:config:push": "supabase config push", "supabase:db:reset": "supabase db reset", "supabase:db:test": "supabase db test", "supabase:db:lint": "supabase db lint", "supabase:db:update": "supabase db update", "supabase:db:dump:local": "supabase db dump --local --data-only", "supabase:db:push": "supabase db push", "supabase:deploy": "pnpm run supabase:project:link && pnpm run supabase:db:push && pnpm run supabase:config:push", "supabase:migration": "supabase migration new", "supabase:project:link": "dotenv -- cross-var supabase link --project-ref %SUPABASE_PROJECT_REF% --password %SUPABASE_DB_PASSWORD%", "supabase:secrets:dev": "sh ../../scripts/set-supabase-secrets.sh dev", "supabase:secrets:stage": "sh ../../scripts/set-supabase-secrets.sh stage", "supabase:secrets:prod": "sh ../../scripts/set-supabase-secrets.sh prod", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:status": "supabase status", "supabase:typegen": "pnpm run supabase:typegen:packages && pnpm run supabase:typegen:app", "supabase:typegen:packages": "supabase gen types typescript --local > ../../packages/supabase/src/database.types.ts", "supabase:typegen:app": "supabase gen types typescript --local > ./lib/database.types.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.846.0", "@next/third-parties": "^15.4.1", "@nutrient-sdk/viewer": "^1.4.1", "@pdfily/auth": "workspace:*", "@pdfily/config": "workspace:*", "@pdfily/documents": "workspace:*", "@pdfily/payment": "workspace:*", "@pdfily/shared": "workspace:*", "@pdfily/supabase": "workspace:*", "@pdfily/ui": "workspace:*", "@pdftron/webviewer": "^11.6.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@react-pdf-viewer/core": "3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@sendgrid/mail": "^8.1.5", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.83.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "dayjs": "^1.11.13", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "next": "^15.3.3", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "pdfjs-dist": "2.12.313", "posthog-js": "^1.257.0", "react": "^19.1.0", "react-dom": "^19.1.0", "sweetalert2": "^11.22.0", "uuid": "^11.1.0"}, "devDependencies": {"@pdfily/eslint-config": "workspace:*", "@pdfily/tailwind-config": "workspace:*", "@pdfily/typescript-config": "workspace:*", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.16.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "cross-var": "^1.1.0", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "postcss": "^8.5.4", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.8.3"}, "turbo": {"pipeline": {"build": {"outputs": []}}}, "typesVersions": {"*": {"*": ["src/*"]}}}