import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';
import { getSupabaseServerAdminClient } from '@pdfily/supabase/server-admin-client';
import authConfig from '@pdfily/config/auth.config';

export async function POST(request: NextRequest) {
  try {
    // Get the email, token, and type from the request body
    const { email, token, type, captchaToken } = await request.json();

    // Get the supabase client
    const supabase = getSupabaseServerClient();

    // Get the anonymous user session
    const { data: anonData } = await supabase.auth.getSession();

    // Verify the OTP
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type,
      ...(authConfig.captchaEnabled && captchaToken && {
        options: { captchaToken }
      }),
    });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    const existingUser = data?.user;

    if (existingUser) {
      const anonymousUserId = anonData?.session?.user?.id ?? '';
      const existingUserId = existingUser?.id ?? '';

      try {
        // Reassign documents tied to the anonymous user to the existing user
        if (anonymousUserId && existingUserId) {
          const adminSupabase = getSupabaseServerAdminClient();
          // Update the user_id of all documents owned by the anonymous user to the existing user
          await adminSupabase.from('documents').update({ user_id: existingUserId }).eq('user_id', anonymousUserId);
        }
      } catch (error) {
        return NextResponse.json({ error: 'Unexpected error during ownership transfer' }, { status: 500 });
      }
    }

    await supabase.auth.getUser(data?.session?.access_token);

    return NextResponse.json(
      { message: 'OTP verified successfully', user: existingUser, session: data?.session },
      { status: 200 },
    );
  } catch (error: any) {
    console.error('Error verifying OTP:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message,
      },
      { status: 500 },
    );
  }
}
