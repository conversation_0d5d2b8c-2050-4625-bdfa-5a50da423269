import 'server-only';

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../database.types';
import { getServiceRoleKey } from '../get-service-role-key';
import { getSupabaseClientKeys } from '../get-supabase-client-keys';

/**
 * Creates a Supabase Server Admin client instance using the Service Role Key
 *
 * @returns Supabase client instance with admin privileges
 */
export function getSupabaseServerAdminClient<GenericSchema = Database>(): SupabaseClient<GenericSchema> {
  const supabaseUrl: string = getSupabaseClientKeys().url; // The URL of the Supabase project
  const supabaseServiceRoleKey: string = getServiceRoleKey(); // The Supabase Service Role Key

  return createClient<GenericSchema>(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
      persistSession: false,
      detectSessionInUrl: false,
      autoRefreshToken: false,
    },
  });
}
