'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Swal from 'sweetalert2';
import type { User } from '@supabase/supabase-js';
import { useSupabase } from '@pdfily/supabase/hooks/use-supabase';
import { useDownloadLogic } from '@pdfily/documents/hooks/use-check-subscription-and-download';
import { useRouter } from '@/lib/i18n/navigation';

/**
 * This component handles redirection after OAuth login.
 *
 * Behavior:
 * - Retrieves `documentId` and `fileName` from the URL
 * - Checks if the user is authenticated
 * - If not authenticated, shows an error and redirects to document creation
 * - If authenticated:
 *    - Checks if the user has a subscription (via Supabase RPC)
 *    - If subscribed: automatically downloads the document and redirects to "My Documents"
 *    - If not: redirects the user to the checkout page for payment
 */
export default function AuthRedirect() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = useSupabase();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get query parameters from the URL
  const documentId = searchParams.get('documentId');
  const fileName = searchParams.get('fileName');

  useEffect(() => {
    const checkUserAndRedirect = async () => {
      try {
        // Check if the user is authenticated
        const {
          data: { user },
        } = await supabase.auth.getUser();

        setUser(user);
        setIsLoading(false);

        if (!user) {
          // Login failed or no user found
          Swal.fire({
            icon: 'error',
            title: 'Login Error',
            text: 'Failed to log in. Please try again.',
          }).then(() => {
            router.push('/document/create');
          });
          return;
        }
      } catch (error) {
        console.error('Error while checking user:', error);
        setIsLoading(false);
        Swal.fire({
          icon: 'error',
          title: 'Unexpected Error',
          text: 'Something went wrong. Please try again.',
        }).then(() => {
          router.push('/document/create');
        });
      }
    };

    checkUserAndRedirect();
  }, [supabase, router]);

  // Show loading spinner while checking user
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen flex-col gap-y-2">
        <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
        {/* <p className="ml-4">Checking authentication...</p> */}
      </div>
    );
  }

  // If no user after loading, return null (redirect will happen)
  if (!user) {
    return null;
  }

  // Safely use the hook with a guaranteed non-null user
  return <AuthenticatedRedirect user={user} documentId={documentId} fileName={fileName} />;
}

function AuthenticatedRedirect({
  user,
  documentId,
  fileName,
}: {
  user: User;
  documentId: string | null;
  fileName: string | null;
}) {
  const router = useRouter();
  const supabase = useSupabase();
  const downloadLogic = useDownloadLogic(user);
  const { documentDownload } = downloadLogic || {};

  useEffect(() => {
    const checkSubscriptionAndRedirect = async () => {
      try {
        // Check if the user has an active subscription
        const { data: subscriptionData } = await supabase.rpc('has_subscription_status', {
          user_id: user.id,
        });

        const subscription = subscriptionData?.[0];
        const hasSubscription = (subscription?.has_subscription || user.user_metadata.is_subscribed) ?? false;

        if (hasSubscription && documentId && fileName) {
          await documentDownload?.(documentId, fileName);
          router.push('/my-documents');
        } else {
          router.push(`/checkout?documentId=${documentId}`);
        }
      } catch (error) {
        console.error('Error while checking user subscription status:', error);
        Swal.fire({
          icon: 'error',
          title: 'Unexpected Error',
          text: 'Something went wrong. Please try again.',
        }).then(() => {
          router.push('/document/create');
        });
      }
    };

    checkSubscriptionAndRedirect();
  }, [supabase, user, router, documentId, fileName, documentDownload]);

  return (
    <div className="flex items-center justify-center h-screen flex-col gap-y-2">
      <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
      {/* <p className="ml-4">Preparing your file...</p> */}
    </div>
  );
}
