import { cn } from '@pdfily/ui/utils';
import { useTranslations } from 'next-intl';
import Image from 'next/image';

export default function HowToConvertSection() {
  const t = useTranslations('HomePage.howToConvertSection');

  return (
    <section
      className={cn(
        'flex items-center justify-center rounded-2xl bg-[#FBFCFD] px-[212px] py-20',
        'desktop:px-[100px]',
        'laptop:px-10',
        'tablet:px-0 tablet:py-10',
        'final:px-0 final:py-10',
      )}
    >
      <div
        className={cn(
          'flex w-full items-center justify-between',
          'tablet:flex-col tablet:justify-center tablet:gap-8',
          'final:flex-col final:justify-center final:gap-8',
        )}
      >
        <div
          className={cn(
            'flex flex-col items-start gap-6',
            'tablet:items-center tablet:gap-3',
            'final:items-center final:gap-3',
          )}
        >
          <h2
            className={cn(
              'font-onest text-[46px] font-medium leading-[50px] tracking-[-0.05px] text-[#1C1C1C]',
              'desktop:text-[36px] desktop:leading-10',
              'tablet:text-[30px] tablet:leading-9',
              'final:text-[30px] final:leading-9',
            )}
          >
            {t.rich('title', {
              br: () => <br />,
            })}
          </h2>
          <p
            className={cn(
              'font-mori text-lg font-normal leading-[26px] tracking-normal text-[#585858]',
              'tablet:text-center tablet:text-[16px] tablet:leading-6',
              'final:text-center final:text-[16px] final:leading-6',
            )}
          >
            {t.rich('description', {
              br: () => <br />,
            })}
          </p>
        </div>

        <Image
          src={'/images/landing-page/how-to-convert-section/PDFily.svg'}
          alt="PDFily Icon"
          width={341}
          height={318}
          className={cn('object-contain', 'final:w-[calc(100%-10px)]')}
        />
      </div>
    </section>
  );
}
