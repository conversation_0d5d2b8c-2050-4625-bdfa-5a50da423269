import { Suspense } from "react";

import { getSupabaseServerClient } from "@pdfily/supabase/server-client";
import { cn } from "@pdfily/ui/utils";

import SideHeader from "@/components/layout/site-header";
import SideFooter from "@/components/layout/site-footer";

async function SiteLayout(props: React.PropsWithChildren) {
  const client = getSupabaseServerClient();
  const { data: { user } } = await client.auth.getUser();

  return (
    <main className="flex min-h-screen flex-col items-center">
      <div className="flex w-full flex-1 flex-col items-center">
        <nav
          className={cn(
            'flex w-full justify-center border-b border-b-[#E7E7E7]',
            'h-[90px]',
            'small:h-[52px]',
            'final:h-[52px]',
          )}
        >
          <div
            className={cn(
              'flex h-full w-full max-w-[1440px] items-center justify-between px-20',
              'mac:px-4',
              'small:px-4',
              'final:px-4',
            )}
          >
            <SideHeader user={user} />
          </div>
        </nav>

        <Suspense fallback={<div>Loading ...</div>}>{props.children}</Suspense>

        <SideFooter />
      </div>
    </main>
  );
}

export default SiteLayout;
