/**
 * Safely stringifies an object to JSON.
 *
 * @param obj - The object to stringify.
 * @param space - Optional number of spaces for formatting (default: none).
 * @returns The JSON string or an empty string on error.
 *
 * @example
 * const json = stringifyObject({ foo: "bar" });
 */
export function stringifyObject<T = unknown>(obj: T, space?: number): string {
  try {
    return JSON.stringify(obj, null, space);
  } catch (error) {
    console.error('❌ Failed to stringify object:', error);
    return '';
  }
}
