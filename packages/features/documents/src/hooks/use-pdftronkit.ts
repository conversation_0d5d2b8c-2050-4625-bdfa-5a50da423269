import { WebViewerInstance } from "@pdftron/webviewer"
import { useEffect } from "react"
import appConfig from "@pdfily/config/app.config"
import {
  UsePDFTronKitResponse,
  UseViewerKitProps,
} from "../types/viewer-kit.types"
import { usePDFViewerBase } from "./use-pdf-viewer-base"

/**
 * Loads PDFTron WebViewer into the container.
 *
 * @param container - HTML div where the viewer will be rendered.
 * @param document - URL to a PDF or Office file to be loaded.
 * @param locale - Optional locale/language.
 * @returns Promise of WebViewerInstance or undefined.
 */
const loadPDFTronKit = async (
  container: HTMLDivElement | null,
  document: string,
  locale: string = "en"
): Promise<WebViewerInstance | undefined> => {
  if (!container || typeof window === "undefined") {
    return undefined
  }

  try {
    // Get origin URL
    const origin = window.location.origin

    // Dynamically import WebViewer
    const module = await import("@pdftron/webviewer")
    const WebViewer = module.default

    // Create instance
    const instance = await WebViewer(
      {
        path: `${origin}/lib/pdftron-webviewer/`,
        initialDoc: document,
        licenseKey: appConfig.pdfViewerLicenseKey,
        fullAPI: true,
        streaming: true, // Activé pour de meilleures performances
        enableRedaction: true,
        enableMeasurement: true,
        css: `${origin}/styles/pdftron/webviewer-custom.css`,
      },
      container
    )

    instance.UI.enableFeatures([instance.UI.Feature.ContentEdit])

    // Set the language of WebViewer UI.
    instance.UI.setLanguage(locale)

    // Get document viewer, annotation manager, and annotations
    const { documentViewer, annotationManager, Annotations } = instance.Core

    instance.UI.addEventListener('toolbarGroupChanged', (toolbarGroup) => {
      if (toolbarGroup.detail === 'toolbarGroup-EditText') {
        // Activate the edition mode of content
        const contentEditManager = documentViewer.getContentEditManager();
        contentEditManager.startContentEditMode();

        // Define the default tool to "EDIT"
        documentViewer.setToolMode(documentViewer.getTool(instance.Core.Tools.ToolNames.EDIT));

        // Force of the "Add Paragraph" tool
        setTimeout(() => {
          const currentTool = documentViewer.getToolMode();
          // check if the currentTool.name is not undefined
          const addParagraphToolname = String(instance.Core.Tools.ToolNames.ADD_PARAGRAPH)
          if (currentTool.name && currentTool.name === addParagraphToolname) {
            documentViewer.setToolMode(documentViewer.getTool(instance.Core.Tools.ToolNames.EDIT));
          }
        }, 100); // 100ms delay for initialization waiting
      }
    });

    // Example: add a rectangle annotation on document load
    documentViewer.addEventListener("documentLoaded", () => {
      // Set zoom to 100% (1 = 100%, 2 = 200%, 0.5 = 50%, etc.)
      documentViewer.zoomTo(1)
    })

    return instance
  } catch (error) {
    console.error(`Error while loading PDFTron WebViewer:`, error);
    throw new Error("Error while loading PDFTron WebViewer")
  }
}

/**
 * Custom hook to initialize and manage PDFTron WebViewer instance. It returns a ref to the viewer container,
 *
 * @param {UseViewerKitProps} options - The options object containing the document URL or ArrayBuffer.
 * @returns {UsePDFTronKitResponse} An object containing containerRef, instance, and loading states.
 *
 * @example
 * const { viewer, loading, instance } = usePDFTronKit({ source: 'path/to/document.pdf', locale: 'en });
 * return <div ref={viewer} style={{ width: '100%', height: '100%' }} />;
 */
const usePDFTronKit = ({
  source,
  locale,
}: UseViewerKitProps): UsePDFTronKitResponse => {
  const { viewer, instance, setInstance, loading, setLoading } =
    usePDFViewerBase<WebViewerInstance | null>()

  useEffect(() => {
    setLoading(true)
    const container = viewer.current

    if (!source || !container) {
      setLoading(false)
      return
    }

    loadPDFTronKit(container, source, locale)
      .then((instance) => {
        if (instance) {
          setInstance(instance)
          console.log("PDFTron WebViewer initialized successfully.")
        }
      })
      .finally(() => setLoading(false))

    return () => {
      // Cleanup: remove WebViewer DOM on unmount
      if (container && container.firstChild) {
        container.removeChild(container.firstChild)
      }
    }
  }, [source])

  return { viewer, instance, loading }
}

export default usePDFTronKit
