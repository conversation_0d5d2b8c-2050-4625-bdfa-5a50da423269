'use client'

import Image from 'next/image'
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react'

import { cn } from '@pdfily/ui/utils'
import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@pdfily/ui/carousel';

const overall = {
  overallRating: 4.5,
  overallReviews: 125,
};

const testimonials = [
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: "<PERSON><PERSON>'s converter is fast, easy, and keeps file quality. Highly recommend!",
    time: '1 day ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: 'PDFily is fast and simple—converting to PDF has never been easier.',
    time: '2 weeks ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: 'The best PDF converter—quick, seamless, and no quality loss!',
    time: '4 weeks ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: "<PERSON>ily's converter is fast, easy, and keeps file quality. Highly recommend!",
    time: '1 day ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: 'PDFily is fast and simple—converting to PDF has never been easier.',
    time: '2 weeks ago',
  },
  {
    name: '<PERSON>',
    verified: true,
    rating: 5,
    text: 'The best PDF converter—quick, seamless, and no quality loss!',
    time: '4 weeks ago',
  },
  {
    name: '<PERSON> <PERSON>',
    verified: true,
    rating: 5,
    text: "PDFily's converter is fast, easy, and keeps file quality. Highly recommend!",
    time: '1 day ago',
  },
  {
    name: '<PERSON> Mozza',
    verified: true,
    rating: 5,
    text: 'PDFily is fast and simple—converting to PDF has never been easier.',
    time: '2 weeks ago',
  },
  {
    name: 'James Dallas',
    verified: true,
    rating: 5,
    text: 'The best PDF converter—quick, seamless, and no quality loss!',
    time: '4 weeks ago',
  },
]

export default function TestimonialsSection({ showTitle = true }: { showTitle?: boolean }) {
  const t = useTranslations('HomePage');

  // Add state to track visible items count
  const [visibleItems, setVisibleItems] = useState(1)

  // Add resize handler to update visible items count
  useEffect(() => {
    const updateVisibleItems = () => {
      const width = window.innerWidth
      if (width >= 1280) {
        // desktop
        setVisibleItems(3)
        setVisibleItems(3)
      } else if (width >= 768) {
        // tablet
        setVisibleItems(2)
      } else {
        // mobile
        setVisibleItems(1)
      }
    }

    updateVisibleItems()
    window.addEventListener('resize', updateVisibleItems)
    return () => window.removeEventListener('resize', updateVisibleItems)
  }, [])

  // Calculate number of pages
  const pageCount = Math.ceil(testimonials.length / visibleItems)

  return (
    <section className="relative flex flex-col items-start">
      {showTitle ? (
        <p
          className={cn(
            'mb-3 font-onest text-lg font-medium leading-[22px] tracking-normal text-[#F0401D]',
            'final:hidden',
          )}
        >
          {t('testimonialsSection.label')}
        </p>
      ) : null}
      <h2
        className={cn(
          'font-onest text-[46px] font-medium leading-[50px] tracking-[-0.05px] text-[#1C1C1C]',
          'desktop:text-[38px] desktop:leading-10',
          'tablet:text-2xl tablet:leading-7',
          'final:text-2xl final:leading-7',
        )}
      >
        {t('testimonialsSection.title')}
      </h2>

      <div className={cn('mt-3 flex items-center gap-3', 'final:mt-2')}>
        <span
          className={cn(
            'font-onest text-[32px] font-medium leading-[56px] tracking-[-0.05px] text-[#F0401D]',
            'tablet:text-[28px] tablet:leading-10',
            'final:text-[28px] final:leading-10',
          )}
        >
          {overall.overallRating}
        </span>
        <div className={cn('flex gap-1', 'tablet:gap-[2px]', 'final:gap-[2px]')}>
          {[1, 2, 3, 4, 5].map((star) => (
            <Image
              key={star}
              src={
                overall.overallRating >= star
                  ? '/images/landing-page/testimonials-section/StarFilled.svg'
                  : overall.overallRating >= star - 0.5
                    ? '/images/landing-page/testimonials-section/StarHalf.svg'
                    : '/images/landing-page/testimonials-section/StarEmpty.svg'
              }
              alt="Star Icon"
              width={18}
              height={18}
              className={cn('object-contain', 'tablet:h-4 tablet:w-4', 'final:h-4 final:w-4')}
            />
          ))}
        </div>
        {t.rich('bannerSection.reviews', {
          reviewtag: (chunks) => (
            <span className="font-onest text-lg font-normal leading-[20px] tracking-normal text-[#585858]">
              {chunks}
            </span>
          ),
          counttag: (chunks) => <span className="font-medium text-[#1C1C1C]">{chunks}</span>,
          count: overall.overallReviews,
        })}
      </div>

      {/* Carousel */}
      <Carousel
        opts={{
          align: 'start',
          loop: false,
          containScroll: 'trimSnaps',
        }}
        className="relative mt-5 w-full"
      >
        <CarouselContent className={cn('-ml-5 pb-5', 'final:-ml-2')}>
          {testimonials.map((testimonial, index) => (
            <CarouselItem key={index} className={cn('pl-5', 'final:pl-2')}>
              <div
                className={cn(
                  'h-[212px] w-[413px] rounded-2xl border border-[#E7E7E7] bg-white p-6 shadow-[2px_8px_14px_0px_#6881B114]',
                  'flex flex-col items-stretch',
                  'tablet:h-[188px] tablet:w-[343px] tablet:p-5',
                  'final:h-[188px] final:w-[343px] final:p-5',
                )}
              >
                <div className="flex items-center justify-between">
                  <h3
                    className={cn(
                      'font-onest text-xl font-medium leading-[24px] tracking-normal text-black',
                      'tablet:text-lg tablet:leading-[22px]',
                      'final:text-lg final:leading-[22px]',
                    )}
                  >
                    {testimonial.name}
                  </h3>
                  <div className={cn('flex gap-1', 'tablet:gap-[2px]', 'final:gap-[2px]')}>
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Image
                        key={star}
                        src={
                          testimonial.rating >= star
                            ? '/images/landing-page/testimonials-section/StarFilled.svg'
                            : testimonial.rating >= star - 0.5
                              ? '/images/landing-page/testimonials-section/StarHalf.svg'
                              : '/images/landing-page/testimonials-section/StarEmpty.svg'
                        }
                        alt="Star Icon"
                        width={18}
                        height={18}
                        className={cn('object-contain', 'tablet:h-4 tablet:w-4', 'final:h-4 final:w-4')}
                      />
                    ))}
                  </div>
                </div>

                <div className={cn('mt-2 flex items-center gap-2', 'tablet:mt-1.5', 'final:mt-1.5')}>
                  <Image
                    src={
                      testimonial.verified
                        ? '/images/landing-page/testimonials-section/Verified.svg'
                        : '/images/landing-page/testimonials-section/Verified.svg'
                    }
                    alt="Verify Badge Icon"
                    width={20}
                    height={20}
                    className={cn('object-contain', 'tablet:h-[18px] tablet:w-[18px]', 'final:h-[18px] final:w-[18px]')}
                  />
                  <span
                    className={cn(
                      'font-onest text-[16px] font-normal leading-6 tracking-normal text-[#0E2432]',
                      'tablet:text-sm tablet:leading-[18px]',
                      'final:text-sm final:leading-[18px]',
                    )}
                  >
                    {testimonial.verified ? 'Verified Customer' : 'Unverified..'}
                  </span>
                </div>

                <div className={cn('mt-5 flex flex-1 flex-col justify-between', 'tablet:mt-4', 'final:mt-4')}>
                  <p
                    className={cn(
                      'font-onest text-lg font-normal leading-[26px] tracking-normal text-[#585858]',
                      'tablet:text-[16px] tablet:leading-6',
                      'final:text-[16px] final:leading-6',
                    )}
                  >
                    {testimonial.text}
                  </p>
                  <p
                    className={cn(
                      'text-right font-onest text-[16px] font-normal leading-5 tracking-normal text-[#585858]',
                      'tablet:text-sm tablet:leading-[18px]',
                      'final:text-sm final:leading-[18px]',
                    )}
                  >
                    {testimonial.time}
                  </p>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>

        <CarouselDots
          count={pageCount}
          className={cn('mt-[26px] gap-[7px]', 'tablet:mt-1 tablet:gap-[5.8px]', 'final:mt-1 final:gap-[5.8px]')}
        />

        <div
          className={cn(
            'absolute -top-[88px] right-0 flex -translate-y-full items-center gap-3',
            'tablet:hidden',
            'final:hidden',
          )}
        >
          <CarouselPrevious className="h-[50px] w-[50px] rounded-full border-[1.5px] shadow-[2px_8px_14px_0px_#6881B114]" />
          <CarouselNext className="h-[50px] w-[50px] rounded-full border-[1.5px] shadow-[2px_8px_14px_0px_#6881B114]" />
        </div>
      </Carousel>
    </section>
  );
}
