'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { cn } from '@pdfily/ui/utils';
import { Link } from '@/lib/i18n/navigation';

/**
 * ContactButton - A reusable component for the "Contact Us" link.
 *
 * @example
 * <ContactButton />
 */
const ContactButton = () => {
  const t = useTranslations('Header');

  return (
    <Link href="/contact-us" className={cn('text-[#1C1C1C]', 'small:hidden', 'final:hidden')}>
      {t('contactUsButton')}
    </Link>
  )
}

export default ContactButton
