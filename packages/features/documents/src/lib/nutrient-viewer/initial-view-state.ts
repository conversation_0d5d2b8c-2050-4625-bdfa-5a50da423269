import NutrientViewer from '@nutrient-sdk/viewer';

/**
 * Default initial view state for PSPDFKit/NutrientViewer.
 * Ensures thumbnails are shown by default when a document loads.
 *
 * @param PSPDFKit - The PSPDFKit or NutrientViewer reference.
 * @returns Initialized ViewState object.
 */
export const getInitialViewState = (PSPDFKit: typeof NutrientViewer) => {
  return new PSPDFKit.ViewState({
    // sidebarMode: PSPDFKit.SidebarMode.THUMBNAILS, // ✅ Open thumbnails by default
    scrollMode: PSPDFKit.ScrollMode.CONTINUOUS,
    sidebarWidth: 240,
    allowPrinting: true,
    zoom: {
      zoomMode: PSPDFKit.ZoomMode.AUTO,
    },
  });
};
