import { z } from 'zod';
import { isEnvTrue } from '@pdfily/shared/utils';
import AuthConfigSchema from './schemas/auth-config.schema';

export type AuthConfig = z.infer<typeof AuthConfigSchema>;

const authConfig: AuthConfig = AuthConfigSchema.parse({
  captchaEnabled: isEnvTrue(process.env.NEXT_PUBLIC_CAPTCHA_ENABLED),
  captchaTokenSiteKey: process.env.NEXT_PUBLIC_CAPTCHA_SITE_KEY,
  googleAuthEnabled: isEnvTrue(process.env.NEXT_PUBLIC_GOOGLE_AUTH_ENABLED),
});

export default authConfig;
