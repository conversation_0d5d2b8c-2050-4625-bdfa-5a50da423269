'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

/**
 * PopupCallback component handles the OAuth callback for popup authentication.
 * 
 * This component:
 * 1. Extracts the authorization code from URL parameters
 * 2. Sends the code to the parent window via BroadcastChannel
 * 3. Closes the popup window
 * 
 * If no code is present, it closes the popup immediately.
 */
export default function PopupCallback() {
  const [mounted, setMounted] = useState(false);
  const searchParams = useSearchParams();

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const code = searchParams.get('code');
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    // Send result to parent window via BroadcastChannel
    const channel = new BroadcastChannel('oauth-popup-channel');
    
    if (code) {
      // Success: send the authorization code
      channel.postMessage({ authResultCode: code });
    } else {
      // Error: send error information
      const errorMessage = error 
        ? `${error}: ${errorDescription || 'Authentication failed'}`
        : 'No authorization code received';
      channel.postMessage({ error: errorMessage });
    }

    // Close the popup window
    window.close();
  }, [mounted, searchParams]);

  // Don't render anything during SSR
  if (!mounted) {
    return null;
  }

  // Show loading state while processing
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Processing authentication...</p>
      </div>
    </div>
  );
}
