import type { User } from '@supabase/supabase-js';
import { UI, WebViewerInstance } from '@pdftron/webviewer';
import { useMemo } from 'react';
import Swal from 'sweetalert2';
import {
  UploadResponse,
  UploadSuccessResponse,
  useUploadFileByDocument,
} from '@pdfily/supabase/hooks/use-upload-file-by-document';
import { useDownloadLogic } from '../../use-check-subscription-and-download';

/**
 * Hook to memoize and return a custom save/download toolbar button.
 *
 * @param instance - Apryse viewer instance.
 * @param documentId - The ID of the document being edited.
 * @param uploadFile - A function to upload the exported PDF.
 * @returns Memoized custom save button for Apryse Viewer toolbar.
 */
export function useCustomDownloadButton(
  instance: WebViewerInstance | null,
  source: string | null,
  documentId: string | null | undefined,
  user: User,
): UI.Components.CustomButton | null {
  const { handleDownload } = useDownloadLogic(user);

  /**
   * Edit and save file to API (memoized)
   */
  const uploadFile = useUploadFileByDocument({
    onSuccess: () => {},
    onError: (error) => {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error?.message || 'Failed to edit and save document.',
      });
    },
  });

  return useMemo(() => {
    if (!instance) return null;

    const downloadButton = new instance.UI.Components.CustomButton({
      title: 'Download',
      // label: "Download",
      img: '/images/apryse/download.svg',
      onClick: async () => {
        if (!instance || !source || !documentId) return;

        try {
          const { Core } = instance;
          const docViewer = Core.documentViewer;
          const contentEditManager = docViewer.getContentEditManager();
          const annotationManager = Core.annotationManager;

          if (contentEditManager.isInContentEditMode()) {
            // get the Id of the content box that is currently selected
            const annotation = annotationManager.getSelectedAnnotations()[0];
            const contentBoxId = annotation?.getCustomData('contentEditBoxId');

            // get the content box by Id and stop editing
            if (contentBoxId) {
              const box = contentEditManager.getContentBoxById(contentBoxId);
              box.stopContentEditing();
            }
            // Wait for all editing unsaved content to be saved to the worker
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }

          const xfdfString = await annotationManager.exportAnnotations();
          const doc = docViewer.getDocument();

          // Get the edited file
          const arrayBuffer = await doc.getFileData({
            downloadType: 'pdf',
            xfdfString,
            includeAnnotations: !!xfdfString,
          });

          const blob = new Blob([arrayBuffer], { type: 'application/pdf' });

          const res: UploadResponse = await uploadFile(blob, documentId);
          if ('error' in res) throw 'error';

          // Download file
          await handleDownload(documentId, res as UploadSuccessResponse);
        } catch (error) {
          console.log(error);
          Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Failed to upload edited PDF.',
          });
        }
      },
      dataElement: 'customButton-download',
    });
    // custom style
    downloadButton.setStyle({
      color: '#F0401D',
      border: '1px solid #F0401D',
      borderRadius: '8px',
    });
    return downloadButton;
  }, [instance, source, documentId, uploadFile]);
}
