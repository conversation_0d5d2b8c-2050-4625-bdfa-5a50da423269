'use client';

import React, { useState, useEffect } from 'react';
import { PaymentElement, useStripe, useElements, Elements } from '@stripe/react-stripe-js';
import { loadStripe, StripeElementsOptions } from '@stripe/stripe-js';
import { Button } from '@pdfily/ui/button';
import { cn } from '@pdfily/ui/utils';
import { User } from '@supabase/supabase-js';
import { StripeProvider } from '../../lib/providers/stripe-provider';
import { PaymentFormProps } from '../../types/payment-types';

interface UpdatePaymentMethodFormProps {
    user?: User | null;
    stripeProvider: StripeProvider;
    clientSecret: string;
    onSuccess?: (paymentMethod: any) => void;
    onError?: (error?: Error) => void;
}

const UpdatePaymentMethodForm: React.FC<UpdatePaymentMethodFormProps> = ({
    user,
    stripeProvider,
    clientSecret,
    onSuccess,
    onError,
}) => {
    const stripe = useStripe();
    const elements = useElements();
    const [ isLoading, setIsLoading ] = useState(false);
    const [ errorMessage, setErrorMessage ] = useState('');

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!stripe || !elements || !user || !clientSecret) {
            setErrorMessage('Incomplete form or user not connected');
            onError?.(new Error('Incomplete form'));
            return;
        }

        setIsLoading(true);
        setErrorMessage('');

        try {
            const { error, setupIntent } = await stripe.confirmSetup({
                elements,
                redirect: 'if_required',
            });

            if (error) {
                setErrorMessage(error.message || 'Error on payment method updating');
                // onError?.(new Error(error.message || 'Update failed'));
                setIsLoading(false);
                return;
            }

            if (setupIntent?.status === 'succeeded' && setupIntent.payment_method) {
                const updatedMethod = await stripeProvider.updateCustomerPaymentMethod(
                    setupIntent.payment_method as string,
                    user
                );
                onSuccess?.(updatedMethod);
            }
        } catch (error) {
            setErrorMessage('An error occured, please try again');
            onError?.(error instanceof Error ? error : new Error('An error occured'));
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        onError?.();
    };

    return (
        <form id="update-payment-method-form" onSubmit={handleSubmit} className="w-full pt-5">
            <PaymentElement
                id="payment-element"
                options={{
                    terms: { card: 'never', applePay: 'never' },
                }}
            />
            {errorMessage && <div className="text-sm text-red-600 my-2">{errorMessage}</div>}
            <div className="flex items-center justify-between gap-3 mt-6">
                <Button
                    className={cn(
                        'mflex h-12 w-full items-center justify-center gap-1.5 rounded-[10px] bg-[#F0401D] hover:bg-[#d83e15]',
                        'font-onest text-[16px] font-medium leading-5 text-white',
                        'desktop:h-12',
                        'final:h-12'
                    )}
                    type="submit"
                    disabled={!stripe || !elements || isLoading}
                >
                    {isLoading ? 'Updating' : 'Update'}
                </Button>
                <Button
                    variant={'outline'}
                    type='button'
                    className={cn(
                        'flex h-12 w-[111px] items-center justify-center rounded-[10px] border border-[#E7E7E7]',
                        'font-onest text-sm font-medium leading-[18px] text-[#1C1C1C]',
                    )}
                    onClick={handleCancel}
                >
                    Cancel
                </Button>
            </div>
        </form>
    );
};

interface UpdatePaymentMethodWrapperProps extends Partial<PaymentFormProps> {
    user?: User | null;
    stripeProvider: StripeProvider;
    stripePublicKey: string;
}

const UpdatePaymentMethodWrapper = ({
    user,
    stripeProvider,
    stripePublicKey,
    onError,
    onSuccess
}: UpdatePaymentMethodWrapperProps) => {
    const [ stripePromise, setStripePromise ] = useState<Promise<any> | null>(null);
    const [ clientSecret, setClientSecret ] = useState<string | null>(null);
    const [ error, setError ] = useState<string | null>(null);

    useEffect(() => {
        if (stripePublicKey) {
            setStripePromise(loadStripe(stripePublicKey));
        }
        async function fetchSetupIntent() {
            if (user) {
                try {
                    const setupIntent = await stripeProvider.createSetupIntent(user);
                    setClientSecret(setupIntent.clientSecret);
                } catch (err) {
                    setError('Erreur lors de la création du Setup Intent');
                    console.error(err);
                }
            } else {
                setError('Utilisateur ou client Stripe non trouvé');
            }
        }
        fetchSetupIntent();
    }, [ stripePublicKey, user, stripeProvider ]);

    if (error) {
        return <div className="text-center py-4 text-red-600">{error}</div>;
    }

    if (!clientSecret || !stripePromise) {
        return <div className="text-center py-4"></div>;
    }

    const options: StripeElementsOptions = {
        clientSecret,
        appearance: {
            theme: 'stripe',
        },
    };

    return (
        <Elements stripe={stripePromise} options={options}>
            <UpdatePaymentMethodForm
                user={user}
                stripeProvider={stripeProvider}
                clientSecret={clientSecret}
                onSuccess={onSuccess}
                onError={onError}
            />
        </Elements>
    );
};

export default UpdatePaymentMethodWrapper;