'use client';

import Link from 'next/link';
import { useEffect, useMemo, useState, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { useCaptchaWrapperContext } from '@pdfily/auth/captcha/client';
import authConfig from '@pdfily/config/auth.config';
import { If } from '@pdfily/ui/if';
import { Input } from '@pdfily/ui/input';
import { Label } from '@pdfily/ui/label';
import { cn } from '@pdfily/ui/utils';
import { signUpAction } from '@/app/actions/sign-up';
import { FormMessage, Message } from '@/components/form-message';
import { SubmitButton } from '@/components/submit-button';
import { PasswordInput } from '@/components/auth/password-input';
import { PasswordRequirements, TPasswordRequirements } from '@/components/auth/password-requirements';

export default function SignUpForm({ searchParams }: { searchParams: Message }) {
  const t = useTranslations('Auth.signUp');
  const { captchaToken, refreshCaptchaToken } = useCaptchaWrapperContext();
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [hasError, setHasError] = useState(false)
  const hasRefreshedTokenRef = useRef(false)

  const [requirements, setRequirements] = useState<TPasswordRequirements>({
    minLength: false,
    hasNumber: false,
    hasUpperLower: false,
    hasSpecial: false,
  });

  const metRequirements = useMemo<boolean>(() => {
    return Object.values(requirements).every(Boolean);
  }, [requirements]);

  useEffect(() => {
    const minLength = password.length >= 8;
    const hasNumber = /\d/.test(password);
    const hasUpper = /[A-Z]/.test(password);
    const hasLower = /[a-z]/.test(password);
    const hasSpecial = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password);

    setRequirements({
      minLength,
      hasNumber,
      hasUpperLower: hasUpper && hasLower,
      hasSpecial,
    });
  }, [password]);

  useEffect(() => {
    const hasFormError = 'error' in searchParams;
    setHasError(hasFormError);

    // Automatically refresh captcha token when there's an error (loads once, not repeatedly)
    if (hasFormError && authConfig.captchaEnabled && !hasRefreshedTokenRef.current) {
      refreshCaptchaToken();
      hasRefreshedTokenRef.current = true;
    }

    // Reset refresh flag when error is cleared
    if (!hasFormError) {
      hasRefreshedTokenRef.current = false;
    }
  }, [searchParams]);

  return (
    <form className="flex flex-col">
      <div className={cn('mb-5 space-y-2', 'final:mb-4')}>
        <Label htmlFor="email" className="text-sm font-medium leading-5 text-[#1C1C1C]">
          {t('form.emailLabel')}
        </Label>
        <Input
          name="email"
          type="email"
          placeholder={t('form.emailPlaceholder')}
          className={cn(
            'h-[50px] w-full rounded-[10px] border border-[#E7E7E7] px-4 py-[15px]',
            'focus-visible:ring-0',
            'font-onest text-[16px] font-normal leading-5 text-[#1C1C1C] placeholder:text-[#585858]',
            'final:h-12',
          )}
          required
          onChange={(e) => setEmail(e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="password" className="text-sm font-medium leading-5 text-[#1C1C1C]">
          {t('form.passwordLabel')}
        </Label>
        <PasswordInput
          name="password"
          placeholder={t('form.passwordPlaceholder')}
          required
          onChange={(v) => setPassword(v)}
        />
        <PasswordRequirements requirements={requirements} />
      </div>

      {false && <FormMessage message={searchParams} />}

      <If condition={authConfig.captchaEnabled}>
        {/* Captcha Token */}
        <input type="hidden" name="captchaToken" value={captchaToken} />
      </If>

      <SubmitButton
        className={cn(
          'mb-5 mt-[26px] flex h-[50px] w-full items-center justify-center rounded-[10px] bg-[#F0401D] transition-colors hover:bg-[#F0401D]/70 disabled:opacity-50',
          'font-onest text-[16px] font-medium leading-5 text-white',
          'final:mb-4',
        )}
        pendingText={t('form.submitLoading')}
        formAction={signUpAction}
        disabled={!email || !password || !metRequirements}
      >
        {t('form.submitButton')}
      </SubmitButton>

      <p
        className={cn(
          'text-center font-onest text-sm font-normal leading-5 text-[#585858]',
          'final:text-xs final:leading-[18px]',
        )}
      >
        {t.rich('disclaimer', {
          br: () => <br />,
          TermLink: (chunks) => (
            <Link href="/terms-conditions" className="underline">
              {chunks}
            </Link>
          ),
          PrivacyLink: (chunks) => (
            <Link href="/privacy-policy" className="underline">
              {chunks}
            </Link>
          ),
        })}
      </p>
    </form>
  );
}
