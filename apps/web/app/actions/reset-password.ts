"use server";

import pathsConfig from "@pdfily/config/paths.config";
import { encodedRedirect } from "@pdfily/shared/utils";
import { getSupabaseServerClient } from "@pdfily/supabase/server-client";

/**
 * Updates the user's password through Supabase authentication.
 *
 * @param {FormData} formData - The form data containing the new password and confirmation password.
 */
export const resetPasswordAction = async (formData: FormData) => {
  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    return encodedRedirect(
      "error",
      pathsConfig.auth.resetPassword,
      "Password and confirm password are required",
    );
  }

  if (password !== confirmPassword) {
    return encodedRedirect(
      "error",
      pathsConfig.auth.resetPassword,
      "Passwords do not match",
    );
  }

  const client = getSupabaseServerClient();

  const { error } = await client.auth.updateUser({
    password: password,
  });

  if (error) {
    encodedRedirect(
      "error",
      pathsConfig.auth.resetPassword,
      "Password update failed",
    );
  }

  return encodedRedirect("success", "/reset-password", "Password updated");
};