'use client';

import { User } from '@supabase/supabase-js';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import pathsConfig from '@pdfily/config/paths.config';
import { Button } from '@pdfily/ui/button';
import { Input } from '@pdfily/ui/input';
import { cn } from '@pdfily/ui/utils';

interface AccountFormProps {
  user?: User | null;
}

export default function AccountForm({ user }: AccountFormProps) {
  const t = useTranslations('Settings.account');

  const [formData, setFormData] = useState({
    name: '',
    email: user?.email ?? '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Reset form
    setFormData({ name: '', email: '', message: '' });
    setIsSubmitting(false);
  };

  return (
    <div
      className={cn(
        'flex w-full max-w-[1060px] flex-col rounded-3xl bg-white p-8 shadow-[2px_8px_14px_0px_#6881B114]',
        'desktop:mx-4 desktop:p-4',
        'final:p-4',
      )}
    >
      <h3
        className={cn(
          'mb-3 font-onest text-[32px] font-medium leading-9 tracking-[-0.05px] text-[#1C1C1C]',
          'desktop:mb-2 desktop:text-xl desktop:leading-6',
          'final:mb-2 final:text-xl final:leading-6',
        )}
      >
        {t('title')}
      </h3>
      <p
        className={cn(
          'mb-6 font-onest text-lg font-normal leading-[22px] text-[#585858]',
          'desktop:mb-5 desktop:text-sm desktop:leading-[19px]',
          'final:mb-5 final:text-sm final:leading-[19px]',
        )}
      >
        {t('keepInfoUpdated')}
      </p>

      <div className={cn('mb-7 w-[527px]', 'desktop:mb-6 desktop:w-full', 'final:mb-6 final:w-full')}>
        <label htmlFor="email" className="mb-2 font-onest text-sm font-medium leading-5 text-[#1C1C1C]">
          {t('form.emailLabel')}
        </label>
        <Input
          id="email"
          name="email"
          type="email"
          placeholder={t('form.emailPlaceholder')}
          value={formData.email}
          onChange={handleChange}
          required
          className={cn(
            'h-[50px] w-full rounded-[10px] border border-[#E7E7E7] p-4 font-onest text-[16px] font-normal leading-5 text-[#1C1C1C] placeholder:text-[#585858]',
            'desktop:h-12',
            'final:h-12',
          )}
        />
      </div>

      <Button
        type="submit"
        disabled={isSubmitting}
        className={cn(
          'flex h-[50px] w-[98px] items-center justify-center rounded-[10px] bg-[#F0401D] font-onest text-[16px] font-medium leading-5 text-white hover:bg-[#d83e15]',
          'desktop:h-12 desktop:w-full',
          'final:h-12 final:w-full',
        )}
      >
        {isSubmitting ? t('form.saving') : t('form.save')}
      </Button>

      <div className="mt-5">
        <Link
          href={pathsConfig.auth.resetPassword}
          className="text-[16px] font-medium leading-5 text-[#F0401D] underline"
        >
          {t('resetPassword')}
        </Link>
      </div>
    </div>
  );
}
