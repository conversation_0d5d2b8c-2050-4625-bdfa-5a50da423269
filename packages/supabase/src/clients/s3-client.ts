import { S3Client } from '@aws-sdk/client-s3';

import s3Config from '@pdfily/config/s3-config';

let s3Client: S3Client | null = null;

/**
 * Initializes and returns a memoized S3 Client for Supabase Storage.
 * Ensures a single S3Client instance is reused globally (singleton pattern).
 *
 * @returns {S3Client} A globally cached S3Client instance.
 * @throws {Error} If AWS credentials are missing or an error occurs during initialization.
 */
export function getSupabaseS3Client(): S3Client {
  if (!s3Client) {
    // Extract S3 configuration
    const { accessKeyId, secretAccessKey, region = 'us-east-1', endpointUrl } = s3Config;

    if (!accessKeyId || !secretAccessKey) {
      console.warn('⚠️ Warning: AWS credentials are missing. Ensure Supabase S3 is configured correctly.');
      throw new Error("Can't retrieve S3 client: Missing AWS credentials");
    }

    if (!endpointUrl) {
      console.warn('⚠️ Warning: `endpointUrl` is missing. Ensure Supabase S3 is configured correctly.');
    }

    try {
      s3Client = new S3Client({
        credentials: { accessKeyId, secretAccessKey },
        region,
        endpoint: endpointUrl,
        forcePathStyle: true, // Required for Supabase S3-compatible services
      });
    } catch (error) {
      console.error('❌ Error initializing Supabase S3 client:', error);
      throw new Error('Failed to initialize Supabase S3 client', { cause: error });
    }
  }

  return s3Client;
}
