'use client';

import Script from 'next/script';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import appConfig, { PDF_EDITOR } from '@pdfily/config/app.config';

export default function ScriptLoader() {
  const searchParams = useSearchParams();
  const editorParam = searchParams.get('editor');
  const [viewerType, setViewerType] = useState<PDF_EDITOR | null>(null);

  useEffect(() => {
    const isClient = typeof window !== 'undefined';
    if (isClient) {
      const storedEditor = localStorage.getItem('selectedEditor');
      const selectedViewerType = Object.values(PDF_EDITOR).includes(editorParam as PDF_EDITOR)
        ? (editorParam as PDF_EDITOR)
        : Object.values(PDF_EDITOR).includes(storedEditor as PDF_EDITOR)
          ? (storedEditor as PDF_EDITOR)
          : appConfig.pdfEditor;
      setViewerType(selectedViewerType as PDF_EDITOR);
    }
  }, [editorParam]);

  if (viewerType !== PDF_EDITOR.NUTRIENT) return null;

  return (
    <Script
      strategy="afterInteractive"
      src="/lib/nutrient-viewer/nutrient-viewer.js"
      async
      type="text/javascript"
      onLoad={() => console.log('Nutrient viewer script loaded')}
    />
  );
}
