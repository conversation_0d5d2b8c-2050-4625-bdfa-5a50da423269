import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { RequestPresigningArguments } from '@smithy/types';
import { getSupabaseS3Client } from '../../clients/s3-client';

/**
 * Generates a presigned URL for securely uploading a file to Supabase S3-compatible storage.
 *
 * This URL allows temporary, secure access to a private file using HTTP GET.
 *
 * @param {string} bucket - The name of the bucket containing the file.
 * @param {string} fileKey - The path of the file inside the bucket.
 * @param {RequestPresigningArguments} [options] - Optional configuration for expiration (defaults to 3600s).
 * @returns {Promise<string | null>} - The generated signed URL, or `null` if an error occurs.
 *
 * @example
 * const url = await getPresignedUploadUrl('uploads', 'documents/uuid.pdf');
 * await axios.put(url, file, { headers: { 'Content-Type': file.type } });
 */
export async function getPresignedUploadUrl(
  bucket: string,
  fileKey: string,
  options: RequestPresigningArguments = { expiresIn: 3600 },
): Promise<string | null> {
  if (!bucket || !fileKey) {
    console.error('❌ Bucket and file key are required for generating a signed upload URL.');
    throw new Error('Bucket and file key are required.');
  }

  try {
    const s3Client: S3Client = getSupabaseS3Client();
    const command = new PutObjectCommand({ Bucket: bucket, Key: fileKey });
    const signedUrl = await getSignedUrl(s3Client, command, options);
    return signedUrl;
  } catch (error) {
    console.error(`❌ Failed to generate signed upload URL for "${fileKey}":`, error);
    return null;
  }
}
