import { getTranslations } from 'next-intl/server';

import pathsConfig from '@pdfily/config/paths.config';
import { cn } from '@pdfily/ui/utils';

import { Message } from '@/components/form-message';
import SignInForm from '@/components/auth/sign-in-form';
import GoogleAuthButton from '@/components/auth/google-auth-button';
import { Link } from '@/lib/i18n/navigation';

export default async function SignIn(props: { searchParams: Promise<Message> }) {
  const t = await getTranslations('Auth.signIn');
  const searchParams = await props.searchParams;

  return (
    <>
      <h1
        className={cn(
          'mb-5 text-center font-onest text-[32px] font-medium leading-9 tracking-[-1px] text-[#1C1C1C]',
          'final:mb-4 final:text-[26px] final:leading-[30px]',
        )}
      >
        {t('title')}
      </h1>
      <p
        className={cn(
          'mb-8 text-center font-onest text-[16px] font-normal leading-[22px] text-[#585858]',
          'final:mb-6',
        )}
      >
        {t.rich('goToSignup', {
          Link: (chunks) => (
            <Link className="font-medium text-[#F0401D] hover:underline" href={pathsConfig.auth.signUp}>
              {chunks}
            </Link>
          ),
        })}
      </p>

      <GoogleAuthButton className="rounded-full border-gray-300" path={pathsConfig.app.home} />

      <SignInForm searchParams={searchParams} />
    </>
  );
}
