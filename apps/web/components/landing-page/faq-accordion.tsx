'use client'

import { cn } from '@pdfily/ui/utils'
import Image from 'next/image'
import type React from 'react'
import { useState } from 'react'

interface AccordionItemProps {
  title: string
  children: React.ReactNode
  defaultOpen?: boolean
}

export const AccordionItem: React.FC<AccordionItemProps> = ({ title, children, defaultOpen = false }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen)

  return (
    <div
      className={cn(
        'flex flex-col gap-6 border-b border-[#E8E8E8]',
        isOpen ? 'mb-[26px]' : '',
        'tablet:mb-0 tablet:gap-[10px]',
        'final:mb-0 final:gap-[10px]',
      )}
    >
      <button
        className="flex w-full items-start justify-between"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <h3
          className={cn(
            'text-left font-onest text-2xl font-medium leading-7 tracking-normal text-[#1C1C1C]',
            'laptop:text-lg laptop:leading-6',
            'tablet:text-lg tablet:leading-6',
            'final:text-lg final:leading-6',
          )}
        >
          {title}
        </h3>
        <Image
          src={isOpen ? '/images/landing-page/faq-section/Collapse.svg' : '/images/landing-page/faq-section/Expand.svg'}
          alt={isOpen ? 'Collapse' : 'Expand'}
          width={28}
          height={28}
          className={cn('object-contain', 'tablet:h-6 tablet:w-6', 'final:h-6 final:w-6')}
        />
      </button>
      <div
        className={cn(
          'font-onest text-lg font-normal leading-[26px] tracking-normal text-[#585858]',
          'overflow-hidden transition-all duration-300 ease-in-out',
          isOpen ? 'max-h-auto pb-5 opacity-100' : 'max-h-0 opacity-0',
          'laptop:text-sm laptop:leading-5',
          'tablet:text-sm tablet:leading-5',
          'final:text-sm final:leading-5',
        )}
      >
        {children}
      </div>
    </div>
  )
}

interface AccordionProps {
  children: React.ReactNode
  className?: string
}

export const Accordion: React.FC<AccordionProps> = ({ children, className = '' }) => {
  return <div className={`${className}`}>{children}</div>
}
