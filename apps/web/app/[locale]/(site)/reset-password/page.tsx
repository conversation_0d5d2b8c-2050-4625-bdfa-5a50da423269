import Link from 'next/link';
import Image from 'next/image';
import { redirect } from 'next/navigation';
import { getTranslations } from 'next-intl/server';

import pathsConfig from '@pdfily/config/paths.config';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';
import { cn } from '@pdfily/ui/utils';
import { Button } from '@pdfily/ui/button';

import { Message } from '@/components/form-message';
import ResetPasswordForm from '@/components/auth/reset-password-form';

export default async function ResetPassword(props: { searchParams: Promise<Message> }) {
  const t = await getTranslations('Auth.resetPassword');
  const client = getSupabaseServerClient();
  const { data: { user } } = await client.auth.getUser();

  if (!user || user.is_anonymous) {
    return redirect(pathsConfig.auth.signIn);
  }

  const { email } = user;
  const searchParams = await props.searchParams;

  if ('success' in searchParams) {
    return (
      <div className="w-full">
        <div className={cn('flex w-full justify-center px-4 py-10', 'final:py-[60px]')}>
          <div className="flex w-full max-w-[414px] flex-col">
            <div className="flex flex-col">
              <div className={cn('mb-9 flex justify-center', 'final:mb-6')}>
                <Image
                  src={'/images/auth/CheckXL.svg'}
                  alt={'check'}
                  width={106}
                  height={106}
                  className={cn('object-contain', 'final:h-[72px] final:w-[72px]')}
                />
              </div>

              <h1
                className={cn(
                  'mb-5 text-center font-onest text-[32px] font-medium leading-9 tracking-[-1px] text-[#1C1C1C]',
                  'final:mb-4 final:text-[26px] final:leading-[30px]',
                )}
              >
                {t('success.title')}
              </h1>
              <p className={'text-center font-onest text-[16px] font-normal leading-[22px] text-[#585858]'}>
                {t('success.subtitle')}
              </p>

              <Link href={pathsConfig.auth.signIn} className="mt-[26px] pb-[200px]">
                <Button className="flex h-[50px] w-full items-center justify-center rounded-[10px] bg-[#F0401D] text-[16px] font-medium leading-5 text-white transition-colors hover:bg-[#F0401D]/70">
                  {t('success.signinLink')}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className={cn('flex w-full justify-center px-4 py-10', 'final:py-5')}>
        <div className="flex w-full max-w-[414px] flex-col">
          <h1
            className={cn(
              'mb-5 text-center font-onest text-[32px] font-medium leading-9 tracking-[-1px] text-[#1C1C1C]',
              'final:mb-4 final:text-[26px] final:leading-[30px]',
            )}
          >
            {t('title')}
          </h1>
          <p
            className={cn(
              'mb-8 text-center font-onest text-[16px] font-normal leading-[22px] text-[#585858]',
              'final:mb-6',
            )}
          >
            {t('description')}
            <br />
            {email || ''}
          </p>

          <ResetPasswordForm searchParams={searchParams} />
        </div>
      </div>
    </div>
  );
}
