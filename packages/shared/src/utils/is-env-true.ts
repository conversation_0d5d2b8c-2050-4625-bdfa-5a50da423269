/**
 * Checks if an environment variable is explicitly set to the string "true" (case-insensitive).
 *
 * @param value - The environment variable value to check.
 * @returns `true` if the key is a non-null, non-undefined string equal to "true" (ignoring case and surrounding spaces), otherwise `false`.
 *
 * @example
 * isEnvTrue("true"); // true
 * isEnvTrue(" TRUE "); // true
 * isEnvTrue("false"); // false
 * isEnvTrue(undefined); // false
 */
export function isEnvTrue(value?: string | null): boolean {
  return typeof value === 'string' && value.trim().toLowerCase() === 'true';
}
