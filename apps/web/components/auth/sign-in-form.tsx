'use client';

import Link from 'next/link';
import { useEffect, useState, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { useCaptchaWrapperContext } from '@pdfily/auth/captcha/client';
import authConfig from '@pdfily/config/auth.config';
import pathsConfig from '@pdfily/config/paths.config';
import { If } from '@pdfily/ui/if';
import { Input } from '@pdfily/ui/input';
import { Label } from '@pdfily/ui/label';
import { cn } from '@pdfily/ui/utils';
import { signInAction } from '@/app/actions/sign-in';
import { FormMessage, Message } from '@/components/form-message';
import { SubmitButton } from '@/components/submit-button';
import { PasswordInput } from '@/components/auth/password-input';

export default function SignInForm({ searchParams }: { searchParams: Message }) {
  const t = useTranslations('Auth.signIn');
  const { captchaToken, refreshCaptchaToken } = useCaptchaWrapperContext();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [hasError, setHasError] = useState(false)
  const hasRefreshedTokenRef = useRef(false);

  useEffect(() => {
    const hasFormError = 'error' in searchParams;
    setHasError(hasFormError);

    // Automatically refresh captcha token when there's an error (loads once, not repeatedly)
    if (hasFormError && authConfig.captchaEnabled && !hasRefreshedTokenRef.current) {
      refreshCaptchaToken();
      hasRefreshedTokenRef.current = true;
    }

    // Reset refresh flag when error is cleared
    if (!hasFormError) {
      hasRefreshedTokenRef.current = false;
    }
  }, [searchParams]);

  return (
    <form className="flex flex-col">
      <div className={cn('mb-5 space-y-2', 'final:mb-4')}>
        <Label htmlFor="email" className="text-sm font-medium leading-5 text-[#1C1C1C]">
          {t('form.emailLabel')}
        </Label>
        <Input
          name="email"
          type="email"
          placeholder={t('form.emailPlaceholder')}
          className={cn(
            'h-[50px] w-full rounded-[10px] border border-[#E7E7E7] px-4 py-[15px]',
            'focus-visible:ring-0',
            'font-onest text-[16px] font-normal leading-5 text-[#1C1C1C] placeholder:text-[#585858]',
            hasError ? 'border-[#F53D67] bg-[#FFF2F5]' : '',
            'final:h-12',
          )}
          required
          onChange={(e) => setEmail(e.target.value)}
        />
      </div>

      <div className={cn('mb-4 space-y-2', 'final:mb-3')}>
        <Label htmlFor="password" className="text-sm font-medium leading-5 text-[#1C1C1C]">
          {t('form.passwordLabel')}
        </Label>
        <PasswordInput
          name="password"
          placeholder={t('form.passwordPlaceholder')}
          required
          onChange={(v) => setPassword(v)}
          className={hasError ? 'border-[#F53D67] bg-[#FFF2F5]' : ''}
        />
      </div>

      <If condition={authConfig.captchaEnabled}>
        {/* Captcha Token */}
        <input type="hidden" name="captchaToken" value={captchaToken} />
      </If>

      <div>
        <Link
          href={pathsConfig.auth.forgotPassword}
          className="text-[16px] font-medium leading-5 text-[#F0401D] hover:underline"
        >
          {t('form.forgotPassword')}
        </Link>
      </div>

      <FormMessage message={searchParams} />

      <SubmitButton
        className={cn(
          'mb-5 mt-[26px] flex h-[50px] w-full items-center justify-center rounded-[10px] bg-[#F0401D] transition-colors hover:bg-[#F0401D]/70 disabled:opacity-50',
          'font-onest text-[16px] font-medium leading-5 text-white',
          'final:mb-4',
        )}
        pendingText="Signing In..."
        formAction={signInAction}
        disabled={!email || !password}
      >
        {t('form.submitButton')}
      </SubmitButton>

      <p
        className={cn(
          'text-center font-onest text-sm font-normal leading-5 text-[#585858]',
          'final:text-xs final:leading-[18px]',
        )}
      >
        {t.rich('disclaimer', {
          br: () => <br />,
          TermLink: (chunks) => (
            <Link href="/terms-conditions" className="underline">
              {chunks}
            </Link>
          ),
          PrivacyLink: (chunks) => (
            <Link href="/privacy-policy" className="underline">
              {chunks}
            </Link>
          ),
        })}
      </p>
    </form>
  );
}
