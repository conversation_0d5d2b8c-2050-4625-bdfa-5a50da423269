import { notFound } from 'next/navigation';
import { Locale, hasLocale, NextIntlClientProvider } from 'next-intl';
import { setRequestLocale } from 'next-intl/server';
import { ReactNode } from 'react';
import PaymentLayout from '@/components/layout/payment-layout';
import { PostHogProvider } from '@/components/layout/PostHogProvider';
import { routing } from '@/lib/i18n/routing';

type LocaleLayoutProps = {
  children: ReactNode;
  params: Promise<{ locale: Locale }>;
};

/**
 * Generates static parameters for the page.
 *
 * @returns
 */
export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

/**
 * LocaleLayout - A layout component that sets the locale for the page.
 *
 * @param param0
 * @returns
 */
export default async function LocaleLayout({ children, params }: LocaleLayoutProps) {
  // Ensure that the incoming `locale` is valid
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <div className={'flex h-full flex-col'}>
      <PostHogProvider>
        <NextIntlClientProvider locale={locale}>
          <PaymentLayout>{children}</PaymentLayout>
        </NextIntlClientProvider>
      </PostHogProvider>
    </div>
  );
}
