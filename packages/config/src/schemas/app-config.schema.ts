import { z } from 'zod';

/**
 * Defines the shape of your app's config and validates
 * environment variables or other input data accordingly.
 */
export const AppConfigSchema = z.object({
  title: z
    .string({
      required_error: 'Please provide NEXT_PUBLIC_SITE_TITLE',
      description: 'Default title tag for your SaaS.',
    })
    .trim()
    .min(1, { message: 'Title must not be empty' }),

  description: z
    .string({
      required_error: 'Please provide NEXT_PUBLIC_SITE_DESCRIPTION',
      description: 'Default description for your SaaS.',
    })
    .trim()
    .min(1, { message: 'Description must not be empty' }),

  url: z
    .string({
      required_error: 'Please provide NEXT_PUBLIC_SITE_URL',
      description: 'Default URL for your SaaS.',
    })
    .trim()
    .url('Please provide a valid URL for NEXT_PUBLIC_SITE_URL'),

  theme: z.enum(['light', 'dark', 'system']).optional(),
  lang: z.enum(['en', 'fr', 'de', 'es', 'it', 'pt', 'ru', 'zh', 'ja']).optional(),

  // PDF Editor Configuration
  pdfEditor: z.enum(['pdftron', 'nutrient']).optional(),
  pdfViewerLicenseKey: z
    .string({
      description: 'License key for Apryse PDF viewer libraries',
    })
    .optional(),
  nutrientLicenceKey: z.string({ description: 'Licence key for Nutrient library' }).optional(),
  isNutrientLicensedEnabled: z.boolean({ description: 'Checks if the Nutrient licensed is activated' }),
});

export default AppConfigSchema;
