import { NextRequest, NextResponse } from 'next/server';
import s3Config from '@pdfily/config/s3-config';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';
import { getSupabaseServerAdminClient } from '@pdfily/supabase/server-admin-client';
import { generatePreviewToken, validatePreviewToken } from '@/lib/jwt/preview-token';
import { ErrorResponse } from '../download/route';

interface TokenResponse {
  token: string;
  expiresAt: number;
}

interface TokenErrorResponse {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

/**
 * Streams a file for preview from the Supabase storage bucket.
 *
 * @name GET /api/v1/files/[id]/preview
 * @param {NextRequest} _request - The incoming HTTP request.
 * @param {Object} param1 - Object containing route parameters.
 * @returns {Promise<NextResponse>} - The server response streaming the file content or an error message.
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
): Promise<NextResponse> {
  try {
    const { id: documentId } = await params;

    if (!documentId) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'Bad Request',
          message: 'Document ID is required but was not provided.',
          statusCode: 400,
          timestamp: new Date().toISOString(),
        },
        { status: 400 },
      );
    }

    // Verify and validate the token
    const authHeader = _request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'Access denied',
          message: 'Missing or invalid authorization token',
          statusCode: 403,
          timestamp: new Date().toISOString(),
        },
        { status: 403 },
      );
    }

    const token = authHeader.substring(7); // Exclude "Bearer "
    const tokenPayload = validatePreviewToken(token);

    if (!tokenPayload) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'Access denied',
          message: 'Invalid or expired token',
          statusCode: 403,
          timestamp: new Date().toISOString(),
        },
        { status: 403 },
      );
    }

    // Check if the token match with the requested document
    if (tokenPayload.documentId !== documentId) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'Access denied',
          message: 'Token does not match requested document',
          statusCode: 403,
          timestamp: new Date().toISOString(),
        },
        { status: 403 },
      );
    }

    // Again check authentication for more security
    const supabaseClient = getSupabaseServerClient();
    const {
      data: { user },
    } = await supabaseClient.auth.getUser();

    if (!user || user.id !== tokenPayload.userId) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'Unauthorized',
          message: 'Invalid user session',
          statusCode: 401,
          timestamp: new Date().toISOString(),
        },
        { status: 401 },
      );
    }

    // Fetch document details from Supabase
    const supabase = getSupabaseServerAdminClient();
    const { data: document, error: docError } = await supabase
      .from('documents')
      .select('edited_key, original_key')
      .eq('id', documentId)
      .eq('user_id', user.id)
      .single();

    if (docError || !document) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'Document Not Found',
          message: `The requested document does not exist or may have been deleted. ${docError?.message || 'Unknown error'}`,
          statusCode: 404,
          timestamp: new Date().toISOString(),
        },
        { status: 404 },
      );
    }

    const bucket = s3Config.bucket ?? 'Uploads';
    const filePath = document.edited_key ?? document.original_key;

    // Generate a signed URL for server-side use (valid for 60 seconds)
    const { data: signedUrlData, error: signedUrlError } = await supabase.storage
      .from(bucket)
      .createSignedUrl(filePath, 60, { download: false });

    if (signedUrlError || !signedUrlData?.signedUrl) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'Internal Server Error',
          message: `Failed to generate signed URL for preview. ${signedUrlError?.message || 'Unknown error'}`,
          statusCode: 500,
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      );
    }

    // Fetch the file content using the signed URL
    const response = await fetch(signedUrlData.signedUrl);
    if (!response.ok) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'Internal Server Error',
          message: 'Failed to fetch the file from storage.',
          statusCode: 500,
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      );
    }

    // Get the file stream
    const fileStream = response.body;
    if (!fileStream) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'Internal Server Error',
          message: 'Failed to retrieve file stream.',
          statusCode: 500,
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      );
    }

    // Set headers to prevent downloading and enable streaming
    const headers = new Headers();
    headers.set('Content-Type', 'application/pdf');
    headers.set('Content-Disposition', 'inline'); // Ensure inline display
    headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
    headers.set('X-Content-Type-Options', 'nosniff'); // Prevent MIME-type sniffing

    return new NextResponse(fileStream, {
      status: 200,
      headers,
    });
  } catch (error) {
    return NextResponse.json<ErrorResponse>(
      {
        error: 'Internal Server Error',
        message: 'Failed to retrieve the preview file.',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * Generates an access token for preview file
 * @name POST /api/v1/files/[id]/preview
 * @param {NextRequest} _request - The incoming HTTP request.
 * @param {Object} param1 - Object containing route parameters.
 * @return {*}  {(Promise<NextResponse<TokenResponse | TokenErrorResponse>>)}
 */
export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
): Promise<NextResponse<TokenResponse | TokenErrorResponse>> {
  try {
    const { id: documentId } = await params;

    if (!documentId) {
      return NextResponse.json<TokenErrorResponse>(
        {
          error: 'Bad Request',
          message: 'Document ID is required',
          statusCode: 400,
          timestamp: new Date().toISOString(),
        },
        { status: 400 },
      );
    }

    // Verify the user's auth status
    const supabaseClient = getSupabaseServerClient();
    const {
      data: { user },
    } = await supabaseClient.auth.getUser();

    if (!user) {
      return NextResponse.json<TokenErrorResponse>(
        {
          error: 'Unauthorized',
          message: 'You must be signed in to access this document',
          statusCode: 401,
          timestamp: new Date().toISOString(),
        },
        { status: 401 },
      );
    }

    // Verify if the user has access to the document
    const supabase = getSupabaseServerAdminClient();
    const { data: document, error: docError } = await supabase
      .from('documents')
      .select('id')
      .eq('id', documentId)
      .eq('user_id', user.id)
      .single();

    if (docError || !document) {
      return NextResponse.json<TokenErrorResponse>(
        {
          error: 'Document Not Found',
          message: 'The requested document does not exist or you do not have access to it',
          statusCode: 404,
          timestamp: new Date().toISOString(),
        },
        { status: 404 },
      );
    }

    // Generate the token
    const token = generatePreviewToken(documentId, user.id);
    const expiresAt = Date.now() + 30000;

    return NextResponse.json<TokenResponse>(
      {
        token,
        expiresAt,
      },
      { status: 200 },
    );
  } catch (error) {
    return NextResponse.json<TokenErrorResponse>(
      {
        error: 'Document Not Found',
        message: 'Failed to retrieve the preview document',
        statusCode: 404,
        timestamp: new Date().toISOString(),
      },
      { status: 404 },
    );
  }
}
