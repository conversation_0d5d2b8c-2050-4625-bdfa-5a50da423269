import { useTranslations } from 'next-intl';
import Image from 'next/image';

import { cn } from '@pdfily/ui/utils';
import { Link } from '@/lib/i18n/navigation';

const items = [
  {
    name: 'wordToPdf',
    title: 'Word to PDF',
    icon: '/images/landing-page/tools-section/Word2PDF.svg',
  },
  {
    name: 'pdfToWord',
    title: 'PDF to Word',
    icon: '/images/landing-page/tools-section/PDF2Word.svg',
  },
  {
    name: 'excelToPdf',
    title: 'Excel to PDF',
    icon: '/images/landing-page/tools-section/Excel2PDF.svg',
  },
  {
    name: 'pdfToExcel',
    title: 'PDF to Excel',
    icon: '/images/landing-page/tools-section/PDF2Excel.svg',
  },
  {
    name: 'powerPointToPdf',
    title: 'PowerPoint to PDF',
    icon: '/images/landing-page/tools-section/PowerPoint2PDF.svg',
  },
  {
    name: 'pdfToPowerPoint',
    title: 'PDF to PowerPoint',
    icon: '/images/landing-page/tools-section/PDF2PowerPoint.svg',
  },
  {
    name: 'jpgToPdf',
    title: 'JPG to PDF',
    icon: '/images/landing-page/tools-section/JPG2PDF.svg',
  },
  {
    name: 'pdfToJpg',
    title: 'PDF to JPG',
    icon: '/images/landing-page/tools-section/PDF2JPG.svg',
  },
  {
    name: 'edit',
    title: 'Edit PDF',
    icon: '/images/landing-page/tools-section/EditPDF.svg',
  },
  {
    name: 'split',
    title: 'Split PDFs',
    icon: '/images/landing-page/tools-section/SplitPDFs.svg',
  },
  {
    name: 'merge',
    title: 'Merge PDFs',
    icon: '/images/landing-page/tools-section/MergePDFs.svg',
  },
  {
    name: 'compress',
    title: 'Compress PDF',
    icon: '/images/landing-page/tools-section/CompressPDF.svg',
  },
  {
    name: 'protect',
    title: 'Protect PDF',
    icon: '/images/landing-page/tools-section/ProtectPDF.svg',
  },
  {
    name: 'sign',
    title: 'Sign PDF',
    icon: '/images/landing-page/tools-section/SignPDF.svg',
  },
  {
    name: 'watermark',
    title: 'Add Watermark',
    icon: '/images/landing-page/tools-section/AddWatermark.svg',
  },
  {
    name: 'rotate',
    title: 'Rotate PDF',
    icon: '/images/landing-page/tools-section/RotatePDF.svg',
  },
];

export default function ToolsSection({ IsMembersAreaPage = false }: { IsMembersAreaPage?: boolean }) {
  const t = useTranslations('HomePage.toolsSection');
  return (
    <section
      className={cn(
        'flex flex-col items-center',
        IsMembersAreaPage ? 'gap-8' : 'gap-[46px]',
        IsMembersAreaPage ? '' : 'desktop:gap-[26px]',
        IsMembersAreaPage ? '' : 'final:gap-[26px]',
      )}
    >
      <h2
        className={cn(
          'font-onest text-[46px] font-medium leading-[50px] tracking-[-0.05px] text-[#1C1C1C]',
          'desktop:text-[30px] desktop:leading-[34px]',
        )}
      >
        {IsMembersAreaPage ? t('memberAreaTitle') : t('commonTitle')}
      </h2>

      <div
        className={cn(
          'grid grid-cols-4 justify-items-center gap-x-5 gap-y-5',
          'desktop:grid-cols-3',
          'laptop:grid-cols-4 laptop:gap-x-[11px] laptop:gap-y-3',
          'tablet:grid-cols-3',
          'small:grid-cols-2',
          'final:grid-cols-2 final:gap-x-[11px] final:gap-y-3',
        )}
      >
        {items.map((item, index) => (
          <Link
            href="#"
            key={index}
            className={cn(
              'shadow-[1px_1px_1px_1px_#8DA0BC14]',
              'shadow-[2px_8px_14px_0px_#6881B114]',
              'flex flex-col items-center rounded-2xl border border-[#E7E7E7] bg-[#FAFAFA]',
              'transition-shadow hover:shadow-md',
              IsMembersAreaPage ? 'px-4 py-7' : 'p-8',
              IsMembersAreaPage ? 'h-[158px] w-[250px] gap-[22px]' : 'h-[180px] w-[305px] gap-[26px]',
              'laptop:h-[112px] laptop:w-[166px] laptop:gap-4 laptop:px-3 laptop:py-5',
              'final:h-[112px] final:w-[166px] final:gap-4 final:px-3 final:py-5',
            )}
          >
            <Image
              src={item.icon}
              alt="Tool Icon"
              width={64}
              height={64}
              className={cn(
                'object-contain',
                IsMembersAreaPage ? 'h-14 w-14' : '',
                'laptop:h-[38px] laptop:w-[38px]',
                'laptop:w-[38px] final:h-[38px]',
              )}
            />
            <span
              className={cn(
                'font-onest font-medium tracking-normal text-[#1C1C1C]',
                IsMembersAreaPage ? 'text-xl leading-6' : 'text-[22px] leading-[26px]',
                'laptop:text-sm laptop:leading-[18px]',
                'final:text-sm final:leading-[18px]',
              )}
            >
              {t(`tools.${item.name}`)}
            </span>
          </Link>
        ))}
      </div>
    </section>
  );
}
