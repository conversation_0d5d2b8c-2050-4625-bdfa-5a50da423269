import { S3Client, DeleteObjectCommand, DeleteObjectCommandOutput } from '@aws-sdk/client-s3';
import { getSupabaseS3Client } from '../../clients/s3-client';

/**
 * Deletes a file from an S3 bucket.
 *
 * @function deleteFromS3
 * @param {string} bucket - The name of the S3 bucket from which to delete the object.
 * @param {string} fileKey - The key (path) of the object to delete from the bucket.
 * @returns {Promise<DeleteObjectCommandOutput | undefined>} A promise that resolves to the output of the S3 delete operation or undefined if an error occurs.
 *
 * @example
 * const result = await deleteFromS3("my-bucket", "documents/abc123/file.pdf");
 * console.log(result);
 */
export async function deleteFileFromS3(
  bucket: string,
  fileKey: string
): Promise<DeleteObjectCommandOutput | undefined> {
  try {
    // Get the globally cached S3 Client
    const s3Client: S3Client = getSupabaseS3Client();

    // Delete the file from the S3 bucket.
    const data: DeleteObjectCommandOutput = await s3Client.send(
      new DeleteObjectCommand({
        Bucket: bucket, // The name of the bucket from which to delete the object.
        Key: fileKey,   // The key (path) of the object to delete from the bucket.
      })
    );

    return data;
  } catch (error) {
    console.error(`Error deleting file from S3: ${fileKey}`, error);
    return undefined;
  }
}
