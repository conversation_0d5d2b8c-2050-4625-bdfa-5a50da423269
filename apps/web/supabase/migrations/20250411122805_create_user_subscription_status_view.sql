-- Migration to create the extended user_subscription_status view

-- Create a view to show the user's subscription status
create view "public"."user_subscription_status" as
select
  "u"."id" as "user_id",

  -- Subscription status flags
  case
    when "s"."status" = 'active' and "s"."current_period_end" > now() then true
    else false
  end as "has_subscription",

  case
    when "s"."status" = 'trialing' and "s"."current_period_end" > now() then true
    else false
  end as "is_trialing",

  -- Raw subscription fields
  "s"."status",
  "s"."plan" as "plan_tier",
  "s"."provider",
  "s"."current_period_end"

from "auth"."users" as "u"
left join lateral (
  select *
  from "public"."subscriptions" as "s"
  where "s"."user_id" = "u"."id"
  order by "s"."current_period_end" desc
  limit 1
) as "s" on true;