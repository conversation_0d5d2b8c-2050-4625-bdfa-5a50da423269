import { PutObjectCommand } from '@aws-sdk/client-s3';
import { NextRequest, NextResponse } from 'next/server';

import s3Config from '@pdfily/config/s3-config';
import { UpdateDocumentPropertiesSchema } from '@pdfily/config/schemas';
import { getFileType, getPdfPageCount } from '@pdfily/shared/utils';
import { Database } from '@pdfily/supabase/database';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';
import { getSupabaseS3Client } from '@pdfily/supabase/s3-client';
import { deleteFileFromS3 } from '@pdfily/supabase/s3/delete-file';
import { getPresignedDownloadUrl } from '@pdfily/supabase/s3/get-presigned-download-url';

type DocumentUpdate = Database['public']['Tables']['documents']['Update'];

/**
 * Fetches a file from the server.
 *
 * @param {NextRequest} _request - The incoming HTTP request.
 * @param {Object} param1 - Object containing route parameters.
 * @returns {Promise<NextResponse>} - The server response after processing the file update.
 *
 * @example
 * GET /api/v1/files/[id]
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
): Promise<NextResponse> {
  // Directly destructure the ID from the params object
  const { id: documentId } = await params;

  if (!documentId) {
    return NextResponse.json(
      {
        error: 'Bad Request',
        message: 'Document ID is required but was not provided in the request.',
        statusCode: 400,
        timestamp: new Date().toISOString(),
      },
      { status: 400 },
    );
  }

  try {
    const supabase = getSupabaseServerClient(); // Get Supabase DB client
    // Fetch document details from Supabase
    const { data, error } = await supabase.from('documents').select('*').eq('id', documentId).single();

    if (error || !data) {
      return NextResponse.json(
        {
          error: 'Document not found',
          message: `The requested document does not exist or may have been deleted.`,
          statusCode: 404,
          timestamp: new Date().toISOString(),
        },
        { status: 404 },
      );
    }

    // Define bucket name
    const bucket = s3Config.bucket ?? 'uploads'; // Default bucket

    // Generate signed URLs if file keys exist
    const originalFileUrl = data.original_key ? await getPresignedDownloadUrl(bucket, data.original_key) : null;

    const editedFileUrl = data.edited_key ? await getPresignedDownloadUrl(bucket, data.edited_key) : null;

    const responseBody = {
      id: data.id,
      title: data.title,
      name: data?.name,
      size: data?.size,
      page_count: data?.page_count,
      original_key: data.original_key,
      original_file_url: originalFileUrl,
      is_edited: data.is_edited,
      edited_key: data.edited_key,
      edited_file_url: editedFileUrl,
    };

    // Return document details along with signed URLs
    return NextResponse.json(responseBody, { status: 200 });
  } catch (err) {
    const error = err as Error;
    console.error('❌ Error fetching document:', {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'An unexpected error occurred while retrieving the document. Please try again later.',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * Updates an existing document with edited file.
 *
 * @param {NextRequest} _request - The incoming HTTP request.
 * @param {Object} param1 - Object containing route parameters.
 * @returns {Promise<NextResponse>} - The server response after processing the file update.
 *
 * @example
 * PUT /api/v1/files/[id]
 */
export async function PUT(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
): Promise<NextResponse> {
  try {
    // Directly destructure the ID from the params object
    const { id: documentId } = await params;

    const formData = await _request.formData();
    const file = formData.get('file') as File;

    if (!file || !documentId) {
      return NextResponse.json({ error: 'Missing file or documentId' }, { status: 400 });
    }

    const s3Client = getSupabaseS3Client();
    const bucket = s3Config.bucket ?? 'uploads';

    // Fetch existing document metadata
    const supabase = getSupabaseServerClient(); // Get Supabase DB client
    const {
      data: { user },
    } = await supabase.auth.getUser(); // Get user details if authenticated

    const { data: existingDocument, error: fetchError } = await supabase
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .single();

    if (fetchError || !existingDocument) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Generate new file path for the edited document
    const editedFileKey = `documents/${documentId}/${Date.now()}-${file.name}`;

    // Convert File to Buffer for AWS SDK
    const fileArrayBuffer = await file.arrayBuffer();
    const fileBuffer = Buffer.from(fileArrayBuffer);

    // Upload the edited file to S3
    await s3Client.send(
      new PutObjectCommand({
        Bucket: bucket,
        Key: editedFileKey,
        Body: fileBuffer,
        ContentType: file.type,
        CacheControl: '3600',
      }),
    );

    const pageCount = await getPdfPageCount(file); // Extract number of pages
    const fileType = getFileType(file.type); // Extract file type

    // Update document metadata in Supabase DB
    const updatedMetadata: DocumentUpdate = {
      user_id: user?.id,
      edited_key: editedFileKey,
      name: file.name,
      size: file.size,
      type: fileType,
      format: fileType.toUpperCase(),
      page_count: pageCount,
      is_edited: true,
      last_modified: new Date().toISOString(),
    };

    const { error: updateError } = await supabase.from('documents').update(updatedMetadata).eq('id', documentId);

    if (updateError) {
      console.error(`❌ Error updating document metadata in Supabase DB: ${updateError.message}`);

      return NextResponse.json(
        {
          message: 'Failed to update document metadata in Supabase DB',
          error: updateError.message,
          statusCode: 500,
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      );
    }

    // Generate signed URL for accessing the edited file
    const fileUrl = await getPresignedDownloadUrl(bucket, editedFileKey);

    const responseBody = {
      documentId,
      file: {
        file_name: updatedMetadata.name,
        file_key: updatedMetadata.edited_key,
        file_size: updatedMetadata.size,
        file_url: fileUrl,
      },
    };

    return NextResponse.json(responseBody);
  } catch (error) {
    return NextResponse.json(
      {
        message: 'Internal Server Error',
        error: 'Internal Server Error',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * Deletes a file from the "documents" table in Supabase.
 *
 * @param {NextRequest} _request - The incoming HTTP request.
 * @param {Object} param1 - Object containing route parameters.
 * @returns {Promise<NextResponse>} A promise that resolves to a JSON response.
 *
 * @example
 * DELETE /api/v1/files/[id]
 * Response:
 * {
 *   "message": "File has been deleted successfully"
 * }
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
): Promise<NextResponse> {
  try {
    // Directly destructure the ID from the params object
    const { id: documentId } = await params;

    const supabase = getSupabaseServerClient(); // Get Supabase DB client

    // Step 1: Fetch both S3 keys from Supabase
    const { data: fileData, error: fetchError } = await supabase
      .from('documents')
      .select('original_key, edited_key') // Fetch both keys
      .eq('id', documentId)
      .single();

    if (fetchError) {
      return NextResponse.json(
        {
          message: 'Failed to fetch file data from Supabase DB.',
          error: fetchError.message,
          statusCode: 404,
          timestamp: new Date().toISOString(),
        },
        { status: 404 },
      );
    }

    if (!fileData) {
      return NextResponse.json(
        {
          message: 'No file found with the given ID',
          error: `No file found with ID ${documentId}`,
          statusCode: 404,
          timestamp: new Date().toISOString(),
        },
        { status: 404 },
      );
    }

    // Step 2: Delete files from S3 Storage
    try {
      const { original_key, edited_key } = fileData; // Extract S3 keys from the file data
      const bucket = s3Config.bucket ?? 'uploads';

      // Delete both files in parallel using Promise.all
      await Promise.all([
        original_key ? deleteFileFromS3(bucket, original_key) : Promise.resolve(),
        edited_key ? deleteFileFromS3(bucket, edited_key) : Promise.resolve(),
      ]);
    } catch (error) {
      console.error(`❌ S3: Failed to delete one or more files from S3`, error);
    }

    // Step 3: Delete the file record from the Supabase table
    const { error } = await supabase.from('documents').delete().eq('id', documentId);

    if (error) {
      return NextResponse.json(
        {
          message: 'Failed to delete file from Supabase DB',
          error: error.message,
          statusCode: 500,
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      );
    }

    return NextResponse.json(
      {
        message: `File has been deleted successfully`,
        statusCode: 200,
        timestamp: new Date().toISOString(),
      },
      { status: 200 },
    );
  } catch (error) {
    return NextResponse.json(
      {
        message: 'Internal server error while deleting file',
        error: 'Internal server error while deleting file',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

export async function PATCH(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
): Promise<NextResponse> {
  // Directly desctructure the ID from the params object
  const { id: documentId } = await params;
  const body = await _request.json();

  if (!documentId) {
    return NextResponse.json(
      {
        error: 'Bad Request',
        message: 'Document ID is required but was not provided in the request.',
        statusCode: 400,
        timestamp: new Date().toISOString(),
      },
      { status: 400 },
    );
  }

  // Validate the body request with zod
  const validationResult = UpdateDocumentPropertiesSchema.safeParse(body);

  if (!validationResult.success) {
    return NextResponse.json(
      {
        error: 'Bad Request',
        message: 'Invalid input data provided.',
        allowedFields: ['description', 'is_public', 'name', 'title'],
        details: validationResult.error.errors,
        statusCode: 400,
        timestamp: new Date().toISOString(),
      },
      { status: 400 },
    );
  }

  const validatedData = validationResult.data;

  // Check if at least one field is provided to proceed with update
  if (Object.keys(validatedData).length === 0) {
    return NextResponse.json(
      {
        error: 'Bad Request',
        message: 'No fields provided for update.',
        allowedFields: ['description', 'is_public', 'name', 'title'],
        statusCode: 400,
        timestamp: new Date().toISOString(),
      },
      { status: 400 },
    );
  }

  try {
    const supabase = getSupabaseServerClient(); // Get Supabase DB client

    const { data: existingDocument, error: fetchError } = await supabase
      .from('documents')
      .select('*')
      .eq('id', documentId)
      .single();

    if (fetchError || !existingDocument) {
      return NextResponse.json(
        {
          error: 'Document not found',
          message: `The requested document does not exist or may have been deleted.`,
          statusCode: 404,
          timestamp: new Date().toISOString(),
        },
        { status: 404 },
      );
    }

    const { error: updateError } = await supabase.from('documents').update(body).eq('id', documentId);

    if (updateError) {
      console.error(`❌ Error updating document in Supabase DB: ${updateError.message}`);

      return NextResponse.json(
        {
          message: 'Failed to update document in Supabase DB',
          error: updateError.message,
          statusCode: 500,
          timestamp: new Date().toISOString(),
        },
        { status: 500 },
      );
    }
    return NextResponse.json({
      message: 'Document updated',
      error: null,
      statusCode: 200,
    });
  } catch (error) {
    return NextResponse.json(
      {
        message: 'Internal Server Error',
        error: 'Internal Server Error',
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
