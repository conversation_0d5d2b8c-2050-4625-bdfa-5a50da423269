import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@pdfily/supabase/server-admin-client';
import authConfig from '@pdfily/config/auth.config';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';

export async function POST(request: NextRequest) {
  try {
    // Get the data from the request body
    const { email, password, captchaToken } = await request.json();

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseServerAdminClient();

    // Get the Supabase client
    const supabase = getSupabaseServerClient();

    // Get the anonymous user session
    const { data: anonData, error: anonError } = await supabase.auth.getUser();

    if (anonError) {
      return NextResponse.json({ error: anonError.message }, { status: 500 });
    }

    // Retrieve the anonymous user ID
    const anonymousUserId = anonData?.user?.id;

    if (!anonymousUserId) {
      return NextResponse.json({ error: 'No anonymous user found' }, { status: 400 });
    }

    // Update the anonymous user with email and password
    const { data: updatedUser, error: updateError } = await supabaseAdmin.auth.admin.updateUserById(anonymousUserId, {
      email,
      password,
      email_confirm: true,
    });

    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    // Sign in the user
    const { data, error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password,
      ...(authConfig.captchaEnabled && {
        options: { captchaToken },
      }),
    });

    if (signInError) {
      return NextResponse.json({ error: signInError.message }, { status: 500 });
    }

    await supabase.auth.getUser();

    return NextResponse.json(
      { message: 'User updated successfully', user: updatedUser, session: data?.session },
      { status: 200 },
    );
  } catch (error: any) {
    console.error('Error updating anonymous user:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message,
      },
      { status: 500 },
    );
  }
}
