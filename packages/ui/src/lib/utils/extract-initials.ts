/**
 * Extracts initials from a full name or an email.
 *
 * If the input is a full name (e.g., "<PERSON>"), it extracts the first letter of the first and last names.
 * If the input is an email (e.g., "<EMAIL>"), it extracts the first letters from parts before the '@'.
 *
 * @param {string} input - The full name or email of the user.
 * @returns {string} The initials of the first and last name or email.
 *
 * @example
 * extractInitials("<PERSON>"); // "JD"
 * extractInitials("Rahul Rathore"); // "RR"
 * extractInitials("<EMAIL>"); // "JD"
 * extractInitials("<EMAIL>"); // "S"
 */
export function extractInitials(input: string): string {
  if (!input) return '';

  // Check if input contains an '@' symbol (indicating an email)
  const isEmail = input.includes('@');

  // If it's an email, split by '@' and take the local part, or use the input itself as fallback
  const localPart = (isEmail ? input.split('@')[0] : input) || '';
  const nameParts = localPart.trim().split(/[ ._-]+/); // Split by space, dot, underscore, or hyphen

  // Get first and last initials if available, ensuring safe access
  const firstNameInitial = nameParts[0]?.charAt(0).toUpperCase() || '';
  const lastNameInitial = (nameParts[nameParts.length - 1]?.charAt(0).toUpperCase()) ?? '';

  return firstNameInitial + lastNameInitial;
}
