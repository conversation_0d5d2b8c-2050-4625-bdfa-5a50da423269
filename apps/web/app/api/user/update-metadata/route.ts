import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerAdminClient } from '@pdfily/supabase/server-admin-client';

/**
 * API route for updating user metadata
 * This route is intended for internal use only
 */
export async function POST(request: NextRequest) {
  try {
    // Get the data from the request body
    const { userId, metadata } = await request.json();

    if (!userId || !metadata) {
      return NextResponse.json({ error: 'Bad request - Missing required parameters' }, { status: 400 });
    }

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseServerAdminClient();

    // Get the current user data
    const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);

    if (userError || !userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Merge the existing metadata with the new metadata
    const updatedMetadata = {
      ...userData.user.user_metadata,
      ...metadata,
    };

    // Update the user metadata
    const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(userId, {
      user_metadata: updatedMetadata,
    });

    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      userId,
      metadata: updatedMetadata,
    });
  } catch (error: any) {
    console.error('Error updating user metadata:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message,
      },
      { status: 500 },
    );
  }
}
