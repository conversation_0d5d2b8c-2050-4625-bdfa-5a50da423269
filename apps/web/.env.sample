# Site Configuration
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
NEXT_PUBLIC_SITE_TITLE="PDF Online Editor - Pdfily"
NEXT_PUBLIC_SITE_DESCRIPTION="Pdfily offers a powerful, intuitive platform to edit, merge, split, and compress PDFs, making document management simple and efficient."

# NEXT_PUBLIC_PDF_EDITOR: nutrient | pdftron
NEXT_PUBLIC_PDF_EDITOR=pdftron
NEXT_PUBLIC_PDF_VIEWER_LICENSE_KEY=demo:1744178434515:613a66bf0300000000bcd818ddea5f8c8b67eba6686d8c4376ab367e7b
NEXT_PUBLIC_PDF_NUTRIENT_LICENSED_ENABLED=true
NEXT_PUBLIC_PDF_NUTRIENT_LICENSE_KEY='rbSQRrJSvrtB0vyojHKDlV2vtaTXPRQxAAbp_PdK6R9U0vFSMt5cQKi5Td6R2up-Uh1FXUY1r1IvaXfW4U9nLwRztu3QeAE8z4XJX2CtLSJukvlCPclUApUreHS-ZV79K6CGSkIp8YgBbQjQHFuPyCXin68g7zioVRdPmUpsCr260d6Fg7lTeMmMusSGGL-8Kn9-8LwEnOCIiZemiuFMFW8N3ceeo9ZLLoW7cBuC055UOeXvixkHufTFDk4YQ2KqHyXPDAVkviAqJsqd90te9qsWY7i2peo4M3jVMOWN6PmEY8fRBadH5FiEEDqfCGXT2y92ZfIXr76nca_m1E8ntJJvDdw_JRL5PC-oGYwmg3uyxN7PaIua2mhqQEpC8Tvn4AX1OJTcWR5SZUVv64ZFI6D9k8B1VDHCA4C94W4jYogc_3VEVFdkGa5ERRutmvAQq6ei_Twm4GzFbLgqRROaikQ0mpwtVpeW7t5Qkk2drdpszMIaj_L4VVgS5eYFUSmpfHX0QvPGvGjOlZ1RhaisWnniH8xw5d8LIT6mCzTSDs8os_pf7CDVxReu6xpr04vTXpMaLY7_0VlckZeyEnkcRj_6lqgEJKsPAl7VyubYxm0Sjn40rcUtyKOjNVSY11b4bw8s1uGY85QAmK8fWzPYqW6dryclSllVM1BcjfTlP8jiu8zCr5Th1y_Ob-0MG7r4'

# Update these with your Supabase details from your project settings > API
# https://app.supabase.com/project/_/settings/api
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Supabase Project Reference and Database Password
# These variables are used during Supabase project linking and database deployment.
# - SUPABASE_PROJECT_REF: The unique reference ID of your Supabase project.
# - SUPABASE_DB_PASSWORD: The password for the database, required for authentication during deployment.
SUPABASE_PROJECT_REF=
SUPABASE_DB_PASSWORD=

# Captcha Configuration
NEXT_PUBLIC_CAPTCHA_SITE_KEY=
NEXT_PUBLIC_CAPTCHA_ENABLED=true
CAPTCHA_SECRET_KEY=

# S3 Storage Configuration
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_HOST=
S3_REGION="us-east-1"
S3_BUCKET="uploads"

# SMTP Mail Configuration
SMTP_USER=
SMTP_PASS=
SMTP_FROM_EMAIL=<EMAIL>
SMTP_SENDER_NAME=Admin

# Google Auth Configuration
NEXT_PUBLIC_GOOGLE_AUTH_ENABLED=false
GOOGLE_AUTH_CLIENT_ID=
GOOGLE_AUTH_SECRET_KEY=

# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SIGNING_SECRET=
# Stripe Product and Price IDs
NEXT_PUBLIC_STRIPE_SUBSCRIPTION_PRICE_ID=
NEXT_PUBLIC_STRIPE_4_WEEKS_SUBSCRIPTOIN_PRICE_ID=

# Tracking
NEXT_PUBLIC_GOOGLE_TRACKING_ENABLED=
NEXT_PUBLIC_GTM_CONTAINER_ID=

NEXT_PUBLIC_POSTHOG_TRACKING_ENABLED=false
NEXT_PUBLIC_POSTHOG_API_KEY=
NEXT_PUBLIC_POSTHOG_HOST=

# Auth
FILE_PREVIEW_JWT_SECRET=

# Locale Prefix Strategy
# 'never' = No language codes in URLs (e.g., /pricing)
# 'as-needed' = Language codes only for non-default locales (e.g., /pricing, /de/pricing)
# 'always' = Language codes for all locales (e.g., /en/pricing, /de/pricing)
NEXT_PUBLIC_LOCALE_PREFIX=never

# SendGrid
SENDGRID_SUPPORT_RECEIVER_EMAIL=
SENDGRID_CONTACT_TEMPLATE_ID=d-915be223d69c4a83b9eda04822ed7cf3
