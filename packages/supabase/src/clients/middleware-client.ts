import 'server-only';

import { CookieOptions, createServerClient } from '@supabase/ssr';
import { SupabaseClient } from '@supabase/supabase-js';
import { type NextRequest, NextResponse } from 'next/server';
import { Database } from '../database.types';
import { getSupabaseClientKeys } from '../get-supabase-client-keys';

/**
 * Interface representing a cookie with options.
 */
interface CookieObject {
  name: string;
  value: string;
  options: CookieOptions;
}

/**
 * Creates a middleware client for Supabase.
 *
 * @param {NextRequest} request - The incoming Next.js middleware request object
 * @param {NextResponse} response - The Next.js middleware response object to manage cookies
 * @returns Supabase client instance configured for middleware
 */
export function createMiddlewareClient<GenericSchema = Database>(
  request: NextRequest,
  response: NextResponse
): SupabaseClient<GenericSchema> {
  // Get Supabase client keys from environment or secure storage
  const keys = getSupabaseClientKeys();

  const supabaseUrl: string = keys.url; // Supabase project URL
  const supabaseAnonKey: string = keys.anonKey; // Supabase public Anon API key

  // Create a Supabase server client with cookie handling integration
  return createServerClient<GenericSchema>(supabaseUrl, supabaseAnonKey, {
    cookies: {
      /**
       * Get all cookies from the incoming request
       */
      getAll() {
        return request.cookies.getAll();
      },
      /**
       * Set cookies in both request (in-memory) and response (header)
       * @param cookiesToSet Array of cookies to set
       */
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value }) => {
          request.cookies.set(name, value);
        });

        cookiesToSet.forEach(({ name, value, options }: CookieObject) => {
          response.cookies.set(name, value, options);
        });
      },
    },
  });
}
