'use client';

import { useTranslations } from 'next-intl';
import { cn } from '@pdfily/ui/utils';
import { useAuth } from '@/components/session-provider';
import { Link } from '@/lib/i18n/navigation';

interface MyDocumentButtonProps {
  href?: string | null;
  className?: string;
  onClickEvent?: () => void;
}

/**
 * @name MyDocumentButton
 *
 * @description
 * A reusable link component that renders the "My Documents" button only when the user is authenticated.
 *
 * @example
 * <MyDocumentButton />
 */
const MyDocumentButton = ({
  href,
  className = cn('text-[#F0401D]', 'small:hidden', 'final:hidden'),
  onClickEvent
}: MyDocumentButtonProps) => {
  const t = useTranslations('Header');
  const { isAnonymous, isAuthenticated } = useAuth();

  if (!isAuthenticated || isAnonymous) return null;

  return (
    <Link
      href={href ?? '/my-documents'}
      className={className}
      onClick={onClickEvent}
    >
      {t('myDocumentsButton')}
    </Link>
  )
}

export default MyDocumentButton
