'use client';

import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { cn } from '@pdfily/ui/utils';

export type TPasswordRequirements = {
  minLength: boolean;
  hasNumber: boolean;
  hasUpperLower: boolean;
  hasSpecial: boolean;
};

interface IPasswordRequirementsProps {
  requirements: TPasswordRequirements;
}

export function PasswordRequirements({ requirements }: IPasswordRequirementsProps) {
  const t = useTranslations('Auth.signUp.form.passwordRequirements');
  const RequirementItem = ({ met, text, className }: { met: boolean; text: string; className?: string }) => (
    <div className={cn('flex items-start gap-1.5', className)}>
      <Image
        src={met ? '/images/auth/Check.svg' : '/images/auth/X.svg'}
        alt={met ? 'check' : 'x'}
        width={18}
        height={18}
        className={cn('object-contain')}
      />
      <span className={cn('font-onest text-sm font-normal leading-[18px]', met ? 'text-[#0AAA79]' : 'text-[#585858]')}>
        {text}
      </span>
    </div>
  );

  return (
    <div className={cn('flex gap-5 pt-2', 'final:flex-col final:gap-2.5 final:pt-1')}>
      <div className={cn('flex flex-col gap-3', 'final:gap-2.5')}>
        <RequirementItem met={requirements.minLength} text={t('length')} className="w-[210px]" />
        <RequirementItem met={requirements.hasUpperLower} text={t('case')} className="w-[210px]" />
      </div>
      <div className={cn('flex flex-col gap-3', 'final:gap-2.5')}>
        <RequirementItem met={requirements.hasNumber} text={t('numeric')} className="w-[87px]" />
        <RequirementItem met={requirements.hasSpecial} text={t('specialChar')} className="w-[150px]" />
      </div>
    </div>
  );
}
