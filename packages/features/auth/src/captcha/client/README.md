# Captcha Wrapper Context

This module provides a comprehensive captcha management system using Cloudflare Turnstile.

## Components and Hooks

### Current Implementation

- **`CaptchaWrapperContext`** - The React context for captcha state management
- **`CaptchaWrapperProvider`** - The provider component that wraps your app
- **`useCaptchaWrapperContext`** - The hook to access captcha functionality
- **`CaptchaWrapperContextType`** - TypeScript interface for the context
- **`CaptchaTokenSetter`** - Component that renders the Turnstile widget

## Usage

### Basic Setup

```tsx
import { CaptchaWrapperProvider, CaptchaTokenSetter } from '@pdfily/auth/captcha/client';

function App() {
  return (
    <CaptchaWrapperProvider>
      <CaptchaTokenSetter siteKey="your-site-key" />
      <YourComponents />
    </CaptchaWrapperProvider>
  );
}
```

### Using the Hook

```tsx
import { useCaptchaWrapperContext } from '@pdfily/auth/captcha/client';

function MyComponent() {
  const {
    captchaToken,
    refreshCaptchaToken,
    resetCaptchaToken,
    isTokenReady
  } = useCaptchaWrapperContext();

  const handleRefreshToken = () => {
    // Refresh token when needed
    refreshCaptchaToken();
  };

  const handleResetToken = () => {
    // Clear current token
    resetCaptchaToken();
  };

  return (
    <div className="flex gap-2 items-center">
      <button onClick={handleRefreshToken}>
        🔄 Refresh Token
      </button>
      <button onClick={handleResetToken}>
        🗑️ Reset Token
      </button>
      <span className="text-sm">
        Status: {isTokenReady ? '✅ Ready' : '❌ No Token'}
      </span>
    </div>
  );
}
```

## API Reference

### CaptchaWrapperContextType

```typescript
interface CaptchaWrapperContextType {
  token: string;                    // Current captcha token
  setToken: (token: string) => void; // Set the token value
  instance: TurnstileInstance | null; // Turnstile instance reference
  setInstance: (ref: TurnstileInstance) => void; // Set the instance
  resetCaptchaToken: () => void;    // Clear the current token
  refreshCaptchaToken: () => void;  // Get a new token
  isTokenReady: boolean;            // Whether token is available
}
```

### useCaptchaWrapperContext()

Returns an object with:
- `captchaToken: string` - The current token
- `resetCaptchaToken: () => void` - Clears the token
- `refreshCaptchaToken: () => void` - Gets a new token
- `isTokenReady: boolean` - Token availability status

## File Structure

```
packages/features/auth/src/captcha/client/
├── captcha-wrapper-provider.tsx    # Main provider and context
├── use-captcha-wrapper-context.ts  # Hook for accessing context
├── captcha-token-setter.tsx        # Turnstile widget component
├── index.ts                        # Exports
└── README.md                       # This file
```

## Refresh Button Integration

The system provides manual token refresh functionality through refresh buttons:

### Basic Refresh Button

```tsx
import { useCaptchaWrapperContext } from '@pdfily/auth/captcha/client';

function BasicRefreshButton() {
  const { refreshCaptchaToken, isTokenReady } = useCaptchaWrapperContext();

  const handleRefreshToken = () => {
    // Manually refresh token when user clicks refresh button
    refreshCaptchaToken();
  };

  return (
    <div className="flex gap-4 items-center">
      <button
        onClick={handleRefreshToken}
        className="flex items-center gap-2 px-3 py-2 border rounded"
      >
        🔄 Refresh Token
      </button>

      <span className="text-sm">
        Status: {isTokenReady ? '✅ Ready' : '❌ No Token'}
      </span>
    </div>
  );
}
```

### Refresh Button with Reset Functionality

```tsx
function RefreshResetButtons() {
  const {
    refreshCaptchaToken,
    resetCaptchaToken,
    isTokenReady,
    captchaToken
  } = useCaptchaWrapperContext();

  const handleRefresh = () => {
    refreshCaptchaToken();
  };

  const handleReset = () => {
    resetCaptchaToken();
  };

  return (
    <div className="flex gap-2 items-center">
      <button
        onClick={handleRefresh}
        className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        🔄 Refresh
      </button>

      <button
        onClick={handleReset}
        className="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600"
      >
        🗑️ Reset
      </button>

      <div className="text-sm">
        <span className={isTokenReady ? 'text-green-600' : 'text-red-600'}>
          {isTokenReady ? '✅ Token Ready' : '❌ No Token'}
        </span>
        {captchaToken && (
          <div className="text-xs text-gray-500 mt-1">
            Token: {captchaToken.substring(0, 20)}...
          </div>
        )}
      </div>
    </div>
  );
}
```

### Advanced Refresh Button with Loading State

```tsx
import { useState } from 'react';
import { useCaptchaWrapperContext } from '@pdfily/auth/captcha/client';

function AdvancedRefreshButton() {
  const { refreshCaptchaToken, resetCaptchaToken, isTokenReady } = useCaptchaWrapperContext();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);

    try {
      // Refresh the captcha token
      refreshCaptchaToken();

      // Wait for token to be generated
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Failed to refresh captcha token:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleReset = () => {
    resetCaptchaToken();
  };

  return (
    <div className="flex gap-2 items-center">
      <button
        onClick={handleRefresh}
        disabled={isRefreshing}
        className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50 hover:bg-blue-600"
      >
        <span className={`${isRefreshing ? 'animate-spin' : ''}`}>🔄</span>
        {isRefreshing ? 'Refreshing...' : 'Refresh Token'}
      </button>

      <button
        onClick={handleReset}
        disabled={isRefreshing}
        className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded disabled:opacity-50 hover:bg-gray-600"
      >
        🗑️ Reset
      </button>

      <div className="flex items-center gap-2">
        <span className={`text-sm ${isTokenReady ? 'text-green-600' : 'text-red-600'}`}>
          {isTokenReady ? '✅ Token Ready' : '❌ No Token'}
        </span>
      </div>
    </div>
  );
}
```

### Compact Refresh Button

```tsx
function CompactRefreshButton() {
  const { refreshCaptchaToken, isTokenReady } = useCaptchaWrapperContext();

  return (
    <button
      onClick={refreshCaptchaToken}
      className={`p-2 rounded-full ${
        isTokenReady
          ? 'bg-green-100 text-green-600 hover:bg-green-200'
          : 'bg-red-100 text-red-600 hover:bg-red-200'
      }`}
      title={isTokenReady ? 'Refresh Token' : 'Generate Token'}
    >
      🔄
    </button>
  );
}
```

## Best Practices

1. **Always provide visual feedback** - Show token status to users
2. **Handle loading states** - Disable buttons during refresh operations
3. **Provide both refresh and reset** - Give users full control over token lifecycle
4. **Use appropriate styling** - Make buttons clearly identifiable
5. **Add tooltips or labels** - Help users understand button functionality

This provides complete manual control over captcha token management through refresh buttons.
