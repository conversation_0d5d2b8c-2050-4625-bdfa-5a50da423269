# @pdfily/auth

Feature package for authentication in the PDFily monorepo.

## Installation

Using pnpm:

```sh
pnpm add @pdfily/auth
```

Or with npm:

```sh
npm install @pdfily/auth
```

Or with yarn:

```sh
yarn add @pdfily/auth
```

## Usage

Import authentication components or hooks from this package in your other packages:

```js
import { useAuth } from '@pdfily/auth';
```

## Development

- Run `pnpm lint` to lint the code.
- Run `pnpm format` to format the code with Pretti<PERSON>.
- Run `pnpm typecheck` to check types.
