'use client';

import { User } from '@supabase/supabase-js';
import { cn } from '@pdfily/ui/utils';

import AccountForm from './forms/account-form';
import PaymentForm from './forms/payment-form';
import MembershipForm from './forms/membership-form';

interface SettingsContentProps {
  activeTab: string;
  user?: User | null;
}

/**
 * SettingsContent - Renders the content based on the active tab.
 *
 * @param {SettingsContentProps} props - The component props.
 * @param {string} props.activeTab - The currently active tab.
 */
function SettingsContent({ activeTab, user }: SettingsContentProps) {
  return (
    <div className={cn('relative flex w-full justify-center pb-[60px] pt-10', 'final:pb-10 final:pt-5')}>
      <div
        className={cn(
          'absolute',
          'left-1/2 top-1/2 -z-10 h-full w-screen -translate-x-1/2 -translate-y-1/2 bg-[#F9F9F9]',
        )}
      ></div>

      {activeTab === 'account' && <AccountForm user={user} />}
      {activeTab === 'payment' && <PaymentForm user={user} />}
      {activeTab === 'membership' && <MembershipForm user={user} />}
    </div>
  );
}

export default SettingsContent;
