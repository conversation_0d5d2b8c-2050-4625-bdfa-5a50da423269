#!/bin/bash

# ------------------------------------------------------------------------------
# 🛠️  set-supabase-secrets.sh
#
# 📌 Usage:
#   pnpm run supabase:secrets {dev|stage|prod} --project-ref YOUR_PROJECT_REF
#   pnpm run supabase:secrets:dev   --project-ref YOUR_DEV_PROJECT_REF
#   pnpm run supabase:secrets:stage --project-ref YOUR_STAGE_PROJECT_REF
#   pnpm run supabase:secrets:prod  --project-ref YOUR_PROD_PROJECT_REF
#
# 📂 This script reads secrets from:
#   apps/web/supabase/functions/.env.{dev|stage|prod}
#
# 🔐 It sets secrets for Supabase Edge Functions via:
#   supabase secrets set KEY=VALUE --project-ref YOUR_PROJECT_REF
#
# 💡 The environment is passed via script name (e.g., `supabase:secrets:dev`)
#     so you only need to specify the --project-ref argument when running.
#
# Example:
#   pnpm run supabase:secrets:prod -- --project-ref YOUR_PROJECT_REF
# ------------------------------------------------------------------------------

ROOT="$(git rev-parse --show-toplevel)"

# Parse arguments
PROJECT_REF=""
ENV=""

while [[ $# -gt 0 ]]; do
  case "$1" in
    dev|stage|prod)
      ENV="$1"
      shift
      ;;
    --project-ref)
      PROJECT_REF="$2"
      shift 2
      ;;
    *)
      echo "❌ Unknown argument: $1"
      exit 1
      ;;
  esac
done

if [ -z "$ENV" ]; then
  echo "❌ Missing environment. Usage: ./set-supabase-secrets.sh dev|stage|prod [--project-ref your-project-id]"
  exit 1
fi

ENV_FILE="$ROOT/apps/web/supabase/functions/.env.$ENV"

if [ ! -f "$ENV_FILE" ]; then
  echo "❌ Env file not found: $ENV_FILE"
  exit 1
fi

echo "🚀 Setting secrets from $ENV_FILE..."

while IFS= read -r line || [ -n "$line" ]; do
  if [[ "$line" == \#* || -z "$line" ]]; then
    continue
  fi

  KEY=$(echo "$line" | cut -d '=' -f 1)
  VALUE=$(echo "$line" | cut -d '=' -f 2-)

  CMD="supabase secrets set $KEY=$VALUE"
  if [ -n "$PROJECT_REF" ]; then
    CMD="$CMD --project-ref $PROJECT_REF"
  fi

  echo "🔐 Setting $KEY..."
  eval $CMD
done < "$ENV_FILE"

echo "✅ All secrets set for environment: $ENV"
