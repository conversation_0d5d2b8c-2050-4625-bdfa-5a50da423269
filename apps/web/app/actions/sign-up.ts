"use server";

import { headers } from "next/headers";

import authConfig from '@pdfily/config/auth.config';
import pathsConfig from "@pdfily/config/paths.config";
import { getSupabaseServerClient } from "@pdfily/supabase/server-client";
import { encodedRedirect } from "@pdfily/shared/utils";
import { getSupabaseServerAdminClient } from "@pdfily/supabase/server-admin-client";

/**
 * @description Handles user sign-up with email and password using Supabase.
 * Handles conflict resolution by reassigning documents from an anonymous user to the registered user.
 *
 * @param {FormData} formData - The form data containing user credentials (email and password).
 */
export const signUpAction = async (formData: FormData) => {
  // Extract email and password from form data
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  const captchaToken = formData.get("captchaToken") as string;

  const origin = (await headers()).get("origin");

  if (!email || !password) {
    return encodedRedirect(
      "error",
      pathsConfig.auth.signUp,
      "Email and password are required"
    );
  }

  // Get the Supabase client
  const supabase = getSupabaseServerClient();

  // Sign in anonymously and retrieve the session
  const { data: anonData, error: anonError } = await supabase.auth.getUser();

  if (anonError) {
    return encodedRedirect(
      "error",
      pathsConfig.auth.signUp,
      "Failed to retrieve session. Please try again."
    );
  }

  const anonymousUserId = anonData?.user?.id ?? "";

  // Sign up the new user
  const { data: { user: registeredUser }, error: signUpError } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback`,
      ...(authConfig.captchaEnabled && {
        captchaToken
      })
    },
  });

  if (signUpError) {
    return encodedRedirect(
      "error",
      pathsConfig.auth.signUp,
      signUpError.message
    );
  }

  const existingUserId = registeredUser?.id ?? "";

  // Reassign documents from anonymous user to the new registered user
  if (anonymousUserId && existingUserId) {
    try {
      // 5. Reassign documents tied to the anonymous user to the existing user
      const adminSupabase = getSupabaseServerAdminClient();
      // Update the user_id of all documents owned by the anonymous user to the existing user
      await adminSupabase
        .from("documents")
        .update({ user_id: existingUserId })
        .eq("user_id", anonymousUserId);
    } catch (error) {
      console.error("Unexpected error during ownership transfer:", error);
    }
  }

  return encodedRedirect(
    "success",
    pathsConfig.auth.signUp,
    "Thanks for signing up! Please check your email for a verification link."
  );
};
