'use client';

import { useEffect, useMemo, useState } from 'react';
import { useTranslations } from 'next-intl';
import { Label } from '@pdfily/ui/label';
import { resetPasswordAction } from '@/app/actions/reset-password';
import { Message } from '@/components/form-message';
import { SubmitButton } from '@/components/submit-button';
import { PasswordInput } from '@/components/auth/password-input';
import { PasswordRequirements, TPasswordRequirements } from '@/components/auth/password-requirements';

export default function ResetPasswordForm({ searchParams }: { searchParams: Message }) {
  const t = useTranslations('Auth.resetPassword.form');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const [requirements, setRequirements] = useState<TPasswordRequirements>({
    minLength: false,
    hasNumber: false,
    hasUpperLower: false,
    hasSpecial: false,
  });
  const metRequirements = useMemo(
    () => requirements.minLength && requirements.hasNumber && requirements.hasUpperLower && requirements.hasSpecial,
    [requirements],
  );
  useEffect(() => {
    setRequirements({
      minLength: password.length >= 8,
      hasNumber: /\d/.test(password),
      hasUpperLower: /[a-z]/.test(password) && /[A-Z]/.test(password),
      hasSpecial: /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password),
    });
  }, [password]);

  const passwordsMatch = password === confirmPassword && password !== '';

  return (
    <form className="flex flex-col">
      <div className="mb-5 space-y-2">
        <Label htmlFor="password" className="text-sm font-medium leading-5 text-[#1C1C1C]">
          {t('newPasswordLabel')}
        </Label>
        <PasswordInput
          name="password"
          placeholder={t('newPasswordPlaceholder')}
          required
          onChange={(v) => setPassword(v)}
        />
        <PasswordRequirements requirements={requirements} />
      </div>

      <div className="space-y-2">
        <Label htmlFor="confirmPassword" className="text-sm font-medium leading-5 text-[#1C1C1C]">
          {t('confirmNewPasswordLabel')}
        </Label>
        <PasswordInput
          name="confirmPassword"
          placeholder={t('confirmNewPasswordPlaceholder')}
          required
          onChange={(v) => setConfirmPassword(v)}
        />
      </div>

      <SubmitButton
        className="mb-[100px] mt-[26px] flex h-[50px] w-full items-center justify-center rounded-[10px] bg-[#F0401D] text-[16px] font-medium leading-5 text-white transition-colors hover:bg-[#F0401D]/70 disabled:opacity-50"
        pendingText="Resetting ..."
        formAction={resetPasswordAction}
        disabled={!passwordsMatch || !metRequirements}
      >
        {t('submitButton')}
      </SubmitButton>
    </form>
  );
}
