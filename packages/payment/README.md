# Supabase - @pdfily/payment

This package is responsible for managing the payments for subscriptions.

Make sure the app installs the `@pdfily/payment` package before using it.

```json
{
  "name": "my-app",
  "dependencies": {
    "@pdfily/payment": "*"
  }
}
```

# Payment Gateway Integration Architecture for PDFily

## Summary

This document describes the architecture and approach adopted for integrating payment gateways into the PDFily application. The initial implementation focuses on <PERSON><PERSON>, while laying the groundwork for an easy future integration of Solidgate or other payment providers.

## Context

PDFily requires a payment system to allow users to purchase subscription plans before downloading their modified documents. The team wants to start with Stripe as the main payment gateway, but plans to integrate Solidgate in the near future.

### Subscription Model

PDFily offers two types of plans:

1. **7-day trial plans**: These plans start with a 7-day trial period, then automatically convert to a recurring 4-week subscription if the user does not cancel.
2. **Direct 1-month subscription plans**: These plans start directly with a recurring 4-week subscription, without a trial period.

## Design Goals

1. **Gateway abstraction**: Create an abstraction layer that allows changing payment gateways with minimal effort
2. **Consistent interfaces**: Maintain a common interface for different gateway implementations
3. **Separation of concerns**: Isolate payment logic from the rest of the application
4. **Extensibility**: Facilitate the addition of new payment gateways in the future
5. **Consistent user experience**: Maintain a consistent user experience regardless of the gateway used
6. **Subscription management**: Support recurring subscriptions with or without trial periods

## Architecture

The architecture is based on the Adapter pattern with a Factory to create appropriate instances of payment providers. This allows easy switching between different gateway implementations.

### Package Structure

```
packages/payment/
├── src/
│   ├── types/
│   │   └── payment-types.ts         # Common types and interfaces
│   ├── lib/
│   │   ├── providers/
│   │   │   ├── stripe-provider.ts   # Stripe implementation
│   │   │   └── solidgate-provider.ts # Future Solidgate implementation (template)
│   │   ├── payment-provider-factory.ts # Factory to create provider instances
│   │   └── payment-service.ts       # High-level service to use providers
│   └── index.ts                    # Package entry points
```

### Future Frontend Structure

For the frontend, we propose the following structure to be implemented later:

```
apps/web/components/checkout/
├── providers/
│   └── payment-provider.tsx        # React context for payment management
├── payment-forms/
│   ├── payment-form-wrapper.tsx    # Wrapper that selects the appropriate form
│   ├── stripe-payment-form.tsx     # Stripe form
│   └── solidgate-payment-form.tsx  # Future Solidgate form
└── payment-details.tsx             # Main payment component (existing)
```

### Future API Routes Structure

```
apps/web/app/api/payments/
├── create-intent/
│   └── route.ts                    # API to create a payment intent
├── create-customer/
│   └── route.ts                    # API to create a customer
├── create-subscription/
│   └── route.ts                    # API to create a subscription
├── cancel-subscription/
│   └── route.ts                    # API to cancel a subscription
└── webhook/
    └── route.ts                    # API to handle payment webhooks
```

### Database Structure

To store subscription information in Supabase, we plan to extend the `users` table schema:

```sql
ALTER TABLE users ADD COLUMN stripe_customer_id VARCHAR(255);
ALTER TABLE users ADD COLUMN stripe_subscription_id VARCHAR(255);
ALTER TABLE users ADD COLUMN subscription_status VARCHAR(50);
ALTER TABLE users ADD COLUMN subscription_next_billing INTEGER;
ALTER TABLE users ADD COLUMN scheduled_cancel_at INTEGER;
```

## Implementation Approach

### 1. Abstraction Layer

We have created a common `PaymentProviderInterface` interface that defines all the methods necessary for any payment gateway, including subscription management:

```typescript
export interface PaymentProviderInterface {
  // Methods to create and manage payments
  createPaymentIntent(params: CreatePaymentParams): Promise<PaymentIntent>;
  confirmPayment(paymentId: string, paymentMethodId: string): Promise<PaymentIntent>;
  verifyPayment(paymentId: string): Promise<PaymentVerificationResult>;

  // Methods to manage subscriptions
  createCustomer(params: CreateCustomerParams): Promise<CustomerResult>;
  createSubscription(params: CreateSubscriptionParams): Promise<SubscriptionInfo>;
  cancelSubscription(subscriptionId: string, cancelAtPeriodEnd?: boolean): Promise<SubscriptionInfo>;
  updateSubscription(params: UpdateSubscriptionParams): Promise<SubscriptionInfo>;

  // Methods for webhooks
  handleWebhook(payload: any, signature: string): Promise<WebhookResult>;

  // Methods for refunds
  refundPayment(paymentId: string, amount?: number): Promise<any>;

  // Method to get the front-end configurations
  getClientConfig(): ClientConfig;
}
```

### 2. Subscription-related Types

We have defined several types and interfaces to handle subscriptions:

```typescript
export interface SubscriptionInfo {
  id: string;
  customerId: string;
  status: SubscriptionStatus;
  currentPeriodEnd: number;
  cancelAtPeriodEnd: boolean;
  cancelAt?: number | null;
  trialEnd?: number | null;
  priceId: string;
  paymentIntentId?: string;
}

export enum SubscriptionStatus {
  INCOMPLETE = 'incomplete',
  INCOMPLETE_EXPIRED = 'incomplete_expired',
  TRIALING = 'trialing',
  ACTIVE = 'active',
  PAST_DUE = 'past_due',
  CANCELED = 'canceled',
  UNPAID = 'unpaid',
}

export enum SubscriptionPlanType {
  TRIAL = 'trial', // 7-day trial that converts to recurring
  RECURRING = 'recurring', // Direct recurring subscription (1-month)
}
```

### 3. Factory Pattern

A `PaymentProviderFactory` has been implemented to create provider instances according to the specified type:

```typescript
export class PaymentProviderFactory {
  static createProvider(providerType: 'stripe' | 'solidgate', config: PaymentProviderConfig): PaymentProviderInterface {
    switch (providerType) {
      case 'stripe':
        return new StripeProvider(config);
      case 'solidgate':
        // Will be implemented later
        throw new Error('Solidgate provider not implemented yet');
      default:
        throw new Error(`Unsupported payment provider: ${providerType}`);
    }
  }
}
```

### 4. Payment Service

A high-level `PaymentService` uses the appropriate provider and exposes unified methods for payments and subscriptions:

```typescript
export class PaymentService {
  private provider: PaymentProviderInterface;

  constructor(config: PaymentServiceConfig) {
    this.provider = PaymentProviderFactory.createProvider(config.provider, config.config);
  }

  // Methods for payments and subscriptions
  async createPaymentIntent(params: CreatePaymentParams): Promise<PaymentIntent> {
    return this.provider.createPaymentIntent(params);
  }

  async createCustomer(params: CreateCustomerParams): Promise<CustomerResult> {
    return this.provider.createCustomer(params);
  }

  async createSubscription(params: CreateSubscriptionParams): Promise<SubscriptionInfo> {
    return this.provider.createSubscription(params);
  }

  // Other methods delegated to the provider...
}
```

## Configuration and Deployment

### Environment Variables

API keys will be stored in environment variables:

```
# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...

# Solidgate (for the future)
SOLIDGATE_API_KEY=...
SOLIDGATE_SECRET_KEY=...
```

### Active Gateway Configuration

The active gateway will be determined by an environment variable or a configuration parameter:

```typescript
const ACTIVE_PAYMENT_PROVIDER = process.env.ACTIVE_PAYMENT_PROVIDER || 'stripe';
```

## Implementation Plan

The implementation will be done in several phases:

### Phase 1: Architecture Foundation (COMPLETED)

- Definition of common interfaces and types
- Implementation of the Stripe provider with subscription support
- Creation of the factory pattern and payment service
- Template for the future Solidgate provider

### Phase 2: Frontend Integration (TO DO)

- Development of React context for payment and subscription management
- Creation of a form component for Stripe
- Integration with existing UI components

### Phase 3: Backend APIs (TO DO)

- Creation of API routes for creating customers and subscriptions
- Implementation of webhooks for handling subscription events
- Integration with Supabase database for subscription information persistence

### Phase 4: Solidgate (FUTURE)

- Complete implementation of the Solidgate provider
- Creation of a Solidgate-specific form component
- Testing and deployment

## Subscription Flow

1. The user selects a plan on the plan selection page (with or without trial period)
2. The user is redirected to the payment page
3. The backend creates a customer in Stripe (`createCustomer`)
4. The backend creates a payment intent if necessary
5. The frontend displays the appropriate payment form
6. The user enters their payment information
7. The backend creates a subscription (`createSubscription`)
8. If the plan has a trial period, the subscription transitions to 'trialing' status
9. If it's a direct plan, the subscription starts immediately
10. Webhooks handle subscription events (end of trial period, recurring payments, etc.)
11. The file can be downloaded and access is granted to the user

## Storing Subscription Information

Subscription information will be stored in the `users` table of Supabase with the following fields:

- `stripe_customer_id`: Customer ID in Stripe
- `stripe_subscription_id`: Subscription ID in Stripe
- `subscription_status`: Current subscription status (active, trialing, cancelled, etc.)
- `subscription_next_billing`: Unix timestamp of the next renewal
- `scheduled_cancel_at`: Unix timestamp of the scheduled cancellation (if applicable)

## Webhook Processing for Subscriptions

Webhooks will handle the following events:

- `customer.subscription.created`: Creation of a new subscription
- `customer.subscription.updated`: Update of a subscription
- `customer.subscription.deleted`: Cancellation of a subscription
- `customer.subscription.trial_will_end`: Trial period end notification
- `invoice.payment_succeeded`: Successful recurring payment
- `invoice.payment_failed`: Failed recurring payment

## Error Handling and Edge Cases

- **Initial payment failure**: If the initial payment fails, the subscription will not be created
- **Recurring payment failure**: If a recurring payment fails, the subscription will transition to 'past_due' status
- **Subscription cancellation**: Users will be able to cancel their subscription, effective immediately or at the end of the current period
- **Trial end transition**: The transition from the trial period to the paid subscription will be handled automatically

## Conclusion

This architecture enables seamless integration of Stripe with subscription support, while facilitating the future addition of Solidgate. The separation of concerns and abstraction of interfaces ensure maintainable and extensible code.

---

## Installation

Using pnpm:

```sh
pnpm add @pdfily/payment
```

Or with npm:

```sh
npm install @pdfily/payment
```

Or with yarn:

```sh
yarn add @pdfily/payment
```

## Usage Example

Import payment hooks or components from this package in your other packages:

```js
import { usePayment } from '@pdfily/payment/hooks';
```

## Development

- Run `pnpm lint` to lint the code.
- Run `pnpm format` to format the code with Prettier.
- Run `pnpm typecheck` to check types.
