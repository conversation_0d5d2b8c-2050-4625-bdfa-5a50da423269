# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
build
.swc/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Ignore all .env files
.env
.env.*

# But don't ignore .env.sample
!.env.sample

# turbo
.turbo

# ui
dist/

# Cache File
/.cache
/**/.cache
