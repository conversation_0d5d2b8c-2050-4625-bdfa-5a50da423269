import { NextRequest, NextResponse } from 'next/server';

import s3Config from '@pdfily/config/s3-config';
import { Database } from '@pdfily/supabase/database';
import { getSupabaseServerClient } from '@pdfily/supabase/server-client';
import { getSupabaseServerAdminClient } from '@pdfily/supabase/server-admin-client';
import { sanitizeFileName } from '@pdfily/shared/utils';

type Document = Database['public']['Tables']['documents']['Row'];

/**
 * Represents a structured error response object.
 *
 * @interface ErrorResponse
 */
export interface ErrorResponse {
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

/**
 * Downloads a file from the Supabase storage bucket.
 *
 * @name GET /api/v1/files/[id]/download
 *
 * @param {NextRequest} _request - The incoming HTTP request.
 * @param {Object} param1 - Object containing route parameters.
 * @returns {Promise<NextResponse>} - The server response containing the file or an error message.
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
): Promise<NextResponse> {
  const { id: documentId } = await params;

  if (!documentId) {
    return NextResponse.json<ErrorResponse>(
      {
        error: 'Bad Request',
        message: 'Document ID is required but was not provided.',
        statusCode: 400,
        timestamp: new Date().toISOString(),
      },
      { status: 400 },
    );
  }

  // Create a new Supabase client
  const supabase = getSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json<ErrorResponse>(
      {
        error: 'Unauthorized',
        message: 'You must be signed in to download this document.',
        statusCode: 401,
        timestamp: new Date().toISOString(),
      },
      { status: 401 },
    );
  }

  // ✅ Check user's subscription status
  const { data: subscription } = await supabase.rpc('has_subscription_status', { user_id: user.id });
  const subscriptionData = subscription?.[0];
  const hasSubscription = (subscriptionData?.has_subscription || user.user_metadata.is_subscribed) ?? false;

  if (!hasSubscription) {
    return NextResponse.json<ErrorResponse>(
      {
        error: 'Subscription Required',
        message: 'You must have a subscription to download this document.',
        statusCode: 401,
        timestamp: new Date().toISOString(),
      },
      { status: 401 },
    );
  }

  // Fetch document details from Supabase
  const data = await fetchDocumentDetails(documentId);
  if (data instanceof NextResponse) return data;

  const bucket = s3Config.bucket ?? 'uploads';
  const filePath = data?.edited_key ?? data.original_key;

  // Call the downloadFile function to get the file Blob
  const fileData = await downloadFile(bucket, filePath);
  if (fileData instanceof NextResponse) return fileData;

  // Set response headers
  const responseHeaders = {
    'Content-Type': fileData.type || 'application/octet-stream',
    'Content-Length': fileData.size?.toString() || '0',
    'Content-Disposition': `attachment; filename="${sanitizeFileName(data.name)}"`,
  };

  return new NextResponse(fileData.stream(), {
    status: 200,
    headers: responseHeaders,
  });
}

/**
 * Fetches document details from the Supabase database.
 *
 * @function fetchDocumentDetails
 * @param {string} documentId - The ID of the document to fetch.
 */
async function fetchDocumentDetails(documentId: string): Promise<Document | NextResponse<ErrorResponse>> {
  try {
    // Create a new Supabase client
    const supabase = getSupabaseServerClient();

    // Attempt to fetch document details from the Supabase database.
    const { data, error } = await supabase.from('documents').select('*').eq('id', documentId).single();

    // Check if there was an error during the fetch operation.
    if (error) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'Document Not Found',
          message: `The requested document does not exist or may have been deleted.`,
          statusCode: 404,
          timestamp: new Date().toISOString(),
        },
        { status: 404 },
      );
    }

    return data;
  } catch (error) {
    return NextResponse.json<ErrorResponse>(
      {
        error: 'Internal Server Error',
        message: `An unexpected error occurred while fetching document details.`,
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

/**
 * Downloads a file from a specified Supabase storage bucket.
 *
 * @function downloadFile
 * @param {string} bucket - The name of the storage bucket from which to download the file.
 * @param {string} filePath - The full path of the file within the bucket.
 * @returns {Promise<NextResponse | Blob>} - A promise that resolves to either the downloaded file as a Blob or an error response.
 */
async function downloadFile(bucket: string, filePath: string): Promise<NextResponse<ErrorResponse> | Blob> {
  try {
    // Create a new Supabase client
    const supabase = getSupabaseServerAdminClient();

    // Attempt to download the file from the specified bucket and file path.
    const { data, error } = await supabase.storage.from(bucket).download(filePath);

    // Check if there was an error during the download process.
    if (error || !data) {
      return NextResponse.json<ErrorResponse>(
        {
          error: 'File Not Found',
          message: `Unable to download the file. The file may not exist or was deleted.`,
          statusCode: 404,
          timestamp: new Date().toISOString(),
        },
        { status: 404 },
      );
    }

    // Return the downloaded file as a Blob.
    return data;
  } catch (error) {
    // Handle unexpected errors during the download process.
    return NextResponse.json<ErrorResponse>(
      {
        error: 'Internal Server Error',
        message: `An unexpected error occurred while downloading the file.`,
        statusCode: 500,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}
